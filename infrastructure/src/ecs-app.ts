import { ECSApplicationSpecs } from '@balsamiq/iac/lib/cli/ecs-app';
import { getEnvVar } from '@balsamiq/iac/lib/cli/tools';
import { cronFromExpression } from '@balsamiq/iac/lib/utils';
import { RDSCredentials, RDSCredentialsSchema } from '@balsamiq/serverconfig/lib/secrets';
import {
    BAS_SERVER_API_SECRET_SCHEMA,
    CLOUD_CONFIG_SCHEMA,
    DATA_RESIDENCIES,
    RTC_CONFIG_SECRET_SCHEMA,
} from './environment-variables-schemas';
import { CONFIG, ENVIRONMENTS, MYSQL_PORT, REDIS_PORT } from './utils';

const PRIVATE_NPM_AUTH_TOKEN = process.env.PRIVATE_NPM_AUTH_TOKEN;
if (!PRIVATE_NPM_AUTH_TOKEN) {
    throw new Error('Undefined environment variable PRIVATE_NPM_AUTH_TOKEN');
}

type CreateFunctionParams = Parameters<NonNullable<ECSApplicationSpecs['manualResources']>[number]['create']>;

export const APP_SPECS: ECSApplicationSpecs = {
    appName: 'bas',
    environments: ENVIRONMENTS,
    build: () => ({
        docker: {
            buildDir: '../', // Relative to the "infrastructure/" project directory
        },
        buildArgs: { PRIVATE_NPM_AUTH_TOKEN: getEnvVar('PRIVATE_NPM_AUTH_TOKEN') },
    }),
    iacContextFilepath: './iac-context.json',
    manualResources: [
        {
            name: 'mysql-subnet-group',
            async create(environment, tools) {
                await tools.createDBSubnetGroup(CONFIG[environment].manualResources.mysql.subnetIds);
            },
        },
        {
            name: 'mysql-security-group',
            async create(_environment, tools) {
                await tools.createMySQLSecurityGroup(MYSQL_PORT);
            },
        },
        {
            name: 'mysql-secret',
            async create(environment, tools) {
                await tools.createSecret(
                    `bas-${environment}-mysql`,
                    {
                        dbname: 'app',
                        engine: 'mysql',
                        host: '--TODO--',
                        password: tools.createStrongSecretString({ length: 20, specialCharacters: false }),
                        port: MYSQL_PORT,
                        username: 'admin',
                    },
                    RDSCredentialsSchema
                );
            },
        },
        {
            name: 'mysql',
            async create(environment, tools) {
                const secretValue = JSON.parse(await tools.readSecret(CONFIG[environment].manualResources.mysql.credentialsSecretArn));
                await tools.createMySQLCluster(secretValue, CONFIG[environment].manualResources.mysql.config);
            },
        },
        {
            name: 'redis-subnet-group',
            async create(environment, tools) {
                await tools.createCacheSubnetGroup(CONFIG[environment].manualResources.redis.subnetIds);
            },
        },
        {
            name: 'redis-security-group',
            async create(_environment, tools) {
                await tools.createRedisSecurityGroup(REDIS_PORT);
            },
        },
        {
            name: 'redis',
            async create(environment, tools) {
                await tools.createRedis(CONFIG[environment].manualResources.redis.config);
            },
        },
        {
            name: 'server-api-secret',
            async create(environment, tools) {
                await tools.createSecret(
                    `bas-${environment}-managedServerApiSecrets`,
                    {
                        data: {
                            cloud: [tools.createStrongSecretString({ length: 30 })],
                            gd: [tools.createStrongSecretString({ length: 30 })],
                            jira: [tools.createStrongSecretString({ length: 30 })],
                            confluence: [tools.createStrongSecretString({ length: 30 })],
                            bas: [tools.createStrongSecretString({ length: 30 })],
                        },
                        pollingIntervalInMin: 5,
                    },
                    BAS_SERVER_API_SECRET_SCHEMA
                );
            },
        },
        ...DATA_RESIDENCIES.flatMap((region) => ({
            name: `s3-bucket-${region}`,
            async create(environment: CreateFunctionParams[0], tools: CreateFunctionParams[1]) {
                await tools.createS3Bucket({
                    region: CONFIG[environment].manualResources.dataResidencies[region].region,
                    bucketName: CONFIG[environment].manualResources.dataResidencies[region].bucketName,
                    versioningEnabled: true,
                });
            },
        })),
    ],
    secretEnvVars: (environment) => ({
        BAS_SERVER_API_SECRET: {
            arn: CONFIG[environment].serverApiSecretsArn,
            schema: BAS_SERVER_API_SECRET_SCHEMA,
        },
        MYSQL_SECRET: {
            arn: CONFIG[environment].manualResources.mysql.credentialsSecretArn,
            schema: RDSCredentialsSchema,
        },
        RTC_SECRET: {
            arn: CONFIG[environment].rtc.secretArn,
            schema: RTC_CONFIG_SECRET_SCHEMA,
        },
        CLOUD_CONFIG_SECRET: {
            arn: CONFIG[environment].cloud.secretArn,
            schema: CLOUD_CONFIG_SCHEMA,
        },
    }),
    specs({ environment }) {
        const config = CONFIG[environment];

        const services: ReturnType<ECSApplicationSpecs['specs']>['services'] = [
            {
                name: 'server',
                command: [
                    'sh',
                    '-c',
                    'AWS_INSTANCE_ID=`curl -s http://169.254.169.254/latest/meta-data/instance-id` node --import=./register-ts-node.js monitor.ts runForever server.js',
                ],
                specs: {
                    type: 'network',
                    subdomains: [
                        {
                            subdomain: config.subdomain,
                            createDNSRecord: config.createDnsRecords,
                        },
                    ],
                    healthCheck: {
                        path: config.processes.server.healthCheck.path,
                        interval: config.processes.server.healthCheck.intervalSeconds,
                        grace: config.processes.server.healthCheck.graceSeconds,
                    },
                    port: 4000,
                    taskCount: config.processes.server.numberOfProcesses,
                    minHealthyPercent: 100,
                    maxHealthyPercent: 200,
                    placementStrategies: ['spreadAcrossInstances', 'packedByMemory'],
                },
                resources: {
                    cpu: config.processes.server.resources.cpu,
                    memoryLimitMiB: config.processes.server.resources.memoryHardLimitMiB,
                    sharedMemorySize: config.processes.server.resources.sharedMemorySizeMiB,
                },
            },
            {
                name: 'main-gardening',
                command: [
                    'sh',
                    '-c',
                    'AWS_INSTANCE_ID=`curl -s http://169.254.169.254/latest/meta-data/instance-id` node --import=./register-ts-node.js monitor.ts runOnce entrypoint_main_gardening.ts',
                ],
                specs: {
                    type: 'cron',
                    cron: cronFromExpression(config.processes.mainGardening.schedule),
                },
                resources: {
                    cpu: config.processes.mainGardening.resources.cpu,
                    memoryLimitMiB: config.processes.mainGardening.resources.memoryHardLimitMiB,
                    sharedMemorySize: config.processes.mainGardening.resources.sharedMemorySizeMiB,
                },
            },
            {
                name: 'cloud-gardening',
                command: [
                    'sh',
                    '-c',
                    'AWS_INSTANCE_ID=`curl -s http://169.254.169.254/latest/meta-data/instance-id` node --import=./register-ts-node.js monitor.ts runOnce entrypoint_cloud_gardening.ts',
                ],
                specs: {
                    type: 'cron',
                    cron: cronFromExpression(config.processes.cloudGardening.schedule),
                },
                resources: {
                    cpu: config.processes.cloudGardening.resources.cpu,
                    memoryLimitMiB: config.processes.cloudGardening.resources.memoryHardLimitMiB,
                    sharedMemorySize: config.processes.cloudGardening.resources.sharedMemorySizeMiB,
                },
            },
            {
                name: 'apps-watcher',
                command: [
                    'sh',
                    '-c',
                    'AWS_INSTANCE_ID=`curl -s http://169.254.169.254/latest/meta-data/instance-id` node --import=./register-ts-node.js entrypoint_apps_watcher.ts',
                ],
                specs: {
                    type: 'simple',
                    taskCount: 1,
                },
                resources: {
                    cpu: config.processes.appsWatcher.resources.cpu,
                    memoryLimitMiB: config.processes.appsWatcher.resources.memoryHardLimitMiB,
                    sharedMemorySize: config.processes.appsWatcher.resources.sharedMemorySizeMiB,
                },
            },
        ];

        return {
            vpc: config.vpc,
            services,
            networkServicesALBFacing: 'public',
            sharedVolumes: [],
        };
    },
    vanta: {
        isProduction: (environment) => CONFIG[environment].inspectWithVanta,
    },
};
