import { BALSAMIQ_VPCS } from '@balsamiq/serverconfig/lib/balsamiq-vpcs';
import { Configuration } from '../utils';

export const CONFIGURATION = {
    vpc: BALSAMIQ_VPCS.internal,
    manualResources: {
        mysql: {
            subnetIds: BALSAMIQ_VPCS.internal.subnets.private,
            config: {
                instanceClass: 'db.t4g.medium',
                optionsGroup: 'default:mysql-8-0',
                engineVersion: '8.0.41',
                backupWindow: '04:06-04:36',
                maintenanceWindow: 'sat:05:27-sat:05:57',
                subnetGroupName: 'bas-ondeck-mysql-subnetgroup',
                securityGroupIdPrefix: 'bas-ondeck-mysql-securityGroup',
                caCertificateIdentifier: 'rds-ca-ecc384-g1',
                storageInGB: 20,
                storageType: 'gp3',
                multiAZCluster: false,
            },
            credentialsSecretArn: 'arn:aws:secretsmanager:eu-west-1:717726050199:secret:bas-ondeck-mysql-N3Zql5',
        },
        redis: {
            subnetIds: BALSAMIQ_VPCS.internal.subnets.private,
            config: {
                multiAZ: false,
                backupsEnabled: false,
                maintenanceWindowUTC: 'wed:04:00-wed:05:00',
                nodeClass: 'cache.t4g.micro',
                numNodes: 1,
                subnetGroupNamePrefix: 'bas-ondeck-redis-redissubnetgroup',
                securityGroupIdPrefix: 'bas-ondeck-redis-sec-group',
            },
        },
        dataResidencies: {
            us: {
                region: 'us-east-1',
                bucketName: 'com-balsamiqstaff-bas20-ondeck-us',
                subdomain: 'share-ondeck-us',
                zone: BALSAMIQ_VPCS.internal.availableZones.balsamiqstaff_com,
            },
            eu: {
                region: 'eu-west-1',
                bucketName: 'com-balsamiqstaff-bas20-ondeck',
                subdomain: 'share-ondeck',
                zone: BALSAMIQ_VPCS.internal.availableZones.balsamiqstaff_com,
            },
        },
    },

    subdomain: `bas20-ondeck`,
    reduceLogging: false,
    metricsRegion: 'eu-west-1',
    createDnsRecords: true,
    defaultDataResidency: 'eu',
    kms: {
        environment: 'internal-ondeck',
    },
    mysql: {
        region: 'eu-west-1',
        endpoint: 'bas-ondeck.cfl8emb144hi.eu-west-1.rds.amazonaws.com',
    },
    redis: {
        endpoint: 'bas-ondeck.epbfco.ng.0001.euw1.cache.amazonaws.com',
    },
    proxyConfig: [
        {
            prefix: '/bw-trello/',
            host: `https://s3.eu-west-1.amazonaws.com`,
            path: `/com-balsamiqstaff-bas20-ondeck-bw-trello/`,
        },
        {
            prefix: '/bw-atlassian/',
            host: `https://s3.eu-west-1.amazonaws.com`,
            path: `/com-balsamiqstaff-bas20-ondeck-bw-atlassian/`,
        },
        {
            prefix: '/media/',
            host: 'https://s3.amazonaws.com',
            path: '/editor-ondeck.balsamiq.com/',
        },
        {
            prefix: '/editor/',
            host: 'https://s3.amazonaws.com',
            path: '/editor-ondeck.balsamiq.com/',
        },
    ],
    confluenceNamespace: 'com.balsamiq.mockups.confluence.ondeck',
    jiraNamespace: 'com.balsamiq.mockups.jira.ondeck',
    serverApiSecretsArn: 'arn:aws:secretsmanager:eu-west-1:717726050199:secret:bas-ondeck-managedServerApiSecrets-SeQY4C',
    rtc: {
        secretArn: 'arn:aws:secretsmanager:eu-west-1:717726050199:secret:rtc-ondeck-managedConfig-fmBfgX',
        websocketsUri: 'rtcws-ondeck.balsamiqstaff.com',
    },
    i2w: {
        serviceUrl: 'https://api.balsamiqstaff.com/i2w-ondeck/',
    },
    w2i: {
        region: 'eu-west-1',
        lambdaFunctionArn: 'arn:aws:lambda:eu-west-1:717726050199:function:w2i-ondeck-ExportFunction-vdIRXw9EKc5w',
    },
    cloud: {
        baseUrl: 'https://cloud-ondeck.balsamiqstaff.com',
        secretArn: 'arn:aws:secretsmanager:eu-west-1:717726050199:secret:cloud-ondeck-secrets-c3sCpk',
        projectsMaxAgeInDays: 30,
        timeDeltaForSavingLiveProjectsInMinutes: 30,
    },
    logRetentionDays: 14,
    processes: {
        server: {
            numberOfProcesses: 1,
            healthCheck: {
                path: '/health?db=true',
                intervalSeconds: 30,
                graceSeconds: 60,
            },
            resources: {
                cpu: 512,
                memoryHardLimitMiB: 4600, // hard limit, and anything that hits it gets destroyed
                sharedMemorySizeMiB: 1024, // size of the /dev/shm volume
            },
        },
        appsWatcher: {
            resources: {
                cpu: 256,
                memoryHardLimitMiB: 512, // hard limit, and anything that hits it gets destroyed
                sharedMemorySizeMiB: 1024, // size of the /dev/shm volume
            },
        },
        mainGardening: {
            resources: {
                cpu: 256,
                memoryHardLimitMiB: 1024, // hard limit, and anything that hits it gets destroyed
                sharedMemorySizeMiB: 1024, // size of the /dev/shm volume
            },
            schedule: '3/5 * * * ? *', // every 5 minutes
        },
        cloudGardening: {
            resources: {
                cpu: 256,
                memoryHardLimitMiB: 1024, // hard limit, and anything that hits it gets destroyed
                sharedMemorySizeMiB: 1024, // size of the /dev/shm volume
            },
            schedule: '3/10 * * * ? *', // every 10 minutes
        },
    },
    slackNotifications: {
        alarms: {
            emergency: ['bot_apps_dev'],
            caution: ['bot_apps_dev'],
            informative: ['bot_apps_dev'],
        },
    },
    inspectWithVanta: false,
} satisfies Configuration;
