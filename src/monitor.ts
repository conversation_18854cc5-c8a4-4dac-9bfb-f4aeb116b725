import { buildUID } from '@balsamiq/saas-utils';
import { spawn, ChildProcess } from 'child_process';
import { hostname } from 'os';

const [_, monitorFn, mode, childProgram, ...childArgs] = process.argv;

const restartDelay = 1000;
const monitorRunID = buildUID('mon');
const HOSTNAME = hostname();
const AWS_INSTANCE_ID = process.env['AWS_INSTANCE_ID'];

// Log message to stdout in JSON format.
function log(message: string, severity: 'ERROR' | 'INFO' = 'INFO', error?: Error) {
    console.log(
        JSON.stringify({
            name: 'bas',
            hostname: HOSTNAME,
            module: 'monitor',
            monitorRunID,
            ec2id: AWS_INSTANCE_ID,
            '@timestamp': new Date().toISOString(),
            severity: error ? 'ERROR' : 'INFO',

            message: message + (error ? `: ${error!.message}` : ''),
            stack: error ? error.stack : undefined,
        })
    );
}

if (!mode || !childProgram) {
    log(`Usage: ts-node ${monitorFn} (runOnce|runForever) child_program [...child_program_args]`, 'ERROR');
    process.exit(1);
}

if (mode !== 'runOnce' && mode !== 'runForever') {
    log('Error: The first argument must be either "runOnce" or "runForever".', 'ERROR');
    process.exit(1);
}

let childProcess: ChildProcess;

function startChildProcess() {
    log(`Starting child process: ${childProgram} ${childArgs.join(' ')}`);

    childProcess = spawn(`node`, [childProgram, ...childArgs], {
        stdio: 'inherit',
        env: {
            ...process.env,
            MONITOR_RUN_ID: monitorRunID,
            NODE_OPTIONS: '--import=./register-ts-node.js --max-old-space-size=4096',
        },
    });

    childProcess.on('exit', (code, signal) => {
        if (signal) {
            log(`Child process was killed by signal: ${signal}`);
        } else if (code !== null) {
            log(`Child process exited with code ${code}`);
        }

        if (mode === 'runForever') {
            log('Restarting child process...');
            setTimeout(startChildProcess, restartDelay);
        } else {
            log('Child process exited. Monitor will now exit.');
            process.exit(code !== null ? code : 0);
        }
    });

    childProcess.on('error', (err) => {
        log('Failed to start child process:', 'ERROR', err);
        if (mode === 'runForever') {
            log('Retrying to start child process in 1 second...');
            setTimeout(startChildProcess, restartDelay);
        } else {
            process.exit(1);
        }
    });
}

// Handle termination signals to clean up child processes
function handleExit(signal: NodeJS.Signals) {
    log(`Received ${signal}. Exiting...`);
    if (childProcess) {
        childProcess.kill('SIGTERM');
    }
    process.exit(0);
}

process.on('SIGINT', handleExit);
process.on('SIGTERM', handleExit);
process.on('unhandledRejection', function (reason) {
    log('Unhandled promise rejection. Shutting down process.' + reason, 'ERROR');
    process.exit(1);
});

// Start the child process
startChildProcess();
