import * as jwt from './connectors/lib/jwt.js';
import atlassianJwt from 'atlassian-jwt';
import superagent from 'superagent';
import type { RedisAdapter } from './redisAdapter.ts';
import type { Logger } from '@balsamiq/logging';
import type { Config } from './configLoader.ts';
import type { Clock } from './clock.ts';
import type { DBConnector } from './database.ts';
import { callWithLegacyCallback } from './calling-style.ts';
import { z } from 'zod';
import assert from 'assert';
import url from 'url';
import type { HttpResponse } from './utils.ts';

// Utilities
function Ok<V, K extends string>(value: V, kind: K): { ok: true; value: V; kind: K };
function Ok<V>(value: V): { ok: true; value: V };
function Ok<V, K extends string>(value: V, kind?: K): { ok: true; value: V; kind?: K } {
    if (arguments.length === 1) {
        return { ok: true, value } as const;
    } else {
        return { ok: true, value, kind } as const;
    }
}
type OkType<V, K = never> = K extends string ? ReturnType<typeof Ok<V, K>> : ReturnType<typeof Ok<V>>;

function Fail<R extends string, E>(reason: R, extra: E): { ok: false; reason: R; extra: E };
function Fail<R extends string>(reason: R): { ok: false; reason: R };
function Fail<R extends string, E>(reason: R, extra?: E): { ok: false; reason: R; extra?: E } {
    if (arguments.length === 1) {
        return { ok: false, reason } as const;
    } else {
        return { ok: false, reason, extra } as const;
    }
}
type FailType<R extends string, E = never> = E extends never ? ReturnType<typeof Fail<R>> : ReturnType<typeof Fail<R, E>>;

type OkFailType<V, R extends string> = OkType<V> | FailType<R>;

export interface ConfluenceApiClient {
    getUserDetailsAsApp(urlWithJWT: string, jwt: string): Promise<HttpResponse>;
    getUserDetailsAsUser(baseUrl: string, userId: string, token: string): Promise<HttpResponse>;
    fetchOAuth2Token(url: string, parameters: Record<string, string>): Promise<HttpResponse>;
    getPageOperation(baseUrl: string, pageId: string, token: string): Promise<HttpResponse>;
    getBlogpostOperation(baseUrl: string, blogpostId: string, token: string): Promise<HttpResponse>;
    getPageAttachments(baseUrl: string, pageId: string, token: string): Promise<HttpResponse>;
    getBlogpostAttachments(baseUrl: string, blogpostId: string, token: string): Promise<HttpResponse>;
    convertIdsToTypes(baseUrl: string, contentIds: string[], token: string): Promise<HttpResponse>;
    getAttachment(baseUrl: string, attachmentId: string, queryString: string, token: string): Promise<HttpResponse>;
    download(url: string, binary: boolean, token: string): Promise<HttpResponse>;
    getPublicKey(kid: string): Promise<HttpResponse>;
}

/**
 * This class takes care of everything concerning the HTTP requests to the Atlassian API,
 * like rate-limiting, retries, etc.
 */
export class HttpConfluenceApiClient implements ConfluenceApiClient {
    private timeout: number = 15000;

    async getUserDetailsAsApp(urlWithJWT: string, jwt: string) {
        const response = await superagent
            .get(urlWithJWT)
            .ok((_res) => true)
            .set('Authorization', `JWT ${jwt}`)
            .set('Accept', 'application/json')
            .timeout(this.timeout);

        return response;
    }

    private getAsUser(url: string, token: string) {
        return superagent
            .get(url)
            .set('Authorization', `Bearer ${token}`)
            .set('Accept', 'application/json')
            .set('X-Atlassian-Token', 'no-check')
            .timeout(this.timeout);
    }

    private postAsUser(url: string, token: string, body: object) {
        return superagent
            .post(url)
            .set('Authorization', `Bearer ${token}`)
            .set('Accept', 'application/json')
            .set('Content-Type', 'application/json')
            .timeout(this.timeout)
            .send(body);
    }

    async getUserDetailsAsUser(baseUrl: string, userId: string, token: string) {
        return await this.getAsUser(`${baseUrl}/rest/api/user?privacyMode=true&accountId=${userId}`, token);
    }

    async getPageOperation(baseUrl: string, pageId: string, token: string) {
        return await this.getAsUser(`${baseUrl}/api/v2/pages/${pageId}/operations`, token);
    }

    async getBlogpostOperation(baseUrl: string, blogpostId: string, token: string) {
        return await this.getAsUser(`${baseUrl}/api/v2/blogposts/${blogpostId}/operations`, token);
    }

    async getPageAttachments(baseUrl: string, pageId: string, token: string) {
        return await this.getAsUser(`${baseUrl}/rest/api/content/${pageId}/attachments`, token);
    }

    async getBlogpostAttachments(baseUrl: string, blogpostId: string, token: string) {
        return await this.getAsUser(`${baseUrl}/rest/api/content/${blogpostId}/attachments`, token);
    }

    async convertIdsToTypes(baseUrl: string, contentIds: string[], token: string) {
        return await this.postAsUser(`${baseUrl}/api/v2/content/convert-ids-to-types`, token, { contentIds });
    }

    async getAttachment(baseUrl: string, attachmentId: string, queryString: string, token: string) {
        return await this.getAsUser(`${baseUrl}/api/v2/attachments/${attachmentId}/` + (queryString ? '?' + queryString : ''), token);
    }

    async download(url: string, binary: boolean, token: string) {
        let req = superagent.get(url).set('Authorization', `Bearer ${token}`).set('X-Atlassian-Token', 'no-check').timeout(this.timeout);
        if (binary) {
            req = req.responseType('arraybuffer');
        }
        return await req;
    }

    async getPublicKey(kid: string) {
        return await superagent.get(`https://connect-install-keys.atlassian.com/${kid}`);
    }

    async fetchOAuth2Token(url: string, parameters: Record<string, string>) {
        const response = await superagent
            .post(url)
            .ok((_res) => true)
            .set({ Accept: 'application/json' })
            .timeout(this.timeout)
            .type('form')
            .send(parameters);
        return response;
    }
}

const ANON_AVATAR_URL = 'https://bas20.balsamiq.com/bw-atlassian/pict/anonymous.png';
const AUTHORIZATION_SERVER_URL = 'https://oauth-2-authorization-server.services.atlassian.com';
const EXPIRY_SECONDS = 60;
const GRANT_TYPE = 'urn:ietf:params:oauth:grant-type:jwt-bearer';

// When using the Atlassian API, we need to create a JWT token for each request.
// This is done using the createJWTForRequest function.
// The JWT token is created using the shared secret and the request parameters.
interface ConfluenceApiRequestParams {
    method: string;
    path: string;
    query: Record<string, string>;
}

/**
 * This class is responsible for handling the Atlassian API, including token management and JWT creation.
 * Uses an AtlassianApiClient to fetch data from the API. The client is mocked in tests.
 */
export class ConfluenceAdapter {
    private tokenCache: AtlassianTokenCache;

    private logger: Logger;
    private config: Config;
    private clock: Clock;
    private apiClient: ConfluenceApiClient;

    constructor({
        redisAdapter,
        logger,
        config,
        clock,
        apiClient,
    }: {
        redisAdapter: RedisAdapter;
        logger: Logger;
        config: Config;
        clock: Clock;
        apiClient: ConfluenceApiClient;
    }) {
        this.tokenCache = new AtlassianTokenCache(redisAdapter, logger, config);
        this.clock = clock;
        this.config = config;
        this.logger = logger.getLogger({ subsystem: 'ConfluenceAdapter' });
        this.apiClient = apiClient;
    }

    // --- Incoming requests helpers ---

    async verifyJWTSignedFromAtlassian(jwtToken: string, expectedAudience: string, expectedIssuer: string) {
        const kid = atlassianJwt.getKeyId(jwtToken);
        const alg = atlassianJwt.getAlgorithm(jwtToken);

        const publicKeyResponse = await this.apiClient.getPublicKey(kid);
        if (publicKeyResponse.status !== 200) {
            this.logger.info(`Error fetching public key`, { status: publicKeyResponse.status, text: publicKeyResponse.text });
            throw new Error(`ConfluenceAdapter: Error fetching public key: ${publicKeyResponse.status} ${publicKeyResponse.text}`);
        }
        if (!publicKeyResponse.text) {
            throw new Error('ConfluenceAdapter: Error fetching public key: no text');
        }
        const jwtPayload = atlassianJwt.decodeAsymmetric(jwtToken, publicKeyResponse.text, alg, false);
        if (jwtPayload.aud.includes(expectedAudience) && jwtPayload.iss === expectedIssuer) {
            return jwtPayload;
        } else {
            this.logger.info(`JWT payload does not match expected audience or issuer`, { jwtPayload });
            throw new Error('ConfluenceAdapter: JWT payload does not match expected audience or issuer');
        }
    }

    async getInstallationDataFromDB(dbConnector: DBConnector, connectorId: string, issuer: string) {
        const installationData: Record<string, unknown> = await callWithLegacyCallback((cb) => {
            dbConnector.getConnectorData(connectorId, issuer, cb);
        });

        if (Object.keys(installationData).length === 0) {
            return null;
        }

        assertAtlassianInstallationData(installationData);

        return installationData;
    }

    public async verifyJWTAndGetInstallationData(dbConnector: DBConnector, connectorId: string, jwtToken: string) {
        const decodedAuthToken = jwt.decode(jwtToken, '', true);
        assertJWTPayload(decodedAuthToken);

        const installationData = await this.getInstallationDataFromDB(dbConnector, connectorId, decodedAuthToken.iss);
        if (installationData === null) {
            return Fail('not found');
        }

        assertAtlassianInstallationData(installationData);

        try {
            jwt.decode(jwtToken, installationData.sharedSecret, false);
        } catch (e) {
            const error = e as Error;
            this.logger.securityWarningIgnore('Signature failed:', 'ATLASSIAN_SIGNATURE_VERIFICATION_FAILED', error);
            return Fail('signature failed');
        }

        let userId: string;
        const context = decodedAuthToken.context;
        if (context && context.user && context.user.userId) {
            userId = context.user.userId;
        } else {
            assert(decodedAuthToken.sub && typeof decodedAuthToken.sub === 'string', 'Invalid JWT: sub claim is missing or not a string');
            userId = decodedAuthToken.sub;
        }

        return Ok({ installationData, decodedAuthToken, userId }, 'success');
    }

    // --- Get access token ---
    public createAccessTokenAssertion(installationData: AtlassianInstallationData, sub: string): string {
        const now = Math.floor(Date.now() / 1000);
        const exp = now + EXPIRY_SECONDS;

        const jwtClaims = {
            iss: `urn:atlassian:connect:clientid:${installationData.oauthClientId}`,
            tnt: installationData.baseUrl,
            aud: AUTHORIZATION_SERVER_URL,
            iat: now,
            exp: exp,
            sub: sub,
        };

        return jwt.encode(jwtClaims, installationData.sharedSecret, 'HS256');
    }

    public async getAccessToken(
        installationData: AtlassianInstallationData,
        userId: string | undefined,
        userAccountId: string | undefined,
        scopes: string,
        useCache: boolean = true
    ): Promise<{
        accessToken: string;
        status: string;
        userId?: string;
        userAccountId?: string;
    }> {
        let userUniqueId: string;
        let sub: string;

        if (userAccountId) {
            sub = `urn:atlassian:connect:useraccountid:${userAccountId}`;
            userUniqueId = userAccountId;
        } else if (userId) {
            sub = `urn:atlassian:connect:userkey:${userId}`;
            userUniqueId = userId;
        } else {
            throw new Error('Either userId or userAccountId must be provided');
        }

        if (useCache) {
            const cachedToken = await this.tokenCache.getToken(userUniqueId);
            if (cachedToken) {
                return {
                    accessToken: cachedToken.access_token,
                    status: 'cached',
                    userId,
                    userAccountId,
                };
            }
        }

        const assertion = this.createAccessTokenAssertion(installationData, sub);
        const parameters = {
            grant_type: GRANT_TYPE,
            assertion: assertion,
            scope: scopes,
        };

        const tokenResponse = await this.apiClient.fetchOAuth2Token(`${AUTHORIZATION_SERVER_URL}/oauth2/token`, parameters);
        if (tokenResponse.status !== 200) {
            this.logger.info(`Error fetching access token`, { status: tokenResponse.status, text: tokenResponse.text });
            throw new Error(`ConfluenceAdapter: Error fetching access token: ${tokenResponse.status} ${tokenResponse.text}`);
        }
        const token = tokenResponse.body;

        assertAtlassianAccessToken(token);

        // Store in cache
        const now = Math.floor(Date.now() / 1000);
        token.expires_at = now + token.expires_in;
        this.tokenCache.storeToken(userUniqueId, token);

        const result = {
            accessToken: token.access_token,
            status: 'new',
            userId,
            userAccountId,
        };

        return result;
    }

    // --- Outgoing requests helpers ---
    public async createJWTForRequest(request: ConfluenceApiRequestParams, installationData: AtlassianInstallationData, userId: string) {
        const now = this.clock.now();
        const unixNow = Math.floor(now.valueOf() / 1000);
        const jwtTokenValidityInMinutes = 5;
        const clientKey = installationData.clientKey;
        const iss = installationData.key || this.config.confluenceNamespace;

        const payload: JWTPayload = {
            iss: iss,
            iat: unixNow,
            exp: unixNow + jwtTokenValidityInMinutes * 60,
            qsh: jwt.createQueryStringHash(request),
            aud: [clientKey],
        };

        if (userId) {
            payload.sub = userId;
        }

        return jwt.encode(payload, installationData.sharedSecret, 'HS256');
    }

    public async getEndpointAndJWT(installationData: AtlassianInstallationData, userId: string, request: ConfluenceApiRequestParams) {
        const queryString = jwt.canonicalizeQueryString(request.query);
        const requestJwt = await this.createJWTForRequest(request, installationData, userId);
        return {
            url: installationData.baseUrl + request.path + (queryString ? '?' + queryString : ''),
            requestJwt,
            urlWithToken: installationData.baseUrl + request.path + (queryString ? '?' + queryString + '&' : '?') + 'jwt=' + requestJwt,
        };
    }

    private checkDeprecationHeaders(response: HttpResponse) {
        if (response.headers && response?.headers?.warning && response?.headers?.deprecation && response?.headers?.link) {
            this.logger.securityWarning(
                `API deprecated: warning - ${response.headers.warning} deprecation - ${response.headers.deprecation} link - ${response.headers.link}`,
                'ATLASSIAN_ADAPTER_API_DEPRECATED'
            );
        }
    }

    // --- User details ---
    public async getUserDetailsAsApp(installationData: AtlassianInstallationData, userId: string) {
        const endpoint = await this.getEndpointAndJWT(installationData, userId, {
            method: 'GET',
            path: '/rest/api/user',
            query: { accountId: userId },
        });

        const userDetailsResponse = await this.apiClient.getUserDetailsAsApp(endpoint.urlWithToken, endpoint.requestJwt);
        this.checkDeprecationHeaders(userDetailsResponse);
        return this.createUserDetailsResultFromResponse(userDetailsResponse, installationData);
    }

    public async getUserDetailsAsUser(installationData: AtlassianInstallationData, userId: string, oauth2Token: string) {
        const userDetailsResponse = await this.apiClient.getUserDetailsAsUser(installationData.baseUrl, userId, oauth2Token);
        this.checkDeprecationHeaders(userDetailsResponse);
        return this.createUserDetailsResultFromResponse(userDetailsResponse, installationData);
    }

    private createUserDetailsResultFromResponse(userDetailsResponse: HttpResponse, installationData: AtlassianInstallationData) {
        if (userDetailsResponse.status === 401 || userDetailsResponse.status === 403) {
            return Fail('unauthorized');
        } else if (userDetailsResponse.status !== 200) {
            throw new Error('ConfluenceAdapter: Error fetching user details: ' + userDetailsResponse.status);
        }

        const user = userDetailsResponse.body;
        assertAtlassianUserDetail(user);

        const parsedUrl = url.parse(installationData.baseUrl);
        const hostname = url.format({
            protocol: parsedUrl.protocol,
            host: parsedUrl.host,
        });
        const avatarUrl = user.profilePicture && user.profilePicture.path ? hostname + user.profilePicture.path : ANON_AVATAR_URL;

        const result = {
            avatarUrl: avatarUrl,
            fullName: user.displayName,
            id: user.accountId,
            userName: user.accountId,
            email: user.email,
        };

        return Ok(result);
    }

    public async convertIdsToTypes(installationData: AtlassianInstallationData, contentIds: string[], token: string) {
        const response = await this.apiClient.convertIdsToTypes(installationData.baseUrl, contentIds, token);
        this.checkDeprecationHeaders(response);
        if (response.status !== 200) {
            this.logger.info(`Error converting IDs to types`, { status: response.status, text: response.text });
            throw new Error(`ConfluenceAdapter: Error converting IDs to types: ${response.status} ${response.text}`);
        }
        assertAtlassianConvertIdsToTypes(response.body);
        return response.body;
    }

    public async getPageOperation(installationData: AtlassianInstallationData, pageId: string, token: string) {
        const response = await this.apiClient.getPageOperation(installationData.baseUrl, pageId, token);
        this.checkDeprecationHeaders(response);
        if (response.status !== 200) {
            this.logger.info(`Error fetching page operation`, { status: response.status, text: response.text });
            throw new Error(`ConfluenceAdapter: Error fetching page operation: ${response.status} ${response.text}`);
        }

        assertAtlassianAllowedOperations(response.body);
        return response.body;
    }

    public async getBlogpostOperation(installationData: AtlassianInstallationData, blogpostId: string, token: string) {
        const response = await this.apiClient.getBlogpostOperation(installationData.baseUrl, blogpostId, token);
        this.checkDeprecationHeaders(response);
        if (response.status !== 200) {
            this.logger.info(`Error fetching blogpost operation`, { status: response.status, text: response.text });
            throw new Error(`ConfluenceAdapter: Error fetching blogpost operation: ${response.status} ${response.text}`);
        }

        assertAtlassianAllowedOperations(response.body);
        return response.body;
    }

    public async getAttachment(
        installationData: AtlassianInstallationData,
        attachmentId: string,
        queryObject: Record<string, string>,
        token: string
    ) {
        const queryString = jwt.canonicalizeQueryString(queryObject);
        const response = await this.apiClient.getAttachment(installationData.baseUrl, attachmentId, queryString, token);
        this.checkDeprecationHeaders(response);
        if (response.status !== 200) {
            this.logger.info(`Error fetching attachments`, { status: response.status, text: response.text });
            throw new Error(`ConfluenceAdapter: Error fetching attachments: ${response.status} ${response.text}`);
        }

        assertAtlassianAttachment(response.body);
        return response.body;
    }

    public async download(url: string, binary: boolean, token: string) {
        const response = await this.apiClient.download(url, binary, token);
        this.checkDeprecationHeaders(response);
        if (response.status !== 200) {
            this.logger.info(`Error downloading`, { status: response.status, text: response.text });
            throw new Error(`ConfluenceAdapter: Error downloading: ${response.status} ${response.text}`);
        }
        return response.body; // response.body is an ArrayBuffer if binary is true
    }
}

/**
 * This is a simple cache for Atlassian tokens. It uses Redis to store the tokens with a TTL.
 */
class AtlassianTokenCache {
    private redisAdapter: RedisAdapter;
    private logger: Logger;
    private readonly KEY_PREFIX = 'atlassian:token:';

    constructor(redisAdapter: RedisAdapter, logger: Logger, config: Config) {
        this.redisAdapter = redisAdapter;
        this.logger = logger.getLogger({ subsystem: 'atlassianToken' });
    }

    private getTokenKey(userUniqueId: string): string {
        return `${this.KEY_PREFIX}${userUniqueId}`;
    }

    public async getToken(userUniqueId: string): Promise<AtlassianAccessToken | null> {
        const key = this.getTokenKey(userUniqueId);

        try {
            const tokenJson = await this.redisAdapter.get(key);

            if (!tokenJson) {
                return null;
            }

            return JSON.parse(tokenJson) as AtlassianAccessToken;
        } catch (error) {
            this.logger.error(`Error getting token from Redis: ${error}`, error as Error, { userUniqueId });
            return null;
        }
    }

    public async storeToken(userUniqueId: string, tokenResponse: AtlassianAccessToken): Promise<void> {
        const key = this.getTokenKey(userUniqueId);
        const expirySeconds = tokenResponse.expires_in - 60; // 1 minute safety margin

        // Set expiry time if not already set
        if (!tokenResponse.expires_at) {
            tokenResponse.expires_at = Math.floor(Date.now() / 1000) + tokenResponse.expires_in;
        }

        try {
            await this.redisAdapter.setex(key, expirySeconds, JSON.stringify(tokenResponse));
        } catch (error) {
            this.logger.error(`Error storing token in Redis: ${error}`, error as Error, { userUniqueId });
        }
    }
}

// --- Private Schemas and Types ---
// The following schemas are used to validate the data structures used in the Atlassian API Adapter.

const AtlassianInstallationDataSchema = z
    .object({
        oauthClientId: z.string(),
        baseUrl: z.string(),
        sharedSecret: z.string(),
        clientKey: z.string(),
        key: z.string(),
    })
    .passthrough();

export type AtlassianInstallationData = z.infer<typeof AtlassianInstallationDataSchema>;

function assertAtlassianInstallationData(obj: any): asserts obj is AtlassianInstallationData {
    const result = AtlassianInstallationDataSchema.safeParse(obj);
    assert(result.success, 'AtlassianInstallationData validation failed: ' + JSON.stringify(result.error));
}

const AtlassianAccessTokenSchema = z
    .object({
        access_token: z.string(),
        token_type: z.string().optional(),
        expires_in: z.number(),
        expires_at: z.number().optional(),
    })
    .passthrough();

export type AtlassianAccessToken = z.infer<typeof AtlassianAccessTokenSchema>;

function assertAtlassianAccessToken(obj: any): asserts obj is AtlassianAccessToken {
    const result = AtlassianAccessTokenSchema.safeParse(obj);
    assert(result.success, 'AccessTokenResponse validation failed: ' + JSON.stringify(result.error));
}

const JWTPayloadSchema = z.object({
    iss: z.string(),
    iat: z.number(),
    exp: z.number(),
    qsh: z.string().optional(),
    sub: z.string().optional(),
    aud: z.union([z.array(z.string()), z.string()]).optional(),
    context: z
        .object({
            user: z
                .object({
                    userId: z.string().optional(),
                })
                .optional(),
        })
        .optional(),
});

type JWTPayload = z.infer<typeof JWTPayloadSchema>;

function assertJWTPayload(obj: any): asserts obj is JWTPayload {
    const result = JWTPayloadSchema.safeParse(obj);
    assert(result.success, 'JWTPayload validation failed: ' + JSON.stringify(result.error));
}

const AtlassianUserSchema = z
    .object({
        accountId: z.string(),
        displayName: z.string(),
        email: z.string().optional(),
        profilePicture: z
            .object({
                path: z.string().optional(),
            })
            .passthrough()
            .optional(),
    })
    .passthrough();

export type AtlassianUserDetail = z.infer<typeof AtlassianUserSchema>;
function assertAtlassianUserDetail(obj: any): asserts obj is AtlassianUserDetail {
    const result = AtlassianUserSchema.safeParse(obj);
    assert(result.success, 'AtlassianUser validation failed: ' + JSON.stringify(result.error));
}

const AtlassianIdTypeResponseSchema = z
    .object({
        results: z.record(z.string(), z.string()),
    })
    .passthrough();
export type AtlassianConvertIdsToTypes = z.infer<typeof AtlassianIdTypeResponseSchema>;
function assertAtlassianConvertIdsToTypes(obj: any): asserts obj is AtlassianConvertIdsToTypes {
    const result = AtlassianIdTypeResponseSchema.safeParse(obj);
    assert(result.success, 'AtlassianConvertIdsToTypesResponse validation failed: ' + JSON.stringify(result.error));
}

const AtlassianAllowedOperationsResponseSchema = z
    .object({
        operations: z.array(
            z
                .object({
                    operation: z.string(),
                    targetType: z.string(),
                })
                .passthrough()
        ),
    })
    .passthrough();
export type AtlassianAllowedOperations = z.infer<typeof AtlassianAllowedOperationsResponseSchema>;
function assertAtlassianAllowedOperations(obj: any): asserts obj is AtlassianAllowedOperations {
    const result = AtlassianAllowedOperationsResponseSchema.safeParse(obj);
    assert(result.success, 'AtlassianAllowedOperationsResponse validation failed: ' + JSON.stringify(result.error));
}

const AtlassianAttachmentResponseSchema = z
    .object({
        downloadLink: z.string(),
    })
    .passthrough();
export type AtlassianAttachment = z.infer<typeof AtlassianAttachmentResponseSchema>;
function assertAtlassianAttachment(obj: any): asserts obj is AtlassianAttachment {
    const result = AtlassianAttachmentResponseSchema.safeParse(obj);
    assert(result.success, 'AtlassianAttachmentResponse validation failed: ' + JSON.stringify(result.error));
}
