import type { MySQLDriverPool } from './mysql-driver.ts';
import type { Logger } from '@balsamiq/logging';
import type { Config } from './configLoader.ts';
import type { RedisAdapter } from './redisAdapter.ts';
import { DB_IDENTIFIER_REGEXP } from './mysql-driver.ts';
import type { SessionData } from './session-manager.ts';
import type mysql from 'mysql';
import {
    isBASLegacyErrorObject,
    type BASLegacyCallback,
    callWithLegacyCallback,
    type BASLegacyErrorObject,
    callSaneFunctionFromLegacy,
} from './calling-style.ts';
import Consts from '@balsamiq/bmpr/lib/BalsamiqArchiveConstants.js';
import * as uuid from 'uuid';
import type { Clock } from './clock.ts';
import { watchdog, type WatchdogContentData } from './utils.ts';
import type { PermalinkData, PlatformData } from './model.ts';

export interface User {
    TOKEN: string;
    USERNAME: string | null;
    USERINFO: string | null;
    INTERNAL_ID: string | null;
    ARCHIVE_ID: string;
    STARTING_TIME: number;
    PERMISSIONS: string;
    PLATFORM_TOKEN: string;
    TIMESTAMP: number;
}

interface PermalinkInfoDBRow {
    PERMALINK_ID: string;
    PLATFORM_SITE_ID: string | null;
    PLATFORM_ARCHIVE_ID: string | null;
    PLATFORM_KIND: string | null;
    RESOURCE_ID: string | null;
    BRANCH_ID: string | null;
    PERMALINK_INFO: string | null;
    PLATFORM_INFO: string | null;
    DIRTY: boolean;
    PERMALINK_KIND: 'image' | 'public_share' | 'image_unfurling' | 'url_unfurling';
    TIMESTAMP: number;
}

export interface PermalinkExtra {
    image?: string;
    edit?: string;
    comment?: unknown;
    view?: unknown;
}

export interface GetUserListResult {
    users: Array<{ userInfo: Record<string, any>; role: string }>;
    basSessionsCount: number;
    minTimestamp: number;
    maxTimestamp: number;
}

const resetDatabase = false;
interface DBConnectorPoolOptions {
    mySqlDriverInstance: MySQLDriverPool;
    logger: Logger;
    config: Config;
    redisAdapter: RedisAdapter;
    callback: () => void;
}

export class DBConnectorPool {
    private mySqlDriverInstance: MySQLDriverPool;
    private logger: Logger;
    private config: Config;
    private redisAdapter: RedisAdapter;

    constructor({ mySqlDriverInstance, logger, config, redisAdapter, callback }: DBConnectorPoolOptions) {
        this.mySqlDriverInstance = mySqlDriverInstance;
        this.logger = logger.getLogger({ module: 'dbConnectorPool' });
        logger = this.logger.getLogger({ action: 'startup' });
        this.config = config;
        this.redisAdapter = redisAdapter;

        this.mySqlDriverInstance.getConnection((obj) => {
            let connection;
            if (isBASLegacyErrorObject(obj)) {
                logger.error('Unexpected error getting connection from DB - Shutting down' + obj.error);
                throw obj.error;
            }
            connection = obj;

            const checkPermalinkInfoExistence = () => {
                if (!config.mySQLConfig.permalinksDBName.match(DB_IDENTIFIER_REGEXP)) {
                    throw new Error('Invalid permalinks database ID');
                }
                connection.query(`USE ${config.mySQLConfig.permalinksDBName}`, (err) => {
                    if (err) {
                        if (err.code === 'ER_BAD_DB_ERROR') {
                            logger.info('#DBConnector# connected, creating permalinks database');
                            setupPermalinkDB(connection, config.mySQLConfig.permalinksDBName, (obj) => {
                                if (isBASLegacyErrorObject(obj)) {
                                    this.mySqlDriverInstance.releaseConnection(connection);
                                    throw obj.error;
                                } else {
                                    this.mySqlDriverInstance.releaseConnection(connection);
                                    callback();
                                }
                            });
                        } else {
                            this.mySqlDriverInstance.releaseConnection(connection);
                            throw err;
                        }
                    } else {
                        this.mySqlDriverInstance.releaseConnection(connection);
                        logger.info('#DBConnector# permalinks database exists');
                        callback();
                    }
                });
            };

            const checkBASExistence = () => {
                connection.query(`USE ${config.mySQLConfig.basDBName}`, (err) => {
                    if (err) {
                        if (err.code === 'ER_BAD_DB_ERROR') {
                            logger.info('#DBConnector# connected, creating BAS database');
                            setupDBs(connection, config, (obj) => {
                                if (isBASLegacyErrorObject(obj)) {
                                    this.mySqlDriverInstance.releaseConnection(connection);
                                    throw obj.error;
                                } else {
                                    checkPermalinkInfoExistence();
                                }
                            });
                        } else {
                            this.mySqlDriverInstance.releaseConnection(connection);
                            throw err;
                        }
                    } else {
                        logger.info('#DBConnector# BAS database exists');
                        checkPermalinkInfoExistence();
                    }
                });
            };

            if (resetDatabase) {
                deleteAllProjects(connection, logger, this.config.archiveIDPrefix, this.config.mySQLConfig.basDBName, () => {
                    checkBASExistence();
                });
            } else {
                checkBASExistence();
            }
        });
    }

    getDBConnector(
        logger: Logger,
        sessionData: Omit<SessionData, 'dbConnector'>,
        lockType: null | 'READ',
        callback: BASLegacyCallback<DBConnector>
    ): void {
        const { redisAdapter, config } = this;
        sessionData.connection.query(`USE ${this.config.mySQLConfig.basDBName}`, (err) => {
            if (err) {
                callback({ error: err });
            } else {
                const dbConnection = new DBConnector(sessionData, logger, config, redisAdapter);
                if (lockType) {
                    dbConnection.lock(lockType, (obj) => {
                        if (isBASLegacyErrorObject(obj)) {
                            callback(obj);
                        } else {
                            callback(dbConnection);
                        }
                    });
                } else {
                    callback(dbConnection);
                }
            }
        });
    }
}

export function setupDBs(connection: mysql.Connection, config: Config, callback: BASLegacyCallback<{}>): void {
    const basDBName = config.mySQLConfig.basDBName;

    function createConnectorTable(connection: mysql.Connection, callback: BASLegacyCallback<{}>) {
        connection.query('CREATE TABLE CONNECTOR_DATA (ID VARCHAR(255) PRIMARY KEY NOT NULL UNIQUE, DATA TEXT)', (err) => {
            if (err) {
                callback({ error: err });
            } else {
                callback({});
            }
        });
    }

    function createPlatformInfoTable(connection: mysql.Connection, callback: BASLegacyCallback<{}>) {
        connection.query(
            'CREATE TABLE PLATFORM_INFO (BAS_ARCHIVE_ID VARCHAR(255), PLATFORM_SITE_ID VARCHAR(255), PLATFORM_ARCHIVE_ID VARCHAR(255), PLATFORM_KIND TEXT, PLATFORM_ARCHIVE_NAME TEXT, TIMESTAMP BIGINT, PLATFORM_INFO TEXT, WARNING_FLAG TINYINT(1), UNIQUE (PLATFORM_SITE_ID, PLATFORM_ARCHIVE_ID), PRIMARY KEY (BAS_ARCHIVE_ID))',
            (err) => {
                if (err) {
                    callback({ error: err });
                } else {
                    createConnectorTable(connection, callback);
                }
            }
        );
    }

    function createUsersTable(connection: mysql.Connection, callback: BASLegacyCallback<{}>) {
        connection.query(
            'CREATE TABLE USERS (TOKEN VARCHAR(255) PRIMARY KEY NOT NULL UNIQUE, USERNAME TEXT, USERINFO TEXT, INTERNAL_ID TEXT, ARCHIVE_ID TEXT, STARTING_TIME BIGINT, PERMISSIONS TEXT, PLATFORM_TOKEN TEXT, TIMESTAMP BIGINT)',
            (err) => {
                if (err) {
                    callback({ error: err });
                } else {
                    createPlatformInfoTable(connection, callback);
                }
            }
        );
    }

    connection.query(`CREATE DATABASE IF NOT EXISTS ${basDBName} CHARACTER SET utf8 COLLATE utf8_general_ci`, (err) => {
        if (err) {
            callback({ error: err });
        } else {
            connection.query(`USE ${basDBName}`, (err) => {
                if (err) {
                    callback({ error: err });
                } else {
                    createUsersTable(connection, callback);
                }
            });
        }
    });
}

export function deleteAllProjects(
    connection: mysql.Connection,
    logger: Logger,
    archiveIDPrefix: string,
    basDBName: string,
    callback: BASLegacyCallback<{ status: 'completed' }>
): void {
    const contentData: WatchdogContentData = {};
    connection.query(`DELETE FROM ${basDBName}.PLATFORM_INFO`, () => {
        connection.query('SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA', (err, rows) => {
            if (err) {
                callback({ error: err });
            } else {
                for (const [id, obj] of Object.entries(rows)) {
                    if (!obj || typeof obj !== 'object' || !('SCHEMA_NAME' in obj) || typeof obj.SCHEMA_NAME !== 'string') {
                        continue;
                    }
                    if (
                        obj.SCHEMA_NAME === 'information_schema' ||
                        obj.SCHEMA_NAME === 'mysql' ||
                        obj.SCHEMA_NAME === 'innodb' ||
                        obj.SCHEMA_NAME === 'performance_schema' ||
                        obj.SCHEMA_NAME === 'tmp' ||
                        obj.SCHEMA_NAME.startsWith('mybalsamiq_') ||
                        !obj.SCHEMA_NAME.startsWith(archiveIDPrefix)
                    ) {
                        continue;
                    }
                    if (!obj.SCHEMA_NAME.match(DB_IDENTIFIER_REGEXP)) {
                        contentData[id] = { status: 'error', error: new Error('Invalid database name') };
                        continue;
                    }
                    contentData[id] = { status: 'onprogress' };
                    ((obj, id) => {
                        logger = logger.getLogger({ action: 'ops' });
                        logger.info('#DBConnector# dropping database ' + obj.SCHEMA_NAME);
                        connection.query('DROP DATABASE ' + obj.SCHEMA_NAME, (err) => {
                            if (err) {
                                contentData[id] = { status: 'error', error: err };
                            } else {
                                contentData[id] = { status: 'success' };
                            }
                        });
                    })(obj, id);
                }
                watchdog(contentData, callback);
            }
        });
    });
}

export function setupPermalinkDB(connection: mysql.Connection, permalinksDBName: string, callback: BASLegacyCallback<{}>): void {
    function createPermalinkInfoTable(connection: mysql.Connection, callback: BASLegacyCallback<{}>) {
        connection.query(
            "CREATE TABLE PERMALINK_INFO (PERMALINK_ID VARCHAR(255), PLATFORM_SITE_ID VARCHAR(255), PLATFORM_ARCHIVE_ID VARCHAR(255), PLATFORM_KIND TEXT, RESOURCE_ID VARCHAR(255), BRANCH_ID VARCHAR(255), PERMALINK_INFO TEXT, PLATFORM_INFO TEXT, DIRTY BOOL DEFAULT FALSE, PERMALINK_KIND ENUM('image', 'public_share', 'image_unfurling', 'url_unfurling') NOT NULL DEFAULT 'image', TIMESTAMP BIGINT, UNIQUE (PLATFORM_SITE_ID, PLATFORM_ARCHIVE_ID, RESOURCE_ID, BRANCH_ID, PERMALINK_KIND), PRIMARY KEY (PERMALINK_ID))",
            (err) => {
                const ret: { error?: mysql.MysqlError } = {};
                if (err) {
                    ret.error = err;
                }
                callback(ret);
            }
        );
    }
    if (!permalinksDBName.match(DB_IDENTIFIER_REGEXP)) {
        throw new Error('Invalid permalinks database ID');
    }
    connection.query(`CREATE DATABASE IF NOT EXISTS ${permalinksDBName} CHARACTER SET utf8 COLLATE utf8_general_ci`, (err) => {
        if (err) {
            callback({ error: err });
        } else {
            connection.query(`USE ${permalinksDBName}`, (err) => {
                if (err) {
                    callback({ error: err });
                } else {
                    createPermalinkInfoTable(connection, callback);
                }
            });
        }
    });
}

// TODO: rename to dbConnection after the whole project is converted to typescript
export class DBConnector {
    sessionData: Omit<SessionData, 'dbConnector'>;
    connection: mysql.Connection;
    logger: Logger;
    basDBName: string;
    redisAdapter: RedisAdapter;
    permalinksDBName: string;
    archiveIDPrefix: string;
    config: Config;

    constructor(sessionData: Omit<SessionData, 'dbConnector'>, logger: Logger, config: Config, redisAdapter: RedisAdapter) {
        this.sessionData = sessionData;
        this.logger = logger;
        this.config = config;
        this.redisAdapter = redisAdapter;
        this.permalinksDBName = config.mySQLConfig.permalinksDBName;
        this.archiveIDPrefix = config.archiveIDPrefix;
        this.basDBName = config.mySQLConfig.basDBName;
        this.connection = sessionData.connection;
    }

    pauseSession(
        callback: BASLegacyCallback<{
            resumeSession: (action: string, resumeCallback: BASLegacyCallback<{}>) => void;
        }>
    ): void {
        const sessionManager = this.sessionData.sessionManager;
        const dbConnector = this;
        const sessionData = this.sessionData as any;

        sessionManager.releaseSession(sessionData, function (obj) {
            if (isBASLegacyErrorObject(obj)) {
                callback && callback(obj);
            } else {
                callback &&
                    callback({
                        resumeSession: function (action: string, resumeCallback: BASLegacyCallback<{}>) {
                            sessionManager.resumeSession(action, sessionData, function (obj) {
                                if (isBASLegacyErrorObject(obj)) {
                                    resumeCallback && resumeCallback(obj);
                                } else {
                                    dbConnector.connection = obj.connection;
                                    resumeCallback && resumeCallback({});
                                }
                            });
                        },
                    });
            }
        });
    }

    releaseSession(callback: BASLegacyCallback<{}>): void {
        callback({});
    }

    unlock(callback: BASLegacyCallback<{}>): void {
        callback && callback({});
    }

    dropZombieArchive(timeoutInMs: number, callback: BASLegacyCallback<{ status: 'completed' }>): void {
        const contentData: WatchdogContentData = {};
        let archiveID: string;
        const logger = this.logger.getLogger({ action: 'dropZombieArchive', module: 'gar' });

        this.connection.query(
            `SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA LEFT OUTER JOIN ${this.basDBName}.PLATFORM_INFO ON INFORMATION_SCHEMA.SCHEMATA.SCHEMA_NAME = ${this.basDBName}.PLATFORM_INFO.BAS_ARCHIVE_ID WHERE ${this.basDBName}.PLATFORM_INFO.BAS_ARCHIVE_ID IS NULL`,
            (err, rows) => {
                if (err) {
                    logger.error('select failed ' + err, err);
                    callback({ error: err });
                } else {
                    // delay the deletion of selected archives in order to avoid race condition
                    setTimeout(() => {
                        for (const key in rows) {
                            if (Object.prototype.hasOwnProperty.call(rows, key)) {
                                const obj = rows[key];

                                if (!obj || typeof obj !== 'object' || !('SCHEMA_NAME' in obj) || typeof obj.SCHEMA_NAME !== 'string') {
                                    continue;
                                }

                                if (
                                    obj.SCHEMA_NAME.startsWith(this.config.archiveIDPrefix + 'wd_') ||
                                    obj.SCHEMA_NAME.startsWith(this.config.archiveIDPrefix + 'gd_') ||
                                    obj.SCHEMA_NAME.startsWith(this.config.archiveIDPrefix + 'jira_') ||
                                    obj.SCHEMA_NAME.startsWith(this.config.archiveIDPrefix + 'cloud_') ||
                                    obj.SCHEMA_NAME.startsWith(this.config.archiveIDPrefix + 'confluence_') ||
                                    obj.SCHEMA_NAME.startsWith(this.config.archiveIDPrefix + 'fs_')
                                ) {
                                    archiveID = obj.SCHEMA_NAME;

                                    if (!archiveID.match(DB_IDENTIFIER_REGEXP)) {
                                        contentData[archiveID] = { status: 'error', error: new Error('Invalid archive ID') };
                                        continue;
                                    }

                                    contentData[archiveID] = { status: 'onprogress' };

                                    ((archiveID: string) => {
                                        this.getPlatformData(archiveID, (obj) => {
                                            if (isBASLegacyErrorObject(obj)) {
                                                contentData[archiveID] = { status: 'error', error: obj.error };
                                            } else if ('BAS_ARCHIVE_ID' in obj) {
                                                // archive exists, avoid the race condition
                                                logger.info('archive exists, avoid race condition ' + archiveID);
                                                contentData[archiveID] = { status: 'success' };
                                            } else {
                                                this.connection.query('DROP DATABASE ' + archiveID, (err) => {
                                                    if (err) {
                                                        contentData[archiveID] = { status: 'error', error: err };
                                                        logger.warn('drop database failed ' + err);
                                                    } else {
                                                        contentData[archiveID] = { status: 'success' };
                                                        logger.info('dropped zombie archive ' + archiveID);
                                                    }
                                                });
                                            }
                                        });
                                    })(archiveID);
                                }
                            }
                        }
                        watchdog(contentData, callback);
                    }, timeoutInMs);
                }
            }
        );
    }

    deleteSessionZombieEntries(callback: BASLegacyCallback<{} | mysql.MysqlError>): void {
        const logger = this.logger.getLogger({ action: 'deleteSessionZombieEntries', module: 'gar' });
        const basDBName = this.basDBName;
        this.connection.query(
            `SELECT ${basDBName}.USERS.INTERNAL_ID FROM ${basDBName}.USERS LEFT OUTER JOIN ${basDBName}.PLATFORM_INFO ON ${basDBName}.USERS.ARCHIVE_ID = ${basDBName}.PLATFORM_INFO.BAS_ARCHIVE_ID WHERE ${basDBName}.PLATFORM_INFO.BAS_ARCHIVE_ID IS NULL`,
            (err: mysql.MysqlError | null, rows: Array<{ INTERNAL_ID: string }>) => {
                if (err) {
                    logger.error('select failed  ' + err, err);
                    callback && callback({ error: err });
                } else {
                    if (rows.length) {
                        const values = rows.map((row) => row.INTERNAL_ID);

                        this.connection.query(
                            `DELETE FROM ${basDBName}.USERS WHERE INTERNAL_ID IN (${makeSqlPlaceholdersForArray(values)})`,
                            values,
                            (err: mysql.MysqlError | null, result: mysql.OkPacket) => {
                                if (err) {
                                    logger.error('delete failed ' + err, err);
                                    callback && callback({ error: err });
                                } else {
                                    if (result.affectedRows) {
                                        logger.info('deleted ' + result.affectedRows + ' session zombies entries');
                                    }
                                    callback && callback({});
                                }
                            }
                        );
                    } else {
                        logger.info('no session zombies entries to be deleted');
                        callback && callback({});
                    }
                }
            }
        );
    }

    deleteArchiveZombieEntries(clock: Clock, callback: BASLegacyCallback<{}>): void {
        const logger = this.logger.getLogger({ action: 'deleteArchiveZombieEntries', module: 'gar' });
        // instantiate a new date from nowTimestamp
        const date = new Date(clock.now());
        const basDBName = this.basDBName;

        // Run the job on Sunday before 13:00
        if (date.getDay() === 0 && date.getHours() < 13) {
            this.connection.query(
                `SELECT ${basDBName}.PLATFORM_INFO.BAS_ARCHIVE_ID FROM ${basDBName}.PLATFORM_INFO LEFT OUTER JOIN INFORMATION_SCHEMA.SCHEMATA ON ${basDBName}.PLATFORM_INFO.BAS_ARCHIVE_ID = INFORMATION_SCHEMA.SCHEMATA.SCHEMA_NAME WHERE INFORMATION_SCHEMA.SCHEMATA.SCHEMA_NAME IS NULL`,
                (err: mysql.MysqlError | null, rows: Array<{ BAS_ARCHIVE_ID: string }>) => {
                    if (err) {
                        logger.error('select failed  ' + err, err);
                        callback && callback({ error: err });
                    } else if (rows.length) {
                        const values = rows.map((row) => row.BAS_ARCHIVE_ID);
                        this.connection.query(
                            `DELETE FROM ${basDBName}.PLATFORM_INFO WHERE BAS_ARCHIVE_ID IN (${makeSqlPlaceholdersForArray(values)})`,
                            values,
                            (err: mysql.MysqlError | null, result: mysql.OkPacket) => {
                                if (err) {
                                    logger.error('delete failed: ' + err, err);
                                    callback && callback({ error: err });
                                } else {
                                    logger.info('deleted ' + result.affectedRows + ' archive zombies entries');
                                    callback && callback({});
                                }
                            }
                        );
                    } else {
                        logger.info('no archive zombies entries to be deleted');
                        callback && callback({});
                    }
                }
            );
        } else {
            // Skip the job on other days
            logger.info('skip job, will execute next Sunday morning');
            callback && callback({});
        }
    }

    deleteZombieSession(timestamp: number, callback: BASLegacyCallback<{}>): void {
        const logger = this.logger.getLogger({ action: 'deleteZombieSession', module: 'gar' });
        this.connection.query(`SELECT TOKEN FROM ${this.basDBName}.USERS WHERE TIMESTAMP < ?`, [timestamp], (err, rows) => {
            if (err) {
                logger.error('select zombie sessions failed: ' + err, err);
            } else {
                if (rows.length) {
                    let concatenatedString = rows.map((obj: any) => obj.TOKEN).join(' ');
                    logger.info('deleted zombie sessions in users database: ' + concatenatedString);
                }
            }

            this.connection.query(`DELETE FROM ${this.basDBName}.USERS WHERE TIMESTAMP < ?`, [timestamp], (err, rows /*, fields*/) => {
                if (err) {
                    logger.error('delete failed: ' + err, err);
                    callback(err);
                } else {
                    if (rows.affectedRows) {
                        logger.info('deleted ' + rows.affectedRows + ' zombie sessions in users database');
                    }
                    callback({});
                }
            });
        });
    }

    makePermalinkFromDBRow(row: PermalinkInfoDBRow): PermalinkData {
        return {
            permalinkID: row.PERMALINK_ID,
            platformKind: row.PLATFORM_KIND ?? '',
            platformArchiveID: row.PLATFORM_ARCHIVE_ID ?? '',
            platformSiteID: row.PLATFORM_SITE_ID ?? '',
            resourceID: row.RESOURCE_ID ?? '',
            branchID: row.BRANCH_ID ?? '',
            timestamp: row.TIMESTAMP,
            dirty: !!row.DIRTY,
            permalinkKind: row.PERMALINK_KIND,
            permalinkInfo: row.PERMALINK_INFO ? JSON.parse(row.PERMALINK_INFO) : {},
            platformInfo: row.PLATFORM_INFO ? JSON.parse(row.PLATFORM_INFO) : {},
        };
    }

    lock(lockType: 'READ' | 'WRITE', callback: BASLegacyCallback<{}>): void {
        callback && callback({});
    }

    savePermalinkData(
        {
            permalinkID,
            platformKind,
            platformSiteID,
            platformArchiveID,
            resourceID,
            branchID,
            dirty,
            permalinkKind,
            permalinkInfo,
            platformInfo,
            timestamp,
        }: PermalinkData,
        callback?: BASLegacyCallback<{}>
    ): void {
        const values = [
            permalinkID,
            platformKind,
            platformSiteID,
            platformArchiveID,
            resourceID,
            branchID,
            timestamp,
            dirty,
            permalinkKind,
            permalinkInfo ? JSON.stringify(permalinkInfo) : null,
            platformInfo ? JSON.stringify(platformInfo) : null,
        ];

        if (!this.permalinksDBName.match(DB_IDENTIFIER_REGEXP)) {
            throw new Error('Invalid permalinks database ID');
        }

        this.connection.query(
            `INSERT INTO ${this.permalinksDBName}.PERMALINK_INFO 
            (PERMALINK_ID, PLATFORM_KIND, PLATFORM_SITE_ID, PLATFORM_ARCHIVE_ID, RESOURCE_ID, BRANCH_ID, TIMESTAMP, DIRTY, PERMALINK_KIND, PERMALINK_INFO, PLATFORM_INFO) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
            values,
            (err) => {
                if (err) {
                    const message = err.message ? err.message : JSON.stringify(err);
                    const resp: BASLegacyErrorObject = { error: message };
                    if (err.code === 'ER_DUP_ENTRY') {
                        // there is a concurrent request to load an archive
                        resp.busy = true;
                    }
                    callback && callback(resp);
                } else {
                    callback && callback({});
                }
            }
        );
    }

    async insertOrUpdatePermalink({
        permalinkID,
        platformKind,
        platformSiteID,
        platformArchiveID,
        resourceID,
        branchID,
        dirty,
        permalinkKind,
        permalinkInfo,
        platformInfo,
        timestamp,
    }: PermalinkData): Promise<[PermalinkData, number]> {
        const permalinkInfoStr = permalinkInfo ? JSON.stringify(permalinkInfo) : null;
        const platformInfoStr = platformInfo ? JSON.stringify(platformInfo) : null;

        const values = [
            permalinkID,
            platformKind,
            platformSiteID,
            platformArchiveID,
            resourceID,
            branchID,
            timestamp,
            dirty,
            permalinkKind,
            permalinkInfoStr,
            platformInfoStr,
            // on update
            timestamp,
            dirty,
            platformInfoStr,
        ];

        //NB - IMPORTANT: on update the permalinkInfo doesn't have to be changed because contains permalink image properties
        let insertOrUpdateResult = await this.runQuery<mysql.OkPacket>(
            `INSERT INTO ${this.permalinksDBName}.PERMALINK_INFO
            (PERMALINK_ID, PLATFORM_KIND, PLATFORM_SITE_ID, PLATFORM_ARCHIVE_ID, RESOURCE_ID, BRANCH_ID, TIMESTAMP, DIRTY, PERMALINK_KIND, PERMALINK_INFO, PLATFORM_INFO)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ON DUPLICATE KEY UPDATE
                TIMESTAMP = ?,
                DIRTY = ?,
                PLATFORM_INFO = ?
            `,
            values
        );

        let selectResults = await this.runQuery<PermalinkInfoDBRow[]>(
            `SELECT * FROM ${this.permalinksDBName}.PERMALINK_INFO WHERE
            PLATFORM_KIND = ? AND PLATFORM_SITE_ID = ? AND PLATFORM_ARCHIVE_ID = ? AND RESOURCE_ID = ? AND BRANCH_ID = ? AND PERMALINK_KIND = ?`,
            [platformKind, platformSiteID, platformArchiveID, resourceID, branchID, permalinkKind]
        );

        return [this.makePermalinkFromDBRow(selectResults[0]), insertOrUpdateResult.affectedRows]; // 1 = insert, 2 = updated, 0 = unchanged (updated with same values)
    }

    async markPermalinkAsOutdated({
        basArchiveID,
        resourceID,
        branchID,
    }: {
        basArchiveID: string;
        resourceID: string;
        branchID: string;
    }): Promise<number> {
        const platformInfo = await this.getArchiveIdentifiersByArchiveID(basArchiveID);
        if (!platformInfo) {
            throw new Error(`Archive not found with ID ${basArchiveID}`);
        }
        const platformArchiveId = platformInfo.PLATFORM_ARCHIVE_ID;
        const platformSiteId = platformInfo.PLATFORM_SITE_ID;
        const platformKind = platformInfo.PLATFORM_KIND;

        let res = await this.runQuery<mysql.OkPacket>(
            `UPDATE ${this.permalinksDBName}.PERMALINK_INFO SET DIRTY = true WHERE PLATFORM_ARCHIVE_ID = ? AND PLATFORM_SITE_ID = ? AND PLATFORM_KIND = ? AND RESOURCE_ID = ? AND BRANCH_ID = ?`,
            [platformArchiveId, platformSiteId, platformKind, resourceID, branchID]
        );
        return res.affectedRows;
    }

    async markPermalinkByPermalinkIDs({
        platformKind,
        platformSiteID,
        platformArchiveID,
        permalinkIDs,
        dirty,
        timestamp,
    }: {
        platformKind: string;
        platformSiteID: string;
        platformArchiveID: string;
        permalinkIDs: string[];
        dirty: boolean;
        timestamp: number;
    }): Promise<number> {
        let res = await this.runQuery<mysql.OkPacket>(
            `UPDATE ${this.permalinksDBName}.PERMALINK_INFO SET DIRTY = ?, TIMESTAMP = ? WHERE PLATFORM_ARCHIVE_ID = ? AND PLATFORM_SITE_ID = ? AND PLATFORM_KIND = ? AND PERMALINK_ID IN (${makeSqlPlaceholdersForArray(permalinkIDs)})`,
            [dirty, timestamp, platformArchiveID, platformSiteID, platformKind, ...permalinkIDs]
        );
        return res.affectedRows;
    }

    async getLockedQueries(): Promise<Record<string, any>[]> {
        let query: string;
        let mySqlVersion: string;
        let ret: Record<string, any>;

        try {
            ret = await this.runQuery<Record<string, any>>('SELECT VERSION()', []);
            mySqlVersion = ret['0']['VERSION()'];

            if (mySqlVersion.startsWith('5.7')) {
                query = `
                SELECT
                  r.trx_id waiting_trx_id,
                  r.trx_mysql_thread_id waiting_thread,
                  r.trx_query waiting_query,
                  b.trx_id blocking_trx_id,
                  b.trx_mysql_thread_id blocking_thread,
                  b.trx_query blocking_query
                FROM       information_schema.innodb_lock_waits w
                INNER JOIN information_schema.innodb_trx b
                  ON b.trx_id = w.blocking_trx_id
                INNER JOIN information_schema.innodb_trx r
                  ON r.trx_id = w.requesting_trx_id`;
            } else if (mySqlVersion.startsWith('8.')) {
                query = `
                SELECT
                    r.trx_id waiting_trx_id,
                    r.trx_mysql_thread_id waiting_thread,
                    r.trx_query waiting_query,
                    b.trx_id blocking_trx_id,
                    b.trx_mysql_thread_id blocking_thread,
                    b.trx_query blocking_query
                FROM       performance_schema.data_lock_waits w
                INNER JOIN information_schema.innodb_trx b
                ON b.trx_id = w.blocking_engine_transaction_id
                INNER JOIN information_schema.innodb_trx r
                ON r.trx_id = w.requesting_engine_transaction_id`;
            } else {
                // unknown MySql version
                return [];
            }
            return await this.runQuery<Record<string, any>[]>(query, []);
        } catch (e) {
            return [];
        }
    }

    async markPermalinksAsOutdated({
        platformKind,
        platformSiteID,
        platformArchiveID,
    }: {
        platformKind: string;
        platformSiteID: string;
        platformArchiveID: string;
    }): Promise<number> {
        let res = await this.runQuery<mysql.OkPacket>(
            `UPDATE ${this.permalinksDBName}.PERMALINK_INFO SET DIRTY = true WHERE
            PLATFORM_ARCHIVE_ID = ? AND PLATFORM_SITE_ID = ? AND PLATFORM_KIND = ?`,
            [platformArchiveID, platformSiteID, platformKind]
        );
        return res.affectedRows;
    }

    async getPermalinksByArchiveIDAndMinTimestamp({
        platformKind,
        platformSiteID,
        platformArchiveID,
        timestamp,
    }: {
        platformKind: string;
        platformSiteID: string | null;
        platformArchiveID: string;
        timestamp: number;
    }): Promise<{ permalinksData: PermalinkData[] }> {
        let row: PermalinkInfoDBRow;
        let query: string;
        let params: (string | number)[];

        if (platformSiteID) {
            query = `SELECT * FROM ${this.permalinksDBName}.PERMALINK_INFO WHERE PLATFORM_KIND = ? AND PLATFORM_SITE_ID = ? AND PLATFORM_ARCHIVE_ID = ? AND TIMESTAMP > ?`;
            params = [platformKind, platformSiteID, platformArchiveID, timestamp];
        } else {
            // in Cloud platformSiteID cannot be available when the archive is deleted
            query = `SELECT * FROM ${this.permalinksDBName}.PERMALINK_INFO WHERE PLATFORM_KIND = ? AND PLATFORM_ARCHIVE_ID = ? AND TIMESTAMP > ?`;
            params = [platformKind, platformArchiveID, timestamp];
        }

        const rows = await this.runQuery<PermalinkInfoDBRow[]>(query, params);

        const res = { permalinksData: [] as PermalinkData[] };
        for (const row of rows) {
            res.permalinksData.push(this.makePermalinkFromDBRow(row));
        }
        return res;
    }

    getPermalinkData(permalinkID: string, callback?: BASLegacyCallback<PermalinkData>): void {
        this.getPermalinkDataByID(permalinkID)
            .then((permalinkData) => {
                if (permalinkData !== null) {
                    callback?.(permalinkData);
                } else {
                    callback?.({
                        error: 'No permalink found with ID ' + permalinkID,
                        notFound: true,
                    });
                }
            })
            .catch((err) => {
                callback?.({ error: 'Unexpected exception: ' + err.message });
            });
    }

    async getPermalinkDataByID(permalinkID: string): Promise<PermalinkData | null> {
        let rows = await this.runQuery<PermalinkInfoDBRow[]>(
            `SELECT * FROM ${this.permalinksDBName}.PERMALINK_INFO WHERE PERMALINK_ID = ?`,
            [permalinkID]
        );
        if (rows.length === 0) {
            return null;
        }

        return this.makePermalinkFromDBRow(rows[0]);
    }

    async deletePermalinkData({
        platformKind,
        platformSiteID,
        platformArchiveID,
        resourceID,
        branchID,
        permalinkKind = 'image',
    }: {
        platformKind: string;
        platformSiteID: string;
        platformArchiveID: string;
        resourceID: string;
        branchID: string;
        permalinkKind: string;
    }): Promise<{ affectedRows: number }> {
        const results = await this.runQuery<mysql.OkPacket>(
            `DELETE FROM ${this.permalinksDBName}.PERMALINK_INFO WHERE PLATFORM_KIND = ? AND PLATFORM_SITE_ID = ? AND PLATFORM_ARCHIVE_ID = ? AND RESOURCE_ID = ? AND BRANCH_ID = ? AND PERMALINK_KIND = ?`,
            [platformKind, platformSiteID, platformArchiveID, resourceID, branchID, permalinkKind]
        );
        return {
            affectedRows: results.affectedRows,
        };
    }

    async deletePermalinksByPlatformKindAndPlatformArchiveIDs({
        platformKind,
        platformArchiveIDs,
    }: {
        platformKind: string | null;
        platformArchiveIDs: string[];
    }): Promise<{ affectedRows: number }> {
        if (platformArchiveIDs.length === 0) {
            return { affectedRows: 0 };
        }
        const results = await this.runQuery<mysql.OkPacket>(
            `DELETE FROM ${this.permalinksDBName}.PERMALINK_INFO WHERE PLATFORM_KIND = ? AND PLATFORM_ARCHIVE_ID IN (?)`,
            [platformKind, platformArchiveIDs]
        );
        return {
            affectedRows: results.affectedRows,
        };
    }

    async getPermalinkDataByPlatformKindAndPlatformArchiveIDs({
        platformKind,
        platformArchiveIDs,
    }: {
        platformKind: string;
        platformArchiveIDs: string[];
    }): Promise<{ permalinksData: PermalinkData[]; affectedRows: number }> {
        if (platformArchiveIDs.length === 0) {
            return {
                permalinksData: [],
                affectedRows: 0,
            };
        }
        const rows = await this.runQuery<PermalinkInfoDBRow[]>(
            `SELECT * FROM ${this.permalinksDBName}.PERMALINK_INFO WHERE PLATFORM_KIND = ? AND PLATFORM_ARCHIVE_ID IN (?)`,
            [platformKind, platformArchiveIDs]
        );
        const res = {
            permalinksData: [] as PermalinkData[],
            affectedRows: rows.length,
        };
        for (const row of rows) {
            res.permalinksData.push(this.makePermalinkFromDBRow(row));
        }
        return res;
    }

    async getPermalinkIDsByPlatformKindAndPlatformArchiveIDsAndPermalinkKind({
        platformKind,
        permalinkKind,
        platformArchiveIDs,
    }: {
        platformKind: string;
        permalinkKind: string;
        platformArchiveIDs: string[];
    }): Promise<string[]> {
        if (platformArchiveIDs.length === 0) {
            return [];
        }
        const rows = await this.runQuery<Array<{ PERMALINK_ID: string }>>(
            `SELECT PERMALINK_ID FROM ${this.permalinksDBName}.PERMALINK_INFO WHERE PLATFORM_KIND = ? AND PERMALINK_KIND = ? AND PLATFORM_ARCHIVE_ID IN (?)`,
            [platformKind, permalinkKind, platformArchiveIDs]
        );
        return rows.map((row: { PERMALINK_ID: string }) => row.PERMALINK_ID);
    }

    async deletePermalinksByPlatformKindAndPlatformArchiveIDsAndPermalinkKind({
        platformKind,
        permalinkKind,
        platformArchiveIDs,
    }: {
        platformKind: string;
        permalinkKind: string;
        platformArchiveIDs: string[];
    }): Promise<{ affectedRows: number }> {
        if (platformArchiveIDs.length === 0) {
            return { affectedRows: 0 };
        }
        const results = await this.runQuery<mysql.OkPacket>(
            `DELETE FROM ${this.permalinksDBName}.PERMALINK_INFO WHERE PLATFORM_KIND = ? AND PERMALINK_KIND = ? AND PLATFORM_ARCHIVE_ID IN (?)`,
            [platformKind, permalinkKind, platformArchiveIDs]
        );
        return {
            affectedRows: results.affectedRows,
        };
    }

    async getPermalinksDataByResourceIDs({
        platformKind,
        platformSiteID,
        platformArchiveID,
        resourceIDs,
        branchID,
        permalinkKind,
    }: {
        platformKind: string;
        platformSiteID: string;
        platformArchiveID: string;
        resourceIDs: string[];
        branchID: string;
        permalinkKind: string;
    }): Promise<{ permalinksData: PermalinkData[]; affectedRows: number }> {
        if (resourceIDs.length === 0) {
            return {
                permalinksData: [],
                affectedRows: 0,
            };
        }
        const query = `SELECT * FROM ${this.permalinksDBName}.PERMALINK_INFO WHERE PLATFORM_KIND = ? AND PLATFORM_SITE_ID = ? AND PLATFORM_ARCHIVE_ID = ? AND RESOURCE_ID IN (${makeSqlPlaceholdersForArray(
            resourceIDs
        )}) AND BRANCH_ID = ? AND PERMALINK_KIND = ?`;
        const params = [platformKind, platformSiteID, platformArchiveID, ...resourceIDs, branchID, permalinkKind];
        const rows = await this.runQuery<PermalinkInfoDBRow[]>(query, params);

        const res = {
            permalinksData: [] as PermalinkData[],
            affectedRows: rows.length,
        };

        for (const row of rows) {
            res.permalinksData.push(this.makePermalinkFromDBRow(row));
        }

        return res;
    }

    async deletePermalinksByPermalinkIDs({
        platformKind,
        platformSiteID,
        platformArchiveID,
        permalinkIDs,
        permalinkKind,
    }: {
        platformKind: string;
        platformSiteID: string;
        platformArchiveID: string;
        permalinkIDs: string[];
        permalinkKind: string;
    }): Promise<{ affectedRows: number }> {
        if (permalinkIDs.length === 0) {
            return { affectedRows: 0 };
        }
        const query = `DELETE FROM ${this.permalinksDBName}.PERMALINK_INFO WHERE PLATFORM_KIND = ? AND PLATFORM_SITE_ID = ? AND PLATFORM_ARCHIVE_ID = ? AND PERMALINK_ID IN (${makeSqlPlaceholdersForArray(
            permalinkIDs
        )}) AND PERMALINK_KIND = ?`;
        const params = [platformKind, platformSiteID, platformArchiveID, ...permalinkIDs, permalinkKind];
        const results = await this.runQuery<mysql.OkPacket>(query, params);
        return {
            affectedRows: results.affectedRows,
        };
    }

    deletePermalinksData(
        platformKind: string,
        platformSiteID: string | null,
        platformArchiveID: string,
        callback: BASLegacyCallback<{ affectedRows: number }>
    ): void {
        let query: string;
        let params: Array<string>;

        try {
            if (platformSiteID) {
                query = `DELETE FROM ${this.permalinksDBName}.PERMALINK_INFO WHERE PLATFORM_KIND = ? AND PLATFORM_SITE_ID = ? AND PLATFORM_ARCHIVE_ID = ?`;
                params = [platformKind, platformSiteID, platformArchiveID];
            } else {
                // in Cloud platformSiteID cannot be available when the archive is deleted
                query = `DELETE FROM ${this.permalinksDBName}.PERMALINK_INFO WHERE PLATFORM_KIND = ? AND PLATFORM_ARCHIVE_ID = ?`;
                params = [platformKind, platformArchiveID];
            }

            this.connection.query(query, params, (err, res) => {
                if (err) {
                    callback && callback({ error: err });
                } else if (res.affectedRows) {
                    const result = {
                        affectedRows: res.affectedRows,
                    };
                    callback && callback(result);
                } else {
                    const error: BASLegacyErrorObject = {
                        error: 'No permalinks found for the requested archive',
                        notFound: true,
                    };
                    callback && callback(error);
                }
            });
        } catch (e) {
            callback && callback({ error: `Unexpected exception: ${(e as Error).message}` });
        }
    }

    async deletePermalinksByArchiveIDAndMinTimestamp({
        platformKind,
        platformSiteID,
        platformArchiveID,
        timestamp,
    }: {
        platformKind: string;
        platformSiteID?: string;
        platformArchiveID: string;
        timestamp: number;
    }): Promise<number> {
        let query: string;
        let params: Array<string | number>;

        if (platformSiteID) {
            query = `DELETE FROM ${this.permalinksDBName}.PERMALINK_INFO WHERE PLATFORM_KIND = ? AND PLATFORM_SITE_ID = ? AND PLATFORM_ARCHIVE_ID = ? AND TIMESTAMP > ?`;
            params = [platformKind, platformSiteID, platformArchiveID, timestamp];
        } else {
            // in Cloud platformSiteID cannot be available when the archive is deleted
            query = `DELETE FROM ${this.permalinksDBName}.PERMALINK_INFO WHERE PLATFORM_KIND = ? AND PLATFORM_ARCHIVE_ID = ? AND TIMESTAMP > ?`;
            params = [platformKind, platformArchiveID, timestamp];
        }

        const res = await this.runQuery<mysql.OkPacket>(query, params);
        return res.affectedRows;
    }

    getPermalinksDataFromArchiveID(
        platformKind: string,
        platformSiteID: string | null,
        platformArchiveID: string,
        callback: BASLegacyCallback<{ permalinksData: PermalinkData[] }>
    ): void {
        callSaneFunctionFromLegacy(
            this.getPermalinksByArchiveIDAndMinTimestamp({
                platformKind,
                platformSiteID,
                platformArchiveID,
                timestamp: 0,
            }),
            callback
        );
    }

    async getPermalinkDataFromResourceID({
        platformKind,
        platformSiteID,
        platformArchiveID,
        resourceID,
        branchID,
        permalinkKind,
    }: {
        platformKind: string;
        platformSiteID: string | null;
        platformArchiveID: string;
        resourceID: string;
        branchID: string;
        permalinkKind?: string;
    }): Promise<PermalinkData | {}> {
        const _permalinkKind = permalinkKind || Consts.PermalinkKind.image;

        const rows = await this.runQuery<PermalinkInfoDBRow[]>(
            `SELECT * FROM ${this.permalinksDBName}.PERMALINK_INFO WHERE PLATFORM_KIND = ? AND PLATFORM_SITE_ID = ? AND PLATFORM_ARCHIVE_ID = ? AND RESOURCE_ID = ? AND BRANCH_ID = ? AND PERMALINK_KIND = ?`,
            [platformKind, platformSiteID, platformArchiveID, resourceID, branchID, _permalinkKind]
        );

        if (rows.length === 0) {
            return {};
        }

        return this.makePermalinkFromDBRow(rows[0]);
    }

    async hasPublicShare({
        platformKind,
        platformSiteID,
        platformArchiveID,
    }: {
        platformKind: string;
        platformSiteID: string;
        platformArchiveID: string;
    }): Promise<{ hasPublicShare: boolean }> {
        const rows = await this.runQuery<PermalinkInfoDBRow[]>(
            `SELECT * FROM ${this.permalinksDBName}.PERMALINK_INFO WHERE PLATFORM_KIND = ? AND PLATFORM_SITE_ID = ? AND PLATFORM_ARCHIVE_ID = ? AND PERMALINK_KIND = ?`,
            [platformKind, platformSiteID, platformArchiveID, Consts.PermalinkKind.public_share]
        );

        return {
            hasPublicShare: rows.length !== 0,
        };
    }

    async getPermalinkDataFromPermalinkID({
        platformKind,
        platformSiteID,
        platformArchiveID,
        permalinkID,
    }: {
        platformKind: string;
        platformSiteID: string;
        platformArchiveID: string;
        permalinkID: string;
    }): Promise<PermalinkData | {}> {
        const rows = await this.runQuery<PermalinkInfoDBRow[]>(
            `SELECT * FROM ${this.permalinksDBName}.PERMALINK_INFO WHERE PLATFORM_KIND = ? AND PLATFORM_SITE_ID = ? AND PLATFORM_ARCHIVE_ID = ? AND PERMALINK_ID = ?`,
            [platformKind, platformSiteID, platformArchiveID, permalinkID]
        );

        if (rows.length === 0) {
            return {};
        }

        return this.makePermalinkFromDBRow(rows[0]);
    }

    getConnectorData(kind: string, id: string, callback: BASLegacyCallback<Record<string, unknown>>): void {
        const connector_id = kind + '_' + id;
        let res: Record<string, unknown>;

        try {
            this.connection.query(`SELECT DATA FROM ${this.basDBName}.CONNECTOR_DATA WHERE ID = ?`, [connector_id], (err, rows) => {
                if (err) {
                    callback({ error: err });
                } else if (rows && rows.length === 1) {
                    res = JSON.parse(rows[0].DATA);
                    callback(res);
                } else {
                    callback({}); // TODO: just return null if not found
                }
            });
        } catch (e) {
            callback({ error: 'Unexpected exception :' + (e as Error).message });
        }
    }

    getAllSessionsForKind(kind: string, callback: BASLegacyCallback<{ rows: User[] }>): void {
        try {
            this.connection.query(
                `SELECT * FROM ${this.basDBName}.USERS WHERE ARCHIVE_ID LIKE ?`,
                [this.archiveIDPrefix + kind + '_%'],
                (err, rows) => {
                    if (err) {
                        callback({ error: err });
                    } else if (rows) {
                        callback({ rows: rows as User[] });
                    } else {
                        callback({ rows: [] });
                    }
                }
            );
        } catch (e) {
            callback({ error: 'Unexpected exception:' + (e as Error).message });
        }
    }

    filterArchiveIdsByLastUpdate(
        kind: string,
        timestamp: number,
        archiveIds: string[],
        callback: BASLegacyCallback<{ rows: { ARCHIVE_ID: string }[] }>
    ): void {
        try {
            if (archiveIds.length === 0) {
                callback?.({ rows: [] });
                return;
            }
            this.connection.query(
                `SELECT BAS_ARCHIVE_ID AS ARCHIVE_ID FROM ${this.basDBName}.PLATFORM_INFO WHERE PLATFORM_KIND = ? AND TIMESTAMP < ? AND BAS_ARCHIVE_ID IN (${makeSqlPlaceholdersForArray(
                    archiveIds
                )})`,
                [kind, timestamp, ...archiveIds],
                (err, rows) => {
                    if (err) {
                        callback({ error: err });
                    } else if (rows) {
                        callback({ rows: rows as { ARCHIVE_ID: string }[] });
                    } else {
                        callback({ rows: [] });
                    }
                }
            );
        } catch (e) {
            callback({ error: 'Unexpected exception:' + (e as Error).message });
        }
    }

    saveConnectorData(kind: string, id: string, data: Record<string, unknown>, callback: BASLegacyCallback<{}>): void {
        const connector_id = kind + '_' + id;
        const data_string = JSON.stringify(data);
        const values = [connector_id, data_string, data_string];
        this.connection.query(
            `INSERT INTO ${this.basDBName}.CONNECTOR_DATA (ID, DATA) VALUES (?, ?) ON DUPLICATE KEY UPDATE DATA=?`,
            values,
            (err) => {
                if (err) {
                    callback({ error: err });
                } else {
                    callback({});
                }
            }
        );
    }

    deleteConnectorData(kind: string, id: string, callback: BASLegacyCallback<{}>): void {
        const connector_id = kind + '_' + id;
        const values = [connector_id];
        this.connection.query(`DELETE FROM ${this.basDBName}.CONNECTOR_DATA WHERE ID=?`, values, (err) => {
            if (err) {
                callback({ error: err });
            } else {
                callback({});
            }
        });
    }

    getUser(token: string, callback: BASLegacyCallback<User>): void {
        if (!token) {
            callback?.({ error: 'Session token is null' });
            return;
        }

        this.connection.query(`SELECT * FROM ${this.basDBName}.USERS WHERE TOKEN = ?`, [token], (err, rows) => {
            if (err) {
                callback({ error: err });
                return;
            }
            if (rows && rows.length === 1) {
                callback(rows[0] as User);
            } else {
                callback({ error: 'Session closed', busy: true });
            }
        });
    }

    saveUser(
        token: string,
        userInfo: Record<string, unknown>,
        internalUserID: string,
        archiveID: string,
        startingTime: number,
        role: string,
        platformToken: string,
        callback: BASLegacyCallback<{}>
    ): void {
        let userName: unknown;
        let userInfoStr: string;
        try {
            userName = userInfo.name;
            userInfoStr = JSON.stringify(userInfo);
        } catch (e) {
            userName = 'unknown';
            userInfoStr = "{name: 'unknown'}";
        }
        const values = [token, userName, userInfoStr, internalUserID, archiveID, startingTime, role, platformToken, startingTime];
        this.connection.query(
            `INSERT INTO ${this.basDBName}.USERS (TOKEN, USERNAME, USERINFO, INTERNAL_ID, ARCHIVE_ID, STARTING_TIME, PERMISSIONS, PLATFORM_TOKEN, TIMESTAMP) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
            values,
            (err) => {
                if (err) {
                    callback({ error: err });
                } else {
                    callback({});
                }
            }
        );
    }

    deleteSessionByToken(token: string, callback: BASLegacyCallback<{}>): void {
        this.connection.query(`DELETE FROM ${this.basDBName}.USERS WHERE TOKEN = ?`, [token], (err) => {
            if (err) {
                callback({ error: err });
            } else {
                callback({});
            }
        });
    }

    deleteSessionsByArchiveID(archiveID: string, callback: BASLegacyCallback<{}>): void {
        this.connection.query(`DELETE FROM ${this.basDBName}.USERS WHERE ARCHIVE_ID = ?`, [archiveID], (err) => {
            if (err) {
                callback({ error: err });
            } else {
                callback({});
            }
        });
    }

    getUsersList(internalIDsList: string[], option?: string, callback?: BASLegacyCallback<GetUserListResult>): void {
        const resp: GetUserListResult = {
            users: [],
            basSessionsCount: 0,
            minTimestamp: 0,
            maxTimestamp: 0,
        };
        let minTimestamp: number | undefined;
        let maxTimestamp: number | undefined;
        const map: Record<string, any> = {};

        if (internalIDsList.length) {
            const query = `SELECT USERINFO, PERMISSIONS, TIMESTAMP FROM ${this.basDBName}.USERS WHERE INTERNAL_ID IN (${makeSqlPlaceholdersForArray(internalIDsList)})`;
            this.connection.query(query, internalIDsList, (err, rows) => {
                if (err) {
                    callback?.({ error: `${err} (${query})` });
                } else {
                    for (const row of rows) {
                        let userInfo: Record<string, any>;
                        try {
                            userInfo = JSON.parse(row.USERINFO);
                            // platform info can contain security sensitive information
                            delete userInfo.platformInfo;
                        } catch (e) {
                            userInfo = { name: row.USERNAME };
                        }

                        const role = `${row.PERMISSIONS}`;
                        if (option && option === 'all') {
                            resp.users.push({ userInfo, role });
                        } else {
                            if (minTimestamp === undefined || row.TIMESTAMP < minTimestamp) {
                                minTimestamp = row.TIMESTAMP;
                            }

                            if (maxTimestamp === undefined || row.TIMESTAMP > maxTimestamp) {
                                maxTimestamp = row.TIMESTAMP;
                            }

                            if (userInfo.avatarURL) {
                                if (map[userInfo.name]) {
                                    // already inserted
                                } else {
                                    map[userInfo.name] = userInfo;
                                    resp.users.push({ userInfo, role });
                                }
                            }
                        }
                    }
                    resp.basSessionsCount = rows.length;
                    resp.minTimestamp = minTimestamp !== undefined ? minTimestamp : 0;
                    resp.maxTimestamp = maxTimestamp !== undefined ? maxTimestamp : 0;
                    callback?.(resp);
                }
            });
        } else {
            callback?.(resp);
        }
    }

    getLoadedArchiveForKind(warningFlag: number, callback: BASLegacyCallback<Record<string, number>>): void {
        const query = 'SELECT PLATFORM_KIND, COUNT(*) AS COUNT FROM PLATFORM_INFO WHERE WARNING_FLAG = ? GROUP BY PLATFORM_KIND';
        this.connection.query(query, [warningFlag], (err, rows) => {
            if (err) {
                callback({ error: `${err} (${query})` });
            } else {
                const res: Record<string, number> = {};
                for (const row of rows) {
                    const kind = row.PLATFORM_KIND;
                    res[kind] = row.COUNT;
                }
                callback(res);
            }
        });
    }

    getArchiveNotSyncForKind(kind: string, callback: BASLegacyCallback<{ rows: PlatformData[] }>): void {
        const query = 'SELECT * FROM PLATFORM_INFO WHERE WARNING_FLAG = ? AND PLATFORM_KIND = ?';
        this.connection.query(query, [1, kind], (err, rows) => {
            if (err) {
                callback?.({ error: `${err} (${query})` });
            } else {
                callback?.({ rows: rows as PlatformData[] });
            }
        });
    }

    getUserInfo(internalID: string, callback: BASLegacyCallback<{ userInfo: Record<string, unknown>; role: string }>): void {
        const query = `SELECT USERINFO, PERMISSIONS FROM ${this.basDBName}.USERS WHERE INTERNAL_ID = ?`;
        this.connection.query(query, [internalID], (err, rows) => {
            if (err) {
                callback?.({ error: `${err} (${query})` });
            } else {
                if (rows.length === 1) {
                    let userInfo: Record<string, unknown>;
                    try {
                        userInfo = JSON.parse(rows[0].USERINFO);
                    } catch (e) {
                        userInfo = { name: rows[0].USERNAME };
                    }
                    callback?.({ userInfo: userInfo, role: rows[0].PERMISSIONS });
                } else {
                    callback?.({ error: 'Unexpected error: token is not unique' });
                }
            }
        });
    }

    updateUserInfo(internalID: string, userInfo: Record<string, any>, callback: BASLegacyCallback<{}>): void {
        const query = `UPDATE ${this.basDBName}.USERS SET USERINFO = ? WHERE INTERNAL_ID = ?`;
        this.connection.query(query, [JSON.stringify(userInfo), internalID], (err) => {
            if (err) {
                callback({ error: err });
            } else {
                callback({});
            }
        });
    }

    getUsersForArchive(archiveID: string, callback: BASLegacyCallback<{ users: User[] }>): void {
        this.getSessionsByArchiveId(archiveID)
            .then((rows) => {
                callback({ users: rows });
            })
            .catch((err) => {
                callback({ error: err.message });
            });
    }

    async getSessionsByArchiveId(archiveId: string): Promise<User[]> {
        return await this.runQuery<User[]>(`SELECT * FROM ${this.basDBName}.USERS WHERE ARCHIVE_ID = ?`, [archiveId]);
    }

    saveArchivePlatformData(
        basArchiveID: string,
        platformKind: string,
        platformSiteID: string | null,
        platformArchiveID: string,
        platformArchiveName: string | null,
        platformInfo: Record<string, unknown> | null,
        callback: BASLegacyCallback<{}>
    ): void {
        const timestamp = new Date().getTime();
        const warningFlag = 0;
        const values = [
            basArchiveID,
            platformKind,
            platformSiteID,
            platformArchiveID,
            platformArchiveName,
            timestamp,
            warningFlag,
            platformInfo ? JSON.stringify(platformInfo) : null,
        ];

        this.connection.query(
            `INSERT INTO ${this.basDBName}.PLATFORM_INFO (BAS_ARCHIVE_ID, PLATFORM_KIND, PLATFORM_SITE_ID, PLATFORM_ARCHIVE_ID, PLATFORM_ARCHIVE_NAME, TIMESTAMP, WARNING_FLAG, PLATFORM_INFO) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
            values,
            (err) => {
                let message;
                if (err) {
                    message = err.message ? err.message : JSON.stringify(err);
                    const resp: BASLegacyErrorObject = { error: message };
                    if (err.code === 'ER_DUP_ENTRY') {
                        // there is a concurrent request to load an archive
                        resp.busy = true;
                        resp.zombie_bar = [basArchiveID];
                    }
                    callback(resp);
                } else {
                    callback({});
                }
            }
        );
    }

    updateArchivePlatformData(
        basArchiveID: string,
        platformKind: string,
        platformSiteID: string | null,
        platformArchiveID: string,
        platformArchiveName: string | null,
        platformInfo: Record<string, any> | null,
        callback: BASLegacyCallback<{}>
    ): void {
        const timestamp = new Date().getTime();
        const values = [
            platformKind,
            platformSiteID,
            platformArchiveID,
            platformArchiveName,
            timestamp,
            platformInfo ? JSON.stringify(platformInfo) : null,
            basArchiveID,
        ];

        this.connection.query(
            `UPDATE ${this.basDBName}.PLATFORM_INFO SET PLATFORM_KIND = ?, PLATFORM_SITE_ID = ?, PLATFORM_ARCHIVE_ID = ?, PLATFORM_ARCHIVE_NAME = ?, TIMESTAMP = ?, PLATFORM_INFO = ? WHERE BAS_ARCHIVE_ID = ?`,
            values,
            (err) => {
                if (err) {
                    callback({ error: err });
                } else {
                    callback({});
                }
            }
        );
    }

    updateArchivePlatformName(basArchiveID: string, platformArchiveName: string, callback?: BASLegacyCallback<{}>): void {
        const timestamp = new Date().getTime();
        const values = [platformArchiveName, timestamp, basArchiveID];

        this.connection.query(
            `UPDATE ${this.basDBName}.PLATFORM_INFO SET PLATFORM_ARCHIVE_NAME = ?, TIMESTAMP = ? WHERE BAS_ARCHIVE_ID = ?`,
            values,
            (err) => {
                if (err) {
                    callback && callback({ error: err });
                } else {
                    callback && callback({});
                }
            }
        );
    }

    updateArchiveWarningFlag(basArchiveID: string, warningFlag: boolean | number, callback?: BASLegacyCallback<{}>): void {
        const values = [warningFlag, basArchiveID];

        this.connection.query(`UPDATE ${this.basDBName}.PLATFORM_INFO SET WARNING_FLAG = ? WHERE BAS_ARCHIVE_ID = ?`, values, (err) => {
            if (err) {
                callback && callback({ error: err });
            } else {
                callback && callback({});
            }
        });
    }

    async getArchiveIdentifiersByArchiveID(basArchiveID: string): Promise<PlatformData | null> {
        let platformData = await this.redisAdapter.getPlatformDataByArchiveID(basArchiveID);
        if (!platformData) {
            let rows = await this.runQuery<Array<PlatformData>>(
                `SELECT BAS_ARCHIVE_ID, PLATFORM_SITE_ID, PLATFORM_ARCHIVE_ID, PLATFORM_KIND FROM ${this.basDBName}.PLATFORM_INFO WHERE BAS_ARCHIVE_ID = ?`,
                [basArchiveID]
            );
            if (rows.length > 1) {
                throw new Error(`Multiple rows for BAS_ARCHIVE_ID = ${basArchiveID}`);
            }
            platformData = rows.length === 1 ? rows[0] : null;
            if (platformData) {
                await this.redisAdapter.setPlatformData(platformData);
            }
        }
        return platformData;
    }

    getPlatformData(basArchiveID: string, callback?: BASLegacyCallback<{} | PlatformData>): void {
        this.connection.query(`SELECT * FROM ${this.basDBName}.PLATFORM_INFO WHERE BAS_ARCHIVE_ID = ?`, [basArchiveID], (err, rows) => {
            if (err) {
                callback && callback({ error: err });
            } else if (rows && rows.length === 0) {
                callback && callback({}); // not found
            } else if (rows && rows.length !== 1) {
                callback && callback({ error: 'Not unique' });
            } else {
                callback && callback(rows[0] as PlatformData);
            }
        });
    }

    getPlatformInfoFromPlatformArchiveID(
        partOfplatformSiteID: string,
        platformArchiveID: string,
        callback: BASLegacyCallback<{ PLATFORM_INFO: string }>
    ): void {
        const key = '%' + partOfplatformSiteID + '%';

        this.connection.query(
            `SELECT PLATFORM_INFO FROM ${this.basDBName}.PLATFORM_INFO WHERE PLATFORM_ARCHIVE_ID = ? AND PLATFORM_SITE_ID LIKE ?`,
            [platformArchiveID, key],
            (err, rows) => {
                if (err) {
                    callback({ error: err });
                } else if (rows && rows.length === 0) {
                    callback({ error: 'Not found' });
                } else if (rows && rows.length !== 1) {
                    callback({ error: 'Not unique' });
                } else {
                    callback(rows[0]);
                }
            }
        );
    }

    getPlatformInfoByPlatformSiteID(platformSiteID: string, callback: BASLegacyCallback<{ rows: PlatformData[] }>): void {
        this.connection.query(`SELECT * FROM ${this.basDBName}.PLATFORM_INFO WHERE PLATFORM_SITE_ID = ?`, [platformSiteID], (err, rows) => {
            if (err) {
                callback({ error: err });
            } else {
                callback({ rows: rows as PlatformData[] });
            }
        });
    }

    // TODO: Replace beginTransaction and closeTransaction with a single function `withTransaction`
    beginTransaction(callback: BASLegacyCallback<{}>): void {
        this.connection.query('BEGIN', (err) => {
            if (err) {
                callback({ error: err });
                return;
            }
            callback({});
        });
    }
    // TODO: Replace beginTransaction and closeTransaction with a single function `withTransaction`
    closeTransaction(commit: boolean, callback: BASLegacyCallback<{}>): void {
        this.connection.query(commit ? 'COMMIT' : 'ROLLBACK', (err) => {
            if (err) {
                callback({ error: err });
            } else {
                callback({});
            }
        });
    }

    getBASArchiveIDWithExclusiveRowLock(
        platformSiteID: string,
        platformArchiveID: string,
        withTransaction: boolean,
        callback: BASLegacyCallback<PlatformData | {}>
    ): void {
        const selectQuery = (cb: BASLegacyCallback<PlatformData | {}>) => {
            this.connection.query(
                `SELECT * FROM ${this.basDBName}.PLATFORM_INFO WHERE PLATFORM_SITE_ID = ? AND PLATFORM_ARCHIVE_ID = ? FOR UPDATE`,
                [platformSiteID, platformArchiveID],
                this.callbackForGetBASArchiveID((obj) => {
                    if (obj && 'error' in obj) {
                        cb(obj);
                        return;
                    }
                    cb(obj);
                })
            );
        };

        if (withTransaction) {
            this.beginTransaction((result) => {
                if (result && 'error' in result) {
                    callback(result);
                    return;
                }

                selectQuery((resultOfTheSelect) => {
                    if (resultOfTheSelect && 'error' in resultOfTheSelect) {
                        callback(resultOfTheSelect);
                        return;
                    }

                    this.closeTransaction(true, (result) => {
                        if (result && 'error' in result) {
                            callback(result);
                            return;
                        }
                        callback(resultOfTheSelect);
                    });
                });
            });
        } else {
            selectQuery(callback);
        }
    }

    getBASArchiveID(platformSiteID: string, platformArchiveID: string, callback: BASLegacyCallback<PlatformData | {}>): void {
        this.connection.query(
            `SELECT * FROM ${this.basDBName}.PLATFORM_INFO WHERE PLATFORM_SITE_ID = ? AND PLATFORM_ARCHIVE_ID = ?`,
            [platformSiteID, platformArchiveID],
            this.callbackForGetBASArchiveID(callback)
        );
    }

    async replaceArchiveID(oldArchiveId: string, newArchiveID: string): Promise<Record<string, any>> {
        return await this.runQuery<mysql.OkPacket>(
            `UPDATE ${this.basDBName}.PLATFORM_INFO SET BAS_ARCHIVE_ID = ? WHERE BAS_ARCHIVE_ID = ?`,
            [newArchiveID, oldArchiveId]
        );
    }

    private callbackForGetBASArchiveID(callback: BASLegacyCallback<PlatformData | {}>) {
        return (err: mysql.MysqlError | null, rows: PlatformData[]) => {
            if (err) {
                callback({ error: err });
            } else if (rows && rows.length === 0) {
                callback({}); // not found
            } else if (rows && rows.length !== 1) {
                callback({ error: 'Not unique' });
            } else {
                callback(rows[0]);
            }
        };
    }

    deleteArchivePlatformData(basArchiveID: string, callback?: BASLegacyCallback<{}>): void {
        this.connection.query(`DELETE FROM ${this.basDBName}.PLATFORM_INFO WHERE BAS_ARCHIVE_ID = ?`, [basArchiveID], (err) => {
            if (err) {
                callback?.({ error: err });
            } else {
                callback?.({});
            }
        });
    }

    parkArchivePlatformData(platformArchiveID: string, basArchiveID: string, callback?: BASLegacyCallback<{}>): void {
        const query = `UPDATE ${this.basDBName}.PLATFORM_INFO SET PLATFORM_ARCHIVE_ID = ? WHERE BAS_ARCHIVE_ID = ?`;
        const randomId = uuid.v1().replace(/-/g, '_');
        const parkedPlatformArchiveID = 'PARKED_' + platformArchiveID + '_' + randomId;

        this.connection.query(query, [parkedPlatformArchiveID, basArchiveID], (err) => {
            if (err) {
                callback?.({ error: err });
            } else {
                callback?.({});
            }
        });
    }

    getArchiveIDfromPlatformArchiveID(
        platformSiteID: string,
        platformArchiveID: string,
        callback: BASLegacyCallback<{ archiveID: string }>
    ): void {
        this.connection.query(
            `SELECT BAS_ARCHIVE_ID FROM ${this.basDBName}.PLATFORM_INFO WHERE PLATFORM_SITE_ID = ? AND PLATFORM_ARCHIVE_ID = ?`,
            [platformSiteID, platformArchiveID],
            (err, rows) => {
                if (err) {
                    callback({ error: err });
                } else {
                    if (rows.length !== 1) {
                        callback({
                            error: 'getArchiveIDfromPlatformArchiveID: Unexpected number of results (' + rows.length + ')',
                            notFound: rows.length === 0,
                        });
                    } else {
                        callback({ archiveID: rows[0].BAS_ARCHIVE_ID });
                    }
                }
            }
        );
    }

    getArchiveIDfromPlatformArchiveIDAndKind(
        platformArchiveID: string,
        platformKind: string,
        callback: BASLegacyCallback<{ archiveID: string }>
    ): void {
        this.getArchivefromPlatformArchiveIDAndKind(platformArchiveID, platformKind)
            .then((archive) => {
                if (archive) {
                    callback({ archiveID: archive.BAS_ARCHIVE_ID });
                } else {
                    callback({
                        error: 'getArchiveIDfromPlatformArchiveIDAndKind: Unexpected number of results',
                        notFound: true,
                    });
                }
            })
            .catch((err) => {
                callback({ error: err.message });
            });
    }

    async getArchivefromPlatformArchiveIDAndKind(
        platformArchiveID: string,
        platformKind: string
    ): Promise<{ BAS_ARCHIVE_ID: string } | null> {
        let rows = await this.runQuery<Array<{ BAS_ARCHIVE_ID: string }>>(
            `SELECT BAS_ARCHIVE_ID FROM ${this.basDBName}.PLATFORM_INFO WHERE PLATFORM_ARCHIVE_ID = ? AND PLATFORM_KIND = ?`,
            [platformArchiveID, platformKind]
        );
        return rows.length === 1 ? rows[0] : null;
    }

    updatePlatformAuthToken(
        token: string,
        platformAuthToken: string | undefined,
        clock: Record<string, any>,
        callback: BASLegacyCallback<{ success: 'ok' }>
    ): void {
        const timestamp = clock.now();
        let values: unknown[];
        let query: string;

        // We avoid overriding the already stored platform token if not passed as parameters
        if (platformAuthToken) {
            values = [platformAuthToken, timestamp, token];
            query = `UPDATE ${this.basDBName}.USERS SET PLATFORM_TOKEN = ?, TIMESTAMP = ? WHERE TOKEN = ?`;
        } else {
            values = [timestamp, token];
            query = `UPDATE ${this.basDBName}.USERS SET TIMESTAMP = ? WHERE TOKEN = ?`;
        }

        this.connection.query(query, values, (err) => {
            if (err) {
                callback({ error: err });
            } else {
                callback({ success: 'ok' });
            }
        });
    }

    selectPlatformDataToUnload(
        kind: string,
        timestamp: number,
        warningFlag: boolean | number,
        callback: BASLegacyCallback<Array<{ BAS_ARCHIVE_ID: string }>>
    ): void {
        this.connection.query(
            `SELECT BAS_ARCHIVE_ID FROM ${this.basDBName}.PLATFORM_INFO 
             LEFT OUTER JOIN ${this.basDBName}.USERS ON ${this.basDBName}.PLATFORM_INFO.BAS_ARCHIVE_ID = ${this.basDBName}.USERS.ARCHIVE_ID 
             WHERE ${this.basDBName}.USERS.ARCHIVE_ID IS NULL 
             AND ${this.basDBName}.PLATFORM_INFO.PLATFORM_KIND = ? 
             AND ${this.basDBName}.PLATFORM_INFO.TIMESTAMP < ? 
             AND ${this.basDBName}.PLATFORM_INFO.WARNING_FLAG = ?`,
            [kind, timestamp, warningFlag],
            (err, rows) => {
                if (err) {
                    callback({ error: err });
                } else {
                    callback(rows);
                }
            }
        );
    }

    async getSessionsByArchiveIdAndUsername(archiveId: string, username: string): Promise<User[]> {
        return await this.runQuery<User[]>(`SELECT * FROM ${this.basDBName}.USERS WHERE USERNAME = ? AND ARCHIVE_ID = ?`, [
            username,
            archiveId,
        ]);
    }

    async getArchiveIDsfromPlatformArchiveIDsAndKind(platformArchiveIDs: string[], platformKind: string): Promise<string[]> {
        if (platformArchiveIDs.length === 0) {
            return [];
        }
        let rows = await this.runQuery<Array<{ BAS_ARCHIVE_ID: string }>>(
            `SELECT DISTINCT BAS_ARCHIVE_ID FROM ${this.basDBName}.PLATFORM_INFO WHERE PLATFORM_ARCHIVE_ID IN (?) AND PLATFORM_KIND = ?`,
            [platformArchiveIDs, platformKind]
        );
        return rows.map((row: { BAS_ARCHIVE_ID: string }) => row.BAS_ARCHIVE_ID);
    }

    async getArchivesfromBASArchiveIds(basArchiveIDs: string[]): Promise<PlatformData[]> {
        if (basArchiveIDs.length === 0) {
            return [];
        }
        return await this.runQuery<PlatformData[]>(`SELECT * FROM ${this.basDBName}.PLATFORM_INFO WHERE BAS_ARCHIVE_ID IN (?)`, [
            basArchiveIDs,
        ]);
    }

    async getSessionsFromArchiveIds(archiveIds: string[]): Promise<Array<{ ARCHIVE_ID: string; TOKEN: string }>> {
        if (archiveIds.length === 0) {
            return [];
        }
        return await this.runQuery<Array<{ ARCHIVE_ID: string; TOKEN: string }>>(
            `SELECT ARCHIVE_ID, TOKEN FROM ${this.basDBName}.USERS WHERE ARCHIVE_ID IN (?)`,
            [archiveIds]
        );
    }

    async updateSessionsPermissionsBySessionTokens({
        tokens,
        permissions,
    }: {
        tokens: string[];
        permissions: string;
    }): Promise<{ affectedRows: number }> {
        if (tokens.length === 0) {
            return { affectedRows: 0 };
        }
        const results = await this.runQuery<mysql.OkPacket>(`UPDATE ${this.basDBName}.USERS SET PERMISSIONS = ? WHERE TOKEN IN (?)`, [
            permissions,
            tokens,
        ]);
        return { affectedRows: results.affectedRows };
    }

    async getSessionsForUsernames({ usernames }: { usernames: string[] }): Promise<User[]> {
        if (usernames.length === 0) {
            return [];
        }
        return await this.runQuery<User[]>(`SELECT * FROM ${this.basDBName}.USERS WHERE USERNAME IN (?)`, [usernames]);
    }

    async updateSessionPermissionsByToken({ token, permissions }: { token: string; permissions: string }): Promise<void> {
        await this.runQuery<mysql.OkPacket>(`UPDATE ${this.basDBName}.USERS SET PERMISSIONS = ? WHERE TOKEN = ?`, [permissions, token]);
    }

    async updateSessionUserInfoByToken({ token, userInfo }: { token: string; userInfo: Record<string, any> }): Promise<void> {
        await this.runQuery<mysql.OkPacket>(`UPDATE ${this.basDBName}.USERS SET USERINFO = ? WHERE TOKEN = ?`, [
            JSON.stringify(userInfo),
            token,
        ]);
    }

    runQuery<T = any>(query: string, params: unknown[]): Promise<T> {
        return new Promise((resolve, reject) => {
            this.connection.query(query, params, (err: Error | null, results: T) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(results);
                }
            });
        });
    }
}

function makeSqlPlaceholdersForArray(arr: unknown[]): string {
    return arr
        .map(function () {
            return '?';
        })
        .join(', ');
}
