import { CloudWatch } from '@aws-sdk/client-cloudwatch';
import { Lambda } from '@aws-sdk/client-lambda';
import { JwtKmsService } from '@balsamiq/saas-utils';
import { BackendRTCClient } from '@balsamiq/serverconfig/lib/rtc-config.js';
import { BlockList } from './blocklist.ts';
import * as uuid from 'uuid';
import { REAL_TIME_CLOCK } from './clock.ts';
import { loadConfig } from './configLoader.ts';
import { initializeConnectors } from './connectors/connectors-registry.ts';
import { AwsKmsAdapter } from './kms_adapter.js';
import { Metrics } from './metrics.ts';
import { MySQLDriverPool } from './mysql-driver.ts';
import { makeRateLimiterConfiguration, RATE_LIMITERS } from './rate-limitation.ts';
import { RedisAdapter } from './redisAdapter.ts';
import { RtcAdapter } from './rtc-adapter.ts';
import { S3PermalinksImageStorageAdapter, S3Adapter, type BucketResidencyProps } from './s3adapter.ts';
import { makeServerUtils } from './server_utils.js';
import { SessionManager } from './session-manager.ts';
import { BuildNumberTracker } from './track_build_number.ts';
import { Wireframe2imageAdapter } from './wireframe2image-adapter.ts';
import { roleForAccessTable } from './role-for-access-table.ts';
import * as bmprUtilsMod from '@balsamiq/bmpr/lib/BmprUtils.js';
import { formatLogForElasticSearch, formatLogForConsole } from './logging-helpers.ts';
import { checkServerAPICredentialsFactory } from './utils.ts';

import { fileURLToPath } from 'url';
import path from 'path';
import { buildMainLogger } from '@balsamiq/logging';
import type { BASRequest } from './request-context.ts';
import { ConfluenceAdapter, HttpConfluenceApiClient } from './atlassian-adapter.ts';
import { StaticFileRepository } from './static-file-repository.ts';
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

export type AppContext = Awaited<ReturnType<typeof makeAppContext>>;

/**
 * Creates the application context which serves as a central container for all of the application's
 * services and parameters. These services and parameters are accessible throughout the entire
 * application, including request handlers and background "gardening" procedures.
 */
export async function makeAppContext() {
    const configInfo = loadConfig();
    const config = configInfo.config;

    const formatLoggerPayload = config.loggerOutputForElasticSearch ? formatLogForElasticSearch : formatLogForConsole;
    const logger = buildMainLogger((payload) => {
        process.stdout.write(formatLoggerPayload(payload));
    });

    logger.info(`Initializing BAS environment, for environment ${configInfo.environment}`, { action: 'initialization' });

    const clock = REAL_TIME_CLOCK;
    const metrics = new Metrics({
        logger,
        namespace: config.metricNamespace,
        buildNumber: config.buildNumber,
        dbCloudWatch: new CloudWatch({ region: config.mySQLConfig.region }),
        cloudWatch: new CloudWatch({ region: config.metricRegion }),
    });

    const permalinkImageStorageAdapter = new S3PermalinksImageStorageAdapter(
        config.permalinkS3Storage,
        config.defaultDataResidencyName,
        new S3Adapter()
    );

    const jwtKmsAdapter = new JwtKmsService(new AwsKmsAdapter({ region: config.kmsService.region }), {
        environment: config.kmsService.environment,
    });
    const dataResidencies = Object.fromEntries<BucketResidencyProps>(
        Object.entries(config.permalinkS3Storage).map(([dataResidencyName, permalinkS3StorageProps]) => [
            dataResidencyName,
            {
                bucketRegion: permalinkS3StorageProps.bucketRegion,
                bucketName: permalinkS3StorageProps.bucketName,
                baseDir: permalinkS3StorageProps.baseDir,
            },
        ])
    );
    const w2iAdapter = new Wireframe2imageAdapter({
        lambda: new Lambda({ region: config.w2iService.region }),
        name: config.w2iService.key_arn,
        dataResidencies: dataResidencies as Record<string, BucketResidencyProps>,
    });

    const mySqlSecrets = await config.mySQLConfig.credentialsSecret.getSecret(logger);

    const mySqlDriverInstance = await new MySQLDriverPool(
        mySqlSecrets.host,
        mySqlSecrets.username,
        mySqlSecrets.password,
        mySqlSecrets.port,
        metrics
    ).initialize(config.clusterSize, logger);

    const redisAdapter = new RedisAdapter({ port: config.redisPort, host: config.redisURL, database: config.redisDB }, logger);
    await redisAdapter.init();

    const sessionManager = new SessionManager(null, mySqlDriverInstance, metrics, configInfo, redisAdapter, logger);

    const serverPrefix = 'bas-';
    const serverID = serverPrefix + uuid.v1();

    let connectors!: ReturnType<typeof initializeConnectors>;
    function getConnector(kind: string | null) {
        return connectors.getConnector(kind);
    }

    const { secrets, websocketsUri, environmentAccess } = config.getRtcConfig();
    const backendRTCClient = new BackendRTCClient({
        platform: 'bas',
        jwtTokenDurationInMinutes: 18,
        logger,
        secrets,
        websocketsUri,
        environmentAccess,
    });

    const rtcAdapter = new RtcAdapter({ backendRTCClient, logger, metrics });

    const checkServerAPICredentials = checkServerAPICredentialsFactory(config);

    const buildNumberTracker = new BuildNumberTracker({
        metrics,
        logger,
        redisAdapter,
    });

    const serverUtils = makeServerUtils({
        sessionManager,
        mySqlDriverInstance,
        logger,
        getConnector,
        config,
        metrics,
        buildNumberTracker,
        rtcAdapter,
        clock,
    });

    const rateLimiterConfiguration = makeRateLimiterConfiguration(RATE_LIMITERS, metrics, redisAdapter, logger);

    const blockList = new BlockList({ redisAdapter: redisAdapter });

    const confluenceApiClient = new HttpConfluenceApiClient();
    const confluenceAdapter = new ConfluenceAdapter({
        redisAdapter,
        logger,
        config,
        clock,
        apiClient: confluenceApiClient,
    });

    connectors = initializeConnectors(
        sessionManager,
        logger,
        metrics,
        permalinkImageStorageAdapter,
        config,
        serverUtils,
        w2iAdapter,
        redisAdapter,
        confluenceAdapter,
        clock
    );

    const staticFileRepository = new StaticFileRepository(config.proxyConfig);

    return {
        config,
        configInfo,
        logger,
        metrics,
        mySqlDriverInstance,
        sessionManager,
        serverID,
        connectors,
        rtcAdapter,
        buildNumberTracker,
        serverUtils,
        permalinkImageStorageAdapter,
        rateLimiterConfiguration,
        blockList,
        jwtKmsAdapter,
        redisAdapter,
        clock,
        roleForAccessTable,
        bmprUtilsMod,
        staticFileRepository,
        srcDirName: __dirname,
        getConnector,
        checkServerAPICredentials,
        notifyBASRequest: null as null | ((req: BASRequest) => void), // This is used in TestContext
    };
}
