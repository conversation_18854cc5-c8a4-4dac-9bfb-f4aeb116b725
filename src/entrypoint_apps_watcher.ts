import { startCheckingLockedQueries } from './utils.ts';
import { makeAppContext } from './app-context.ts';
import { ConfluenceRedisExpirationKeyListener as runConfluenceRedisExpirationKeyListener } from './connectors/confluence.js';
import { JiraRedisExpirationKeyListener as runJiraRedisExpirationKeyListener } from './connectors/jira.js';

async function main() {
    const initializationData = await makeAppContext();
    const { config, metrics, sessionManager, serverUtils, redisAdapter } = initializationData;
    const logger = initializationData.logger.getLogger({ module: 'apps_watcher', action: 'watcherInitialization' });

    logger.info('Integrations watcher started ' + config.redisURL + ' at port ' + config.redisPort);

    if (runConfluenceRedisExpirationKeyListener) {
        runConfluenceRedisExpirationKeyListener(sessionManager, serverUtils, logger, metrics, config, false, redisAdapter);
    } else {
        logger.warn('Confluence watcher NOT started');
    }

    if (runJiraRedisExpirationKeyListener) {
        runJiraRedisExpirationKeyListener(sessionManager, serverUtils, logger, metrics, config, false, redisAdapter);
    } else {
        logger.warn('Confluence watcher NOT started');
    }

    if (startCheckingLockedQueries) {
        startCheckingLockedQueries(sessionManager, logger);
    } else {
        logger.warn('Locked queries watcher NOT started');
    }
}

main();
