import { z } from 'zod';
import type { DataResidencyName } from './environment-variables-schemas.ts';
import type { PermalinkImageFormat } from './s3adapter.ts';

// PlatformData
export const PlatformDataSchema = z.object({
    BAS_ARCHIVE_ID: z.string(),
    PLATFORM_SITE_ID: z.string().nullable(),
    PLATFORM_ARCHIVE_ID: z.string().nullable(),
    PLATFORM_KIND: z.string().nullable(),
    PLATFORM_ARCHIVE_NAME: z.string().nullable().optional(),
    TIMESTAMP: z.number(),
    PLATFORM_INFO: z.string().nullable().optional(),
    WARNING_FLAG: z.union([z.number(), z.boolean()]).optional(), // true/false can be returned as 1/0 by mysql driver
});

export type PlatformData = z.infer<typeof PlatformDataSchema>;

export function assertPlatformData(data: unknown): PlatformData {
    return PlatformDataSchema.parse(data);
}

export function isPlatformData(data: unknown): data is PlatformData {
    const result = PlatformDataSchema.safeParse(data);
    return result.success;
}

// PlatformInfo
export function parsePlatformInfo(platformInfoAsString: string | null | undefined): Record<string, unknown> {
    if (!platformInfoAsString) {
        return {};
    }
    try {
        return JSON.parse(platformInfoAsString);
    } catch (e) {
        return {};
    }
}

// PermalinkInfo
export const PermalinkInfoSchema = z
    .object({
        permalinkUUID: z.string().optional(),
        dataResidency: z.custom<DataResidencyName>().optional(),
        image: z
            .object({
                format: z.custom<PermalinkImageFormat>().optional(),
                quality: z.string().optional(),
                transparentBackground: z.boolean().optional(),
            })
            .optional(),
        edit: z.string().optional(),
        comment: z.string().optional(),
        view: z.string().optional(),
    })
    .passthrough();

export type PermalinkInfo = z.infer<typeof PermalinkInfoSchema>;

export function assertPermalinkInfo(data: unknown): PermalinkInfo {
    return PermalinkInfoSchema.parse(data);
}

export function isPermalinkInfo(data: unknown): data is PermalinkInfo {
    const result = PermalinkInfoSchema.safeParse(data);
    return result.success;
}

// PermalinkData
export const PermalinkDataSchema = z.object({
    permalinkID: z.string(),
    platformKind: z.string(),
    platformArchiveID: z.string(),
    platformSiteID: z.string(),
    resourceID: z.string(),
    branchID: z.string(),
    timestamp: z.number(),
    dirty: z.boolean(),
    permalinkKind: z.string(),
    permalinkInfo: PermalinkInfoSchema,
    platformInfo: z.record(z.string(), z.unknown()),
});

export type PermalinkData = z.infer<typeof PermalinkDataSchema>;

export function assertPermalinkData(data: unknown): PermalinkData {
    return PermalinkDataSchema.parse(data);
}

export function isPermalinkData(data: unknown): data is PermalinkData {
    const result = PermalinkDataSchema.safeParse(data);
    return result.success;
}
