import mysql from 'mysql';
import { callWithLegacyCallback, type BASLegacyCallback } from './calling-style.ts';
import type { Metrics } from './metrics.ts';
import type { Logger } from '@balsamiq/logging';

export const DB_IDENTIFIER_REGEXP = /^\w+$/;

const APPLICATION_LOCKS = ['MAIN_GARDENING', 'CLOUD_GARDENING'] as const;

export type ApplicationLockName = (typeof APPLICATION_LOCKS)[number];

interface LockResult {
    error?: string;
    lockAcquired?: boolean;
}

/**
 * MySQL's GET_LOCK will acquire a global named lock, with or without waiting for it to be available.
 * Documentation: GET_LOCK (https://dev.mysql.com/doc/refman/5.7/en/locking-functions.html#function_get-lock)
 */
export function acquireApplicationLock(
    connection: mysql.Connection | mysql.PoolConnection,
    lockName: ApplicationLockName,
    wait: boolean,
    cb: (result: LockResult) => void
): void {
    if (!APPLICATION_LOCKS.includes(lockName)) {
        throw new Error(`Unknown application lock ${lockName}`);
    }
    connection.query('SELECT GET_LOCK(?, ?) AS result', [lockName, wait ? -1 : 0], function (err, rows) {
        if (err) {
            cb({ error: err.message });
        } else {
            cb({ lockAcquired: !!rows[0].result });
        }
    });
}

interface ConnectionOptions {
    host: string;
    user: string;
    password: string;
    port: number;
    charset: string;
    connectionLimit?: number;
}

/**
 * MySQL driver pool for managing database connections.
 */
export class MySQLDriverPool {
    connectionOptions: ConnectionOptions;
    metrics: Metrics;
    pool!: mysql.Pool;

    constructor(host: string, user: string, password: string, port: number, metrics: Metrics) {
        this.connectionOptions = {
            host,
            user,
            password,
            port,
            charset: 'utf8mb4',
        };
        this.metrics = metrics;
    }

    /**
     * Initializes the MySQL connection pool by determining the appropriate connection limit
     * based on the server's max_connections setting and the size of the cluster.
     *
     * @param clusterSize - The number of BAS instances in the cluster.
     * @returns A promise that resolves to the initialized MySQLDriverPool.
     */
    initialize(clusterSize: number, logger: Logger): Promise<MySQLDriverPool> {
        return new Promise((resolve, reject) => {
            const connection = mysql.createConnection(this.connectionOptions);
            connection.connect();

            // In the default RDS parameter group `max_connections` is set to `{DBInstanceClassMemory/12582880}`,
            // but since there's no easier way to determine the value of `DBInstanceClassMemory`,
            // we're fetching the final value from the DB at runtime. Maybe, someday, CDK will support this,
            // then we can pass its value as an environment variable during deployment.
            //
            // https://repost.aws/knowledge-center/rds-mysql-max-connections
            // https://docs.aws.amazon.com/AmazonRDS/latest/UserGuide/CHAP_Limits.html#RDS_Limits.MaxConnections
            //
            connection.query(`SHOW GLOBAL VARIABLES LIKE 'max_connections'`, (error, results, _fields) => {
                if (error) {
                    logger.error(`Error fetching max_connections: ${error.message}`, error);
                    return reject(new Error(`Error fetching max_connections`));
                }

                for (const result of results) {
                    if (result.Variable_name === 'max_connections') {
                        // Calculating the DB pool connection limit per BAS "instance" ourselves,
                        // based on the number of maximum connections allowed by the DB divided by
                        // the number of BAS "instances" running in the BAS "cluster", which gives us
                        // the average number of connections each BAS "instance" should use for
                        // their connection pool.
                        this.connectionOptions.connectionLimit = result.Value / clusterSize;

                        if (!this.connectionOptions.connectionLimit) {
                            return reject(
                                new Error(
                                    `Invalid value was calculated for the DB pool connection limit (${this.connectionOptions.connectionLimit}).`
                                )
                            );
                        }

                        this.pool = mysql.createPool(this.connectionOptions);

                        return resolve(this);
                    }
                }

                return reject(new Error(`Can't find 'max_connections' variable in DB.`));
            });

            connection.end();
        });
    }

    waitForMySQLToBeReady(callback: BASLegacyCallback<void>): void {
        let tries = 3;
        let error: mysql.MysqlError = {} as mysql.MysqlError;
        const pool = this.pool;

        function tryGetConnection() {
            if (tries === 0) {
                callback({ error: error.message });
                return;
            }
            tries = tries - 1;

            pool.getConnection(function (err, connection) {
                if (err) {
                    error = err; // save the error
                    if (err.code === 'ECONNREFUSED') {
                        // Handle refused connection by retrying
                        setTimeout(tryGetConnection, 1000);
                    } else {
                        callback({ error: err.message }); // Unhandled error are returned immediately
                    }
                } else {
                    connection.release();
                    callback();
                }
            });
        }

        tryGetConnection();
    }

    getConnection(callback?: BASLegacyCallback<mysql.PoolConnection>): void {
        const timer = this.metrics.trackTimeInterval('mysql-getConnection');
        this.pool.getConnection((err, connection) => {
            if (err) {
                const resp: { error: string; busy?: boolean } = { error: err.message };
                // TODO: for now every kind of connection issue to db is treated as temporary error
                //if (err.code == "ECONNRESET" || err.code == "PROTOCOL_CONNECTION_LOST") {
                resp.busy = true;
                //}
                callback && callback(resp);
            } else {
                timer.stop();
                callback && callback(connection);
            }
        });
    }

    getActivePoolConnection(): number {
        // These properties are not in the type definitions but exist in the actual implementation
        // @ts-ignore
        return this.pool._allConnections.length - this.pool._freeConnections.length;
    }

    releaseConnection(connection: mysql.PoolConnection, callback?: BASLegacyCallback<{}>): void {
        const obj: { error?: string } = {};
        try {
            connection.release();
        } catch (e) {
            obj.error = e instanceof Error ? e.message : 'unknown exception releasing MySql drive pool connection';
        }
        callback && callback(obj);
    }

    async withConnection<T>(fn: (connection: mysql.PoolConnection) => Promise<T>): Promise<T> {
        const connection = await callWithLegacyCallback<mysql.PoolConnection>((cb) => this.getConnection(cb));
        try {
            return await fn(connection);
        } finally {
            try {
                await callWithLegacyCallback<{}>((cb) => this.releaseConnection(connection, cb));
            } catch {
                // Ignore errors while releasing connection
            }
        }
    }

    shutdown(callback: BASLegacyCallback<{}>): void {
        this.pool.end(function (err) {
            // all connections in the pool have ended
            if (err) {
                _finalizeCallback('', err, null, callback);
            } else {
                callback({});
            }
        });
    }
}

/**
 * Single MySQL driver for direct connection operations.
 */
export class MySQLDriver {
    connection: mysql.Connection;
    type: string;

    /**
     * Creates a new MySQLDriver instance.
     *
     * @param connection - The MySQL connection to use.
     */
    constructor(connection: mysql.Connection) {
        this.connection = connection;
        this.type = 'mysql';
    }

    /**
     * Creates a database from a buffer.
     *
     * @param id - The database ID.
     * @param buffer - The buffer containing the database data.
     * @param callback - The callback function.
     */
    createFromBuffer(id: string, buffer: Buffer, callback: BASLegacyCallback<{}>): void {
        //not implemented here
    }

    /**
     * Gets a buffer representation of a database.
     *
     * @param id - The database ID.
     * @param callback - The callback function.
     */
    getBuffer(id: string, callback: BASLegacyCallback<{}>): void {
        //not implemented here
    }

    /**
     * Destroys a database.
     *
     * @param id - The database ID.
     * @param callback - The callback function.
     */
    destroy(id: string, callback: BASLegacyCallback<{}>): void {
        this.destroyExt(id, false, callback);
    }

    /**
     * Extended destroy method with force option.
     *
     * @param id - The database ID.
     * @param force - Whether to force destruction by unlocking tables first.
     * @param callback - The callback function.
     */
    destroyExt(id: string, force: boolean, callback: BASLegacyCallback<{}>): void {
        if (!id.match(DB_IDENTIFIER_REGEXP)) {
            const err = new Error('Invalid database ID');
            _finalizeCallback(id, err, null, callback);
            return;
        }

        const _dropDb = () => {
            this.connection.query('DROP DATABASE IF EXISTS ' + id, (err) => {
                if (err) {
                    _finalizeCallback(id, err, null, callback);
                } else {
                    callback({});
                }
            });
        };

        if (force) {
            this.connection.query('UNLOCK TABLES', (err) => {
                if (err) {
                    _finalizeCallback(id, err, null, callback);
                } else {
                    _dropDb();
                }
            });
        } else {
            _dropDb();
        }
    }

    /**
     * Opens a database.
     *
     * @param id - The database ID.
     * @param openParams - The parameters for opening the database.
     * @param callback - The callback function.
     */
    open(id: string, openParams: 'readWriteOrCreate' | 'readWrite' | 'read', callback: BASLegacyCallback<{}>): void {
        if (!id.match(DB_IDENTIFIER_REGEXP)) {
            const err = new Error('Invalid database ID');
            _finalizeCallback(id, err, null, callback);
            return;
        }

        if (openParams === 'readWriteOrCreate') {
            this.connection.query('CREATE DATABASE IF NOT EXISTS ' + id + ' CHARACTER SET utf8 COLLATE utf8_general_ci', (err) => {
                if (err) {
                    _finalizeCallback(id, err, null, callback);
                } else {
                    callback({});
                }
            });
        } else if (openParams === 'readWrite') {
            this.connection.changeUser(
                {
                    database: id,
                },
                (err) => {
                    if (err) {
                        _finalizeCallback(id, err, null, callback);
                    } else {
                        callback({});
                    }
                }
            );
        }
    }

    /**
     * Serializes database operations.
     *
     * @param id - The database ID.
     * @param callback - The callback function.
     */
    serialize(id: string, callback: BASLegacyCallback<{}>): void {
        // Implementation is commented out in the original code
        callback({});
    }

    /**
     * Runs a query on the database.
     *
     * @param id - The database ID.
     * @param query - The SQL query to run.
     * @param values - The values to bind to the query.
     * @param callback - The callback function.
     */
    run(id: string, query: string, values: any[], callback: BASLegacyCallback<{ affectedRows: number }>): void {
        if (!id.match(DB_IDENTIFIER_REGEXP)) {
            const err = new Error('Invalid database ID');
            _finalizeCallback(id, err, null, callback);
            return;
        }

        this.connection.changeUser(
            {
                database: id,
            },
            (err) => {
                if (err) {
                    _finalizeCallback(id, err, null, callback);
                } else {
                    this.connection.query(query, values, (err, rows) => {
                        if (err) {
                            _finalizeCallback(id, err, 'ER_BAD_DB_ERROR', callback);
                        } else {
                            callback({ affectedRows: (rows as mysql.OkPacket).affectedRows });
                        }
                    });
                }
            }
        );
    }

    /**
     * Gets all rows from a query.
     *
     * @param id - The database ID.
     * @param query - The SQL query.
     * @param values - The values to bind to the query.
     * @param callback - The callback function.
     */
    all(id: string, query: string, values: any[], callback: BASLegacyCallback<{ rows: any[] }>): void {
        if (!id.match(DB_IDENTIFIER_REGEXP)) {
            const err = new Error('Invalid database ID');
            _finalizeCallback(id, err, null, callback);
            return;
        }

        this.connection.changeUser(
            {
                database: id,
            },
            (err) => {
                if (err) {
                    _finalizeCallback(id, err, null, callback);
                } else {
                    this.connection.query(query, values, (err, rows) => {
                        if (err) {
                            _finalizeCallback(id, err, 'ER_BAD_DB_ERROR', callback);
                        } else {
                            callback({ rows: rows as any[] });
                        }
                    });
                }
            }
        );
    }

    /**
     * Gets a single row from a query.
     *
     * @param id - The database ID.
     * @param query - The SQL query.
     * @param values - The values to bind to the query.
     * @param callback - The callback function.
     */
    get(id: string, query: string, values: any[], callback: BASLegacyCallback<{ row?: any }>): void {
        if (!id.match(DB_IDENTIFIER_REGEXP)) {
            const err = new Error('Invalid database ID');
            _finalizeCallback(id, err, null, callback);
            return;
        }

        this.connection.changeUser(
            {
                database: id,
            },
            (err) => {
                if (err) {
                    _finalizeCallback(id, err, null, callback);
                } else {
                    this.connection.query(query, values, (err, rows) => {
                        if (err) {
                            _finalizeCallback(id, err, 'ER_BAD_DB_ERROR', callback);
                        } else if (Array.isArray(rows) && rows[0]) {
                            callback({ row: rows[0] });
                        } else {
                            callback({});
                        }
                    });
                }
            }
        );
    }

    /**
     * Closes the database connection.
     *
     * @param id - The database ID.
     * @param callback - The callback function.
     */
    close(id: string, callback: BASLegacyCallback<{}>): void {
        callback({});
    }
}

/**
 * Helper function to finalize callback with error formatting.
 *
 * @param id - The database ID associated with the error.
 * @param err - The error object.
 * @param concurrentError - The concurrent error code to check.
 * @param callback - The callback function to call with the formatted error.
 */
function _finalizeCallback(
    id: string,
    err: Error | mysql.MysqlError,
    concurrentError: string | null,
    callback: BASLegacyCallback<any>
): void {
    const message = err.message ? err.message : JSON.stringify(err);
    const resp: { error: string; busy?: boolean; zombie_db?: string[] } = { error: message };

    if (concurrentError && 'code' in err && err.code === concurrentError) {
        resp.busy = true;
        resp.zombie_db = [id];
    }

    callback(resp);
}
