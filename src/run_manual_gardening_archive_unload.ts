import type { Logger } from '@balsamiq/logging';
import { makeAppContext } from './app-context.ts';
import { type ServerUtils } from './server_utils.js';
import type { SessionManager } from './session-manager.ts';
import type { BASLegacyCallback } from './calling-style.ts';

function doJob(serverUtils: ServerUtils, sessionManager: SessionManager, logger: Logger, callback: BASLegacyCallback<void>): void {
    logger = logger.getLogger({ action: 'manualGardeningJob', module: 'gar' });
    sessionManager.createSession(logger, 'manualGardeningJob', function (obj) {
        let sessionData;
        if (obj.error) {
            logger.error('manualGardeningJob: ' + obj.error);
            callback({ error: obj.error });
            return;
        }
        sessionData = obj;
        serverUtils.unloadUnusedArchiveJob(
            logger,
            sessionData,
            {
                connectors: ['cloud'],
                debugPrint: function (msg: unknown) {
                    console.log(msg);
                },
                maxBulkSize: 1,
                // onlyDoArchiveID: 'cloud_6b2ed850_123c_11e8_9983_7b9e3dfc8ec9',
                skipProcessing: true,
                skipDelete: true,
                skipSettingWarningFlag: true,
            },
            callback
        );
    });
}

async function main() {
    let initializationData = await makeAppContext();
    let logger = initializationData.logger.getLogger({ action: 'main-garderning', module: 'gar' });
    let sessionManager = initializationData.sessionManager;
    let serverUtils = initializationData.serverUtils;

    logger.info('Unloading unused archives started');

    let tsStarted = new Date().getTime();
    doJob(serverUtils, sessionManager, logger, function () {
        let tsEnded = new Date().getTime();
        let elapsed = tsEnded - tsStarted;
        logger.info('Unloading unused archives ended. Elapsed ' + (elapsed / 1000 / 60).toFixed(1) + ' min');
        // Forcibly quit the process
        process.exit(0);
    });
}

main().catch((err) => {
    console.error(err);
    process.exit(1);
});
