/* global testContext */
/* eslint-env mocha */

import { expect } from 'chai';

import assert from 'assert';
import type { Application } from 'express';
import path from 'path';
import { fileURLToPath } from 'url';
import * as jwt from '../connectors/lib/jwt.js';
import con from '../constants.ts';
import { BASApiClient } from './utils/apiclient.ts';
import type { CloudServerMock } from './utils/mocks.ts';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

suite('Permissions', function () {
    const kind = `cloud`;
    const userInfo = {
        // User info
        userId: `cloud-user`,
        name: `User`,
        displayName: `user`,
        avatarURL: `https://placehold.it/60x60`,
    };

    const jwtsecret = 'foobar';
    const getPlatformToken = (extra: unknown) => {
        assert(extra !== null && typeof extra === 'object', 'extra must be an object');
        assert('siteId' in extra, 'siteId is required');
        assert('projectId' in extra, 'projectId is required');
        assert('role' in extra, 'role is required');
        const { siteId, projectId, role } = extra;
        return jwt.encode(
            {
                exp: Date.now() + 3600,
                sub: 'create_archive',
                iss: 'cloud-server',
                siteId,
                projectId,
                role,
            },
            jwtsecret,
            'HS256'
        );
    };

    const projectId = 1;
    const siteID = 1000;

    const platformTokenExtra = {
        siteId: siteID,
        projectId: projectId,
        role: 'ADMIN',
    };

    const rtcKind = 'new_rtc';

    let app: Application;
    let cloudServerMock!: CloudServerMock;
    let bas!: BASApiClient;

    async function createTestProject() {
        const createRes = await bas.createMultipart(path.join(__dirname, '../connectors/templates/2.0/webdemo.bmpr'));
        expect(createRes.body).to.have.property('platformArchiveID', `${projectId}`);
        let openRes = await bas.open();
        const { token } = openRes.body;
        return token;
    }

    setup(async function () {
        app = testContext.app;
        cloudServerMock = testContext.cloudServerMock;
        bas = new BASApiClient(app, {
            kind,
            userInfo,
            platformArchiveID: projectId,
            siteID,
            getPlatformToken,
            adminUser: testContext.basAdminUser,
            adminSecret: testContext.basAdminSecret,
            platformTokenExtra,
        });
    });

    test('Create project as ADMIN, and can change it', async function () {
        const token = await createTestProject();
        await bas.setArchiveAttributes({ token, attributes: { projectName: 'new name' } });
    });

    test('Create project as ADMIN, then set session to NO_ACCESS, and cannot change it anymore', async function () {
        const token = await createTestProject();
        await testContext.withDBSession(async (dbConnector) => {
            await dbConnector.updateSessionsPermissionsBySessionTokens({ tokens: [token], permissions: `${con.ROLE_NO_ACCESS}` });
        });
        let response = await bas.setArchiveAttributes({ token, attributes: { projectName: 'new name' } });
        expect(response.body.error).to.equal('Not authorized');
        testContext.expectErrorsInLogs((err) => err.message.includes('Not authorized: ROLE_NO_ACCESS < ROLE_ADMIN'));
    });
});
