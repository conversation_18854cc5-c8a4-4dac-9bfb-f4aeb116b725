/* global testContext */
/* eslint-env mocha */

import superagent from 'superagent';

import { expect } from 'chai';
import express from 'express';
import supertest from 'supertest';
import { pipeToNewRequest } from '../utils.ts';
import { Writable } from 'stream';
import type { StaticServerMock } from './utils/mocks.ts';

suite('Reverse Proxy', () => {
    let staticServerMock!: StaticServerMock;

    setup(async function () {
        staticServerMock = testContext.staticServerMock;
    });

    test('StaticServerMock works fine', async () => {
        let res = await superagent.get(staticServerMock.serverURL + '/chunks?c=5&delay=1').buffer();
        expect(res.statusCode).to.eq(200);
    });

    suite('Pipe function', function () {
        let pipeFunctionFinished: Promise<unknown>;
        let reverseProxyTimeout: number | undefined = undefined;
        let reverseProxyApp: express.Application;

        setup(async function () {
            // Setup a new httpServer that uses the reverse-proxy function to serve
            // the content of StaticServerMock
            reverseProxyApp = express();

            let logger = testContext.logger.getLogger({ test: 'reverseproxy' });
            reverseProxyTimeout = undefined;
            reverseProxyApp.get('/reverse', (req, res) => {
                let url = req.query.url;
                pipeFunctionFinished = new Promise((resolve, reject) => {
                    try {
                        // This is the reverse proxy function that will be tested
                        pipeToNewRequest(
                            req,
                            res,
                            {
                                url: `${url}`,
                                timeout: reverseProxyTimeout,
                                incomingResHandler: (incomingRes) => {
                                    delete incomingRes.headers['x-header-to-delete'];
                                },
                            },
                            logger,
                            (result) => {
                                if ('error' in result) {
                                    res.status(500).end();
                                }
                                resolve(result);
                            }
                        );
                    } catch (err) {
                        console.error(err);
                        reject(err);
                    }
                });
            });
        });

        test('Handle bad addr network error', async function () {
            const badResourceAddress = 'http://1235.23.232.111';

            await supertest(reverseProxyApp)
                .get('/reverse')
                .query({ url: badResourceAddress + '/chunks?c=5&delay=1' });

            expect(testContext.loglines.some((x) => x.includes('Pipe error'))).to.be.true;
            expect(await pipeFunctionFinished).to.have.key('error');

            testContext.expectErrorsInLogs((err) => err.message.includes('Pipe error'));
        });

        test('Handle connection refused', async function () {
            const badResourceAddress = 'http://127.0.0.1:9999';

            await supertest(reverseProxyApp)
                .get('/reverse')
                .query({ url: badResourceAddress + '/chunks?c=5&delay=1' });

            expect(testContext.loglines.some((x) => x.includes('outgoing request error'))).to.be.true;
            expect(await pipeFunctionFinished).to.have.key('error');

            testContext.expectErrorsInLogs((err) => err.message.includes('outgoing request error'));
        });

        test('Handle connection timeout', async function () {
            reverseProxyTimeout = 200;
            await supertest(reverseProxyApp)
                .get('/reverse')
                .query({ url: staticServerMock.serverURL + `/chunks?c=5&wait=${reverseProxyTimeout * 2}` });

            expect(testContext.loglines.some((x) => x.includes('Timeout'))).to.be.true;
            expect(await pipeFunctionFinished).to.have.key('error');

            testContext.expectErrorsInLogs((err) => err.message.includes('Timeout'));
        });

        test('Handles aborted requests', async function () {
            await new Promise<void>((resolve) => {
                const req = supertest(reverseProxyApp)
                    .get('/reverse')
                    .query({ url: staticServerMock.serverURL + '/chunks?c=5' });

                const writableStream = new Writable({
                    write(_chunk, _encoding, callback) {
                        req.abort(); // Abort after first chunk received
                        callback();
                        resolve();
                    },
                });

                req.pipe(writableStream);
                req.on('response', (res) => {
                    res.on('error', () => {
                        // Response is aborted. Ignore the connection truncated error
                    });
                });
            });

            expect(await pipeFunctionFinished).to.be.deep.eq({ error: 'aborted' });
            expect(testContext.loglines.some((x) => x.includes('client closed connection'))).to.be.true;

            testContext.expectErrorsInLogs((err) => err.message.includes('pipeToNewRequest: unexpected error piping the request'));
        });

        test('Returns expected data', async function () {
            const response = await supertest(reverseProxyApp)
                .get('/reverse')
                .query({ url: staticServerMock.serverURL + '/chunks?c=5&delay=1' });
            let requestLibraryResult = response.text;

            expect(requestLibraryResult.toString()).to.contain(staticServerMock.nextChunkData);
            expect(await pipeFunctionFinished).to.be.deep.eq({});
        });

        test('Handle redirect to expected data', async function () {
            const response = await supertest(reverseProxyApp)
                .get('/reverse')
                .query({ url: staticServerMock.serverURL + '/redirect-to-chunks?c=5&delay=1' });
            let requestLibraryResult = response.text;

            expect(requestLibraryResult.toString()).to.contain(staticServerMock.nextChunkData);
            expect(await pipeFunctionFinished).to.be.deep.eq({});
        });

        test('Reverse proxy forward response headers', async function () {
            staticServerMock.responseHeaders = { 'X-Reverse-Proxy-Test': 'true', 'X-Header-To-Delete': 'true' };

            const response = await supertest(reverseProxyApp)
                .get('/reverse')
                .query({ url: staticServerMock.serverURL + '/chunks?c=5&delay=1' });

            expect(response.header).to.have.property('x-reverse-proxy-test', 'true');
            expect(response.header['x-header-to-delete']).to.be.undefined;
            expect(await pipeFunctionFinished).to.be.deep.eq({});
        });

        test('Reverse proxy forward request headers', async function () {
            await supertest(reverseProxyApp)
                .get('/reverse')
                .query({ url: staticServerMock.serverURL + '/chunks?c=5&delay=1' })
                .set('X-Reverse-Proxy-Test', 'true');

            expect(staticServerMock.requestHeaders).to.have.property('x-reverse-proxy-test', 'true');
            expect(await pipeFunctionFinished).to.be.deep.eq({});
        });

        test('Reverse proxy supports streaming', async function () {
            const numOfChunks = 5;
            const req = supertest(reverseProxyApp)
                .get('/reverse')
                .query({ url: staticServerMock.serverURL + `/chunks?c=${numOfChunks}&delay=1` });

            let chunks: Buffer[] = [];
            const streamHandler = new Writable({
                write(chunk: Buffer, _encoding: string, callback) {
                    chunks.push(chunk);
                    callback();
                },
            });

            await new Promise((resolve) => {
                req.pipe(streamHandler).on('close', resolve);
            });

            expect(chunks).to.have.lengthOf(numOfChunks);

            let httpRequestResult = Buffer.concat(chunks);
            expect(httpRequestResult.toString()).to.contain(staticServerMock.nextChunkData);

            expect(await pipeFunctionFinished).to.be.deep.eq({});
        });
    });
});
