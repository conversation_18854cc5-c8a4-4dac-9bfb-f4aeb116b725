import { BASApiClient } from './utils/apiclient.ts';
import type { Application } from 'express';
import jwt from 'atlassian-jwt';
import * as chai from 'chai';
import { expect } from 'chai';
import chaiAsPromised from 'chai-as-promised';
import { ConfluenceConnector, doCheckPermissionAndUpdateUserSession } from '../connectors/confluence.js';
import assert from 'assert';
import { ConfluenceRedisExpirationKeyListener as runConfluenceRedisExpirationKeyListener } from '../connectors/confluence.js';
import { callWithLegacyCallback } from '../calling-style.ts';
import { sleep } from '@balsamiq/saas-utils';

chai.use(chaiAsPromised);

suite('Confluence', function () {
    const kind = `confluence`;

    const userInfo = {
        name: `557058:dc2e8c13-7d13-428d-ad89-b1adb52e5d1e`,
        displayName: `user`,
        avatarURL: `https://placehold.it/60x60`,
        email: `<EMAIL>`,
    };

    // The following key pair is used to simulate signature of the JWT token coming from Atlassian.
    const privateKey = `**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`;
    const publicKey = `-----BEGIN PUBLIC KEY-----
MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCznkDKD9XQo+f3ZJtJDyO4bxY7
AqCVds7qqBD5dajvLL65shGrWMCc7JJd6mxe1Mbfv56nELuh6SFwtUQ3x0hTuNAK
/ipLfCRJzV9WeYo/hI95QvX/e4DVTkpC35eO/MLSJu5awr7a/9JOI+xneOjqIUd9
jkNcSSd/YiezQKzwPwIDAQAB
-----END PUBLIC KEY-----`;

    const clientKey = 'cca88a7e-8310-30ce-a914-9dd1244322ff';
    const appKey = 'com.balsamiq.mockups.confluence.staging';
    const sharedSecret = 'ATCO2lFgVfxDeTnTRd2ZXipjIKzkJDgBnsucC1i311_WHCdL934x7juzxO6nHxDCenEO_m_0B3gOKEi25vAd1NgcNA3F1A4827';
    const baseUrl = 'https://some-customer-server.atlassian.net/wiki';
    const contentID = '36175875';
    const siteID = `${clientKey}_${contentID}`;
    const attachmentID = `att${contentID}`;

    // This is the payload posted from Confluence to BAS at installation time
    const installationData = {
        key: appKey,
        clientKey,
        oauthClientId: Buffer.from(`{"hostKey":"${clientKey}","addonKey":"${appKey}"}`).toString('base64'),
        publicKey:
            'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAorAIfPTE8mqeR1cMEkHlUsKPGpxoJMTq0+GGeZSVBC1glxbX1Jg/dsA6Rw6tFa1P/vyDp6NKuLomjmxjSeqpNadRYjY96KTa7NJTgPmS0bwmUp9c+ThwmVokbSWAYmEFPhwjbEHRzjUPubu3ihhuvj/IXsK+dl2/pJehZEAHZ9CunutbFVgp+buonc05CE325R6Ez1OG+Dvk0nMY4zCV5qP4Io/85r4q/hTYJBdjJ9R4jZcfDH7G5m6+itGuFZ1+5MR8AqD8XfVOqGieTOugZdsJ9TkTg5lWtXuI9LUhk9bt2KdnsMM4uL+460R1UEGi2GJ8D4nPe3cIygy7WOo3RwIDAQAB',
        sharedSecret,
        serverVersion: '6452',
        pluginsVersion: '1000.0.0.cb8eab2fafe3',
        baseUrl,
        productType: kind,
        description: `Atlassian Confluence at ${baseUrl}`,
        eventType: 'installed',
        displayUrl: `${baseUrl}`,
    };

    // This is posted from Confluence to BAS at open time
    const platformInfo = {
        contentID,
        iss: clientKey,
        licenceStatus: 'active',
        draftStatus: true,
        jwt: 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiI1NTcwNTg6ZGMyZThjMTMtN2QxMy00MjhkLWFkODktYjFhZGI1MmU1ZDFlIiwicXNoIjoiMzhjNTA0NjQ2YWE1ODUwMjQzNTNkMGE2MGNiMDY4MDY3OWU2ZmM4M2E3NDA5NDJjZDllOTA1ZmJiMTczOGU2OSIsImlzcyI6ImNjYTg4YTdlLTgzMTAtMzBjZS1hOTE0LTlkZDEyNDQzMjJmZiIsImNvbnRleHQiOnt9LCJleHAiOjE3NDcwNTYwNzcsImlhdCI6MTc0NzA1NTg5N30.IdSQBs3jPvjChZpWtddABBQAPg_HABZaVMOScmmlFY0',
        archiveRevisionOnPlatform: 11,
    };

    const getPlatformToken = (extra: unknown) => {
        const iat = Math.round(Date.now() / 1000);
        const exp = iat + 60 * 60;
        const token = jwt.encodeSymmetric(
            {
                sub: userInfo.name,
                qsh: '42f1b76ea77ac424281d59a9aa2a74f5c3cacedef1b928b41fa48943c1e1d28e', // this changes depending on the incoming request.
                iss: clientKey,
                context: {},
                exp,
                iat,
            },
            installationData.sharedSecret,
            jwt.SymmetricAlgorithm.HS256
        );

        return token;
    };

    let app: Application;
    let bas: BASApiClient;

    setup(async function () {
        app = testContext.app;
        bas = new BASApiClient(app, {
            siteID,
            platformArchiveID: attachmentID,
            platformInfo: JSON.stringify(platformInfo),
            userInfo,
            kind,
            getPlatformToken,
            adminUser: testContext.basAdminUser,
            adminSecret: testContext.basAdminSecret,
        });
    });

    suite('Install connector', function () {
        setup(async function () {
            const res = await bas.postCustomAPI({
                kind: 'confluence',
                api: 'installed',
                data: installationData,
            });
            expect(res.status).to.equal(200);

            const verifyInstallationResult = await testContext.withDBSession(async (dbSession) => {
                return await callWithLegacyCallback<Record<string, unknown>>((cb) =>
                    dbSession.getConnectorData('confluence', installationData.clientKey, cb)
                );
            });
            expect(verifyInstallationResult).to.deep.equal(installationData);

            // Configure the mock atlassian server based on the installation data
            testContext.mockConfluenceApiClient.setFetchOAuth2TokenResponse(200, { sharedSecret, userAccountId: userInfo.name, clientKey });
            testContext.mockConfluenceApiClient.setGetUserDetailsResponse(200, {
                accountId: userInfo.name,
                displayName: userInfo.displayName,
                email: userInfo.email,
                profilePicturePath: userInfo.avatarURL,
            });
            testContext.mockConfluenceApiClient.setConvertIdsToTypesResponse(200, {
                results: {
                    [contentID]: 'page',
                },
            });
            testContext.mockConfluenceApiClient.setAttachmentResponse(200, {
                downloadLink: `/download/attachments/${contentID}/BalsamiqProject_${contentID}.bmpr?version=1&modificationDate=*************&cacheVersion=1&api=v2`,
            });
            testContext.mockConfluenceApiClient.setPublicKey(publicKey);
        });

        test('uninstall connector (newInstallationCycle)', async function () {
            const res = await bas.postCustomAPI({
                kind: 'confluence',
                api: 'uninstalled',
                data: installationData,
                queryParams: {
                    jwt: jwt.encodeAsymmetric(
                        {
                            iss: clientKey,
                            aud: [testContext.config.baseUrl],
                        },
                        privateKey,
                        jwt.AsymmetricAlgorithm.RS256,
                        {
                            kid: 'any_kid_is_fine',
                        }
                    ),
                },
            });
            expect(res.status).to.equal(200);
        });

        test('uninstall connector', async function () {
            const res = await bas.postCustomAPI({
                kind: 'confluence',
                api: 'uninstalled',
                data: installationData,
                queryParams: {
                    jwt: jwt.encodeAsymmetric(
                        {
                            iss: clientKey,
                            aud: [testContext.config.baseUrl],
                        },
                        privateKey,
                        jwt.AsymmetricAlgorithm.RS256,
                        {
                            kid: 'any_kid_is_fine',
                        }
                    ),
                },
            });
            expect(res.status).to.equal(200);
        });

        suite('Open Project', function () {
            setup(async function () {
                let open = await bas.open({ skipThumbnailImage: true });
                expect(open.status).to.equal(200);
            });

            test('test setup works fine', async function () {
                // Just test the setup
            });
        });

        suite('Expiration key listener', function () {
            test('Setup expiration listener', async function () {
                await runConfluenceRedisExpirationKeyListener(
                    testContext.sessionManager,
                    testContext.serverUtils,
                    testContext.logger,
                    testContext.metrics,
                    testContext.config,
                    true,
                    testContext.redisAdapter
                );
            });

            test('doCheckPermissionAndUpdateUserSession for unknown user', async function () {
                const sessionToken = 'unknown_session_token';
                expect(
                    callWithLegacyCallback((cb) =>
                        doCheckPermissionAndUpdateUserSession(
                            testContext.sessionManager,
                            testContext.serverUtils,
                            testContext.logger,
                            sessionToken,
                            testContext.config,
                            cb
                        )
                    )
                ).to.eventually.be.rejected;
            });
        });

        suite('endpoint API', function () {
            const basApi = '/confluence/endpoint/';
            const resourceName = 'test.txt';

            // This creates a JWT token for Confluence API calls.
            // See https://bitbucket.org/atlassian/atlassian-jwt-js/src/master/
            const req: jwt.Request = jwt.fromMethodAndUrl('GET', basApi + resourceName);
            const now = Math.round(Date.now() / 1000); // Unix time in seconds
            const tokenData = {
                iss: installationData.clientKey,
                iat: now,
                exp: now + 180,
                qsh: jwt.createQueryStringHash(req), // [Query String Hash](https://developer.atlassian.com/cloud/jira/platform/understanding-jwt/#a-name-qsh-a-creating-a-query-string-hash)
            };
            const token = jwt.encodeSymmetric(tokenData, installationData.sharedSecret, jwt.SymmetricAlgorithm.HS512);

            test('JWT missing', async function () {
                const res = await bas.getCustomAPI({
                    kind: 'confluence',
                    api: `endpoint/${resourceName}`,
                    params: {},
                });
                expect(res.status).to.equal(401);
                testContext.expectErrorsInLogs((record) => record.message.includes('missing JWT token'));
            });

            test('JWT bad', async function () {
                const res = await bas.getCustomAPI({
                    kind: 'confluence',
                    api: `endpoint/${resourceName}`,
                    params: {
                        jwt: 'BAD_TOKEN',
                    },
                });
                expect(res.status).to.equal(401);
                testContext.expectErrorsInLogs((record) => record.message.includes('BAD_TOKEN'));
            });

            test('resource does not match', async function () {
                const res = await bas.getCustomAPI({
                    kind: 'confluence',
                    api: `endpoint/BAD_RESOURCE`, // This should match the url used in jwt.fromMethodAndUrl
                    params: {
                        jwt: token,
                    },
                });
                expect(res.status).to.equal(401);
                testContext.expectErrorsInLogs((record) => record.message.includes('invalid JWT token'));
            });

            test('secret does not match', async function () {
                const res = await bas.getCustomAPI({
                    kind: 'confluence',
                    api: `endpoint/${resourceName}`,
                    params: {
                        jwt: jwt.encodeSymmetric(tokenData, 'BAD_SECRET', jwt.SymmetricAlgorithm.HS512),
                    },
                });
                expect(res.status).to.equal(401);
                testContext.expectErrorsInLogs((record) => record.message.includes('invalid JWT'));
            });

            test('everything ok', async function () {
                const res = await bas.getCustomAPI({
                    kind: 'confluence',
                    api: `endpoint/${resourceName}`,
                    params: {
                        jwt: token,
                    },
                });
                expect(res.text).to.equal(testContext.staticServerMock.txtResourceContent);
            });
        });

        suite('custom APIs', function () {
            const basApi = '/confluence/'; // This is the base URL for custom APIs
            const resourceName = 'test.txt';

            // This creates a JWT token for Confluence API calls.
            // See https://bitbucket.org/atlassian/atlassian-jwt-js/src/master/
            const req: jwt.Request = jwt.fromMethodAndUrl('GET', basApi + resourceName);
            const now = Math.round(Date.now() / 1000); // Unix time in seconds
            const tokenData = {
                iss: installationData.clientKey,
                iat: now,
                exp: now + 180,
                qsh: jwt.createQueryStringHash(req), // [Query String Hash](https://developer.atlassian.com/cloud/jira/platform/understanding-jwt/#a-name-qsh-a-creating-a-query-string-hash)
            };
            const token = jwt.encodeSymmetric(tokenData, installationData.sharedSecret, jwt.SymmetricAlgorithm.HS512);

            test('render', async function () {
                const res = await bas.getCustomAPI({
                    kind: 'confluence',
                    api: 'render',
                    params: {
                        permalinkID: '1234567890',
                    },
                    token,
                });
                expect(res.status).to.equal(200);
            });
        });
    });
});
