/* global testContext */
/* eslint-env mocha */

import { assert, expect } from 'chai';

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import * as uuid from 'uuid';
import z from 'zod';
import { cloudGardening } from '../cloud-gardening.ts';
import type { CloudConnector } from '../connectors/cloud.js';
import * as jwt from '../connectors/lib/jwt.js';
import type { DataResidencyName } from '../environment-variables-schemas.ts';
import { createPermalinkOnFirstResource, defineCommonOfflineTests, defineCommonOnlineTests } from './shared-tests.ts';
import { BASApiClient } from './utils/apiclient.ts';
import { CloudServerMock, mockThumbnailBase64 } from './utils/mocks.ts';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

function parseSetPermalinkResponse(res: unknown) {
    return z
        .union([
            z.object({ error: z.string() }),
            z.object({
                permalinkID: z.string(),
                branchID: z.string().optional(),
                resourceID: z.string().optional(),
                permalinkInfo: z
                    .object({
                        dataResidency: z.string().optional(),
                    })
                    .optional(),
            }),
        ])

        .parse(res);
}
type SetPermalinkResponse = ReturnType<typeof parseSetPermalinkResponse>;

function parseCreateSnapshotResponse(res: unknown) {
    return z
        .union([
            z.object({ error: z.string() }),
            z
                .object({
                    projectName: z.string().optional(),
                    name: z.string(),
                    image: z.string(),
                })
                .passthrough(),
        ])
        .parse(res);
}
type CreateSnapshotResponse = ReturnType<typeof parseCreateSnapshotResponse>;

suite('Cloud', function () {
    const kind = `cloud`;
    const cloudUserId = 10;
    const userInfo = {
        // User info
        name: `cloudUserId-${cloudUserId}`,
        displayName: `user`,
        avatarURL: `https://placehold.it/60x60`,
        userId: `${cloudUserId}`,
    };

    const jwtsecret = 'foobar';
    const adminRole: 'ADMIN' | 'COMMENTER' | 'VIEWER' = 'ADMIN';
    const getPlatformToken = (extra: unknown) => {
        assert(extra !== null && typeof extra === 'object', 'extra must be an object');
        assert('siteId' in extra, 'siteId is required');
        assert('projectId' in extra, 'projectId is required');
        assert('role' in extra, 'role is required');
        const { siteId, projectId, role } = extra;
        return jwt.encode(
            {
                exp: Date.now() + 3600,
                sub: 'create_archive',
                iss: 'cloud-server',
                siteId,
                projectId,
                role,
            },
            jwtsecret,
            'HS256'
        );
    };

    const projectId = 1;
    const siteId = 1000;

    const platformTokenExtra = {
        siteId: siteId,
        projectId: projectId,
        role: 'ADMIN',
    };

    let app;
    let cloudServerMock!: CloudServerMock;
    let bas!: BASApiClient;

    const keysOfSetPermalinkAPI = [
        'image',
        'edit',
        'permalinkID',
        'branchID',
        'resourceID',
        'dirty',
        'permalinkKind',
        'platformKind',
        'permalinkInfo',
        'timestamp',
    ];
    const keysOfSetSnapshot = ['name', 'image'];

    setup(async function () {
        app = testContext.app;
        cloudServerMock = testContext.cloudServerMock;
        bas = new BASApiClient(app, {
            kind,
            userInfo,
            platformArchiveID: projectId,
            siteID: siteId,
            getPlatformToken,
            adminUser: testContext.basAdminUser,
            adminSecret: testContext.basAdminSecret,
            platformTokenExtra,
        });
    });

    async function createTestProject() {
        const createRes = await bas.createMultipart(path.join(__dirname, '../connectors/templates/2.0/webdemo.bmpr'));
        expect(createRes.body).to.have.property('platformArchiveID', `${projectId}`);
        let openRes = await bas.open();
        let { token, userID, dump, rtcChannel, rtcSubscriberKey } = openRes.body;
        return { token, userID, dump, rtcChannel, rtcSubscriberKey };
    }

    async function createPermalinkImageUnfurlingResource({
        dump,
        index,
        dataResidency,
    }: {
        dump: BmprDump;
        index: number;
        dataResidency?: DataResidencyName;
    }): Promise<SetPermalinkResponse> {
        const resource = dump.Resources[index];
        const { ID: resourceID, BRANCHID: branchID } = resource;
        const postCustomAPIRes = await bas.postCustomAPI({
            kind: 'cloud',
            api: 'get-create-image-unfurling',
            basicAuth: 'admin',
            data: {
                projectId,
                siteId: siteId,
                resourceId: resourceID,
                branchId: branchID,
                permalinkInfo: {
                    image: {
                        format: 'jpg',
                    },
                    dataResidency,
                },
            },
        });

        const setPermalinkRes = parseSetPermalinkResponse(JSON.parse(postCustomAPIRes.text));
        if ('error' in setPermalinkRes) {
            return setPermalinkRes;
        }
        expect(setPermalinkRes.branchID).to.be.equal(branchID);
        expect(setPermalinkRes.resourceID).to.be.equal(resourceID);

        return setPermalinkRes;
    }

    async function createSnapshotForFirstResource({
        dump,
        dataResidency,
    }: {
        dump: BmprDump;
        dataResidency?: DataResidencyName;
    }): Promise<CreateSnapshotResponse> {
        const resource = dump.Resources[0];
        const { ID: resourceID, BRANCHID: branchID } = resource;

        let createSnapshotRes = await bas.createSnapshot({
            resourceID,
            branchID,
            platformArchiveID: `${projectId}`,
            platformSiteID: `${siteId}`,
            platformKind: kind,
            platformInfo: {
                userID: 'xxx',
                bucketDir: 'slack',
                dataResidency,
            },
        });
        return parseCreateSnapshotResponse(createSnapshotRes.body);
    }

    test('Get RTC Auth token', async function () {
        const { token } = await createTestProject();
        const response = await bas.getRTCAuthToken({ token });
        const tokenInfo = response.body;
        const averageExpectedValue = 18 * 60;
        expect(tokenInfo.expirationInSec).to.be.closeTo(averageExpectedValue, averageExpectedValue * 0.11);
    });

    test('Get RTC Auth token, invalid session', async function () {
        await createTestProject();
        const response = await bas.getRTCAuthToken({ token: 'invalid_token' });
        expect(response.statusCode).to.equal(403);
        expect(response.body).to.deep.equal({ error: 'Not authenticated' });
    });

    test('restore project offloaded to platform', async function () {
        const restoreOneRes = await bas.restore({
            platformArchiveName: 'restored project',
            bmprPath: path.join(__dirname, '../connectors/templates/2.0/webdemo.bmpr'),
            bmprTimestamp: `${testContext.clock.now()}`,
        });
        expect(restoreOneRes.statusCode).to.equal(200);
    });

    suite('Gardening', function () {
        async function runGardening() {
            await cloudGardening({
                serverUtils: testContext.serverUtils,
                sessionManager: testContext.sessionManager,
                logger: testContext.logger.getLogger({ action: 'cloud-gardening-test', module: 'gar' }),
                metrics: testContext.metrics,
                timeDeltaForSavingLiveProjects: 20 * 60 * 1000,
                clock: testContext.clock,
                cloudConnector: testContext.getConnector('cloud') as CloudConnector,
            });
            return testContext.loglines;
        }

        test('Live modified project is saved after 1 hour, not before', async function () {
            const { token } = await createTestProject();
            testContext.clock.advance(10 * 60 * 1000); // 10 minutes
            await bas.setArchiveAttributes({ token, attributes: { projectName: 'new name' } }); // Make some project change
            await runGardening();
            testContext.cloudServerMock.expectApiCalls([]); // No save calls have been made to Cloud yet
            testContext.clock.advance(60 * 60 * 1000); // 1 hour
            await bas.refreshSession({ token }); // To keep session alive
            await bas.setArchiveAttributes({ token, attributes: { projectName: 'new name 2' } }); // Make some project change
            await runGardening();
            testContext.cloudServerMock.expectApiCalls([{ api: 'snapshot' }]); // Project has been saved to Cloud
            await bas.close({ token });
        });

        test('Live unchanged project is not saved, even after 1 hour', async function () {
            const { token } = await createTestProject();
            testContext.clock.advance(70 * 60 * 1000); // 70 minutes
            await bas.refreshSession({ token }); // To keep session alive
            await runGardening();
            testContext.cloudServerMock.expectApiCalls([]); // No save calls have been made to Cloud
            await bas.close({ token });
        });

        test('Project with no sessions is saved, if changed', async function () {
            const { token } = await createTestProject();
            await bas.refreshSession({ token }); // Touch session
            await bas.setArchiveAttributes({ token, attributes: { projectName: 'new name' } }); // Make some project change
            testContext.clock.advance(20 * 60 * 1000); // Wait 20 minutes (so the session is stale)
            await runGardening();
            testContext.cloudServerMock.expectApiCalls([{ api: 'snapshot' }]); // Project has been saved to Cloud
            await bas.close({ token });
        });

        test('Project with no sessions, if not changed, is not saved', async function () {
            const { token } = await createTestProject();
            await bas.refreshSession({ token }); // Touch session
            testContext.clock.advance(20 * 60 * 1000); // Wait 20 minutes (so the session is stale)
            await runGardening();
            testContext.cloudServerMock.expectApiCalls([]); // Project is not saved to Cloud
            await bas.close({ token });
        });
    });

    test('Create Multipart', async function () {
        const createRes = await bas.createMultipart(path.join(__dirname, '../connectors/templates/2.0/webdemo.bmpr'));
        expect(createRes.body).to.have.property('platformArchiveID', `${projectId}`);
    });

    suite('Offline Methods', async function () {
        setup(async function () {
            const createRes = await bas.createMultipart(path.join(__dirname, '../connectors/templates/2.0/webdemo.bmpr'));
            expect(createRes.body).to.have.property('platformArchiveID', `${projectId}`);
        });

        defineCommonOfflineTests(() => ({ bas, archiveId: `${projectId}`, siteId: `${siteId}`, kind }));

        test('Unload Archive if not in Sync', async function () {
            let unloadArchiveIfNotSyncRes = await bas.unloadArchiveIfNotSync({
                platformArchiveID: projectId.toString(),
                platformSiteID: siteId.toString(),
                kind: kind,
                modifiedTimestamp: Date.now(),
            });
            expect(unloadArchiveIfNotSyncRes.body).to.not.have.key('error');
        });

        test('Open with skipThumbnailImage', async function () {
            let openRes = await bas.open({ skipThumbnailImage: true });
            const { token, userID, dump, heuristicArchiveSize } = openRes.body;
            expect(token).not.to.be.undefined;
            expect(userID).not.to.be.undefined;
            expect(dump).not.to.be.undefined;
            expect(heuristicArchiveSize).not.to.be.undefined;

            expect(dump.Thumbnails).to.be.an('array');
            for (let thumbnail of dump.Thumbnails) {
                expect(thumbnail).to.have.property('ATTRIBUTES');
                const attributes = thumbnail.ATTRIBUTES;
                expect(attributes).to.have.property('branchID');
                expect(attributes).to.have.property('resourceID');
                expect(attributes).to.not.have.property('image');
            }
        });

        test('Project Exists of Platform', async function () {
            for (let v of [true, false]) {
                cloudServerMock.setHasSnapshot(v); // Set expected value

                let projectExistsOnPlatformRes = await bas.projectExistsOnPlatform({
                    platformArchiveID: `${projectId}`,
                    platformSiteID: `${siteId}`,
                    kind: kind,
                });
                expect(projectExistsOnPlatformRes.body).to.have.property('status', true);
                expect(projectExistsOnPlatformRes.body).to.have.property('response', v);
            }
        });
    });

    suite('Online Methods', async function () {
        let token: string, userID: string, dump: BmprDump, rtcChannel: string, rtcSubscriberKey: string, basArchiveID: string;

        setup(async function () {
            const createRes = await bas.createMultipart(path.join(__dirname, '../connectors/templates/2.0/webdemo.bmpr'));
            expect(createRes.body).to.have.property('platformArchiveID', `${projectId}`);

            let openRes = await bas.open();
            ({ token, userID, dump, rtcChannel, rtcSubscriberKey, basArchiveID } = openRes.body);
            expect(token).not.to.be.undefined;
            expect(userID).not.to.be.undefined;
            expect(dump).not.to.be.undefined;

            // Set presence
            testContext.mockBackendRTCClient.setReturnHerenowUuids([userID]);
        });

        defineCommonOnlineTests(() => ({
            bas,
            token,
            userInfo,
            userID,
            dump,
            basArchiveID,
            siteId: `${siteId}`,
            projectId: `${projectId}`,
            kind,
        }));

        suite('Permalinks Cloud', async function () {
            test(`Create permalink from an archive that doesn't reside on BAS`, async function () {
                const resource = dump.Resources[0];
                const { ID: resourceID, BRANCHID: branchID } = resource;
                const postCustomAPIRes = await bas.postCustomAPI({
                    kind: 'cloud',
                    api: 'get-create-image-unfurling',
                    basicAuth: 'admin',
                    data: {
                        projectId: 1234, // This project doesn't exist. BAS will ask cloud for the BMPR.
                        siteId: siteId,
                        resourceId: resourceID,
                        branchId: branchID,
                    },
                });
                expect(postCustomAPIRes.body).to.have.property('permalinkID');
            });

            test('Create permalink image unfurling and delete project offline', async function () {
                const response = await createPermalinkImageUnfurlingResource({ dump, index: 0 });
                await bas.close({ token });
                assert(!('error' in response), 'Error creating permalink');

                const { permalinkID } = response;
                let getPermalinkRes = await bas.getPermalink({
                    id: permalinkID,
                    query: 'metadata',
                });
                expect(getPermalinkRes.body).to.not.have.property('error');

                let deleteOfflineRes = await bas.deleteOffline({
                    platformArchiveID: `${projectId}`,
                    platformSiteID: `${siteId}`,
                    platformKind: kind,
                });
                expect(deleteOfflineRes.body).to.have.property('message', 'success');

                let getPermalinkRes2 = await bas.getPermalink({
                    id: permalinkID,
                    query: 'metadata',
                });
                expect(getPermalinkRes2.body).to.have.property('error');
                expect(getPermalinkRes2.body).to.have.property('notFound', true);
                testContext.expectErrorsInLogs((record) => record.message.includes('No thumbnail found on S3'));
            });

            test('Create permalink image unfurling and delete one resource', async function () {
                const response = await createPermalinkImageUnfurlingResource({ dump, index: 0 });
                assert(!('error' in response), 'Error creating permalink');
                const { permalinkID } = response;

                let getPermalinkRes = await bas.getPermalink({
                    id: permalinkID,
                    query: 'metadata',
                });
                expect(getPermalinkRes.body).to.not.have.property('error');
                expect(getPermalinkRes.body.permalinkData.permalinkInfo.image).to.match(/^https:\/\/us.testshare.com/);
                expect(getPermalinkRes.body.permalinkData.permalinkInfo.dataResidency).to.be.undefined;

                const resource = dump.Resources[0];
                const { ID: resourceID, BRANCHID: branchID } = resource;
                const deleteResourcesRes = await bas.deleteResources({
                    token,
                    branchID,
                    resourceIDs: [resourceID],
                });
                expect(deleteResourcesRes.body).to.have.property('archiveRevision');
                expect(deleteResourcesRes.body).to.have.property('heuristicArchiveSize');

                let getPermalinkRes2 = await bas.getPermalink({
                    id: permalinkID,
                    query: 'metadata',
                });
                expect(getPermalinkRes2.body).to.have.property('error');
                expect(getPermalinkRes2.body).to.have.property('notFound', true);
                testContext.expectErrorsInLogs((record) => record.message.includes('No thumbnail found on S3'));
                await bas.close({ token });
            });

            test('Create permalink image unfurling with data residency', async function () {
                let permalinkID;
                let getPermalinkRes;

                let response = await createPermalinkImageUnfurlingResource({ dump, index: 0, dataResidency: 'us' });
                assert(!('error' in response), 'Error creating permalink');
                permalinkID = response.permalinkID;
                getPermalinkRes = await bas.getPermalink({
                    id: permalinkID,
                    query: 'metadata',
                });
                expect(getPermalinkRes.body.permalinkData.permalinkInfo.image).to.match(/^https:\/\/us.testshare.com/);
                expect(getPermalinkRes.body.permalinkData.permalinkInfo.dataResidency).to.be.equal('us');
                testContext.expectErrorsInLogs((record) => record.message.includes('No thumbnail found on S3')); // Permalink thumbnail is generated asynchronously

                response = await createPermalinkImageUnfurlingResource({ dump, index: 1, dataResidency: 'eu' });
                assert(!('error' in response), 'Error creating permalink');
                permalinkID = response.permalinkID;

                getPermalinkRes = await bas.getPermalink({
                    id: permalinkID,
                    query: 'metadata',
                });
                expect(getPermalinkRes.body.permalinkData.permalinkInfo.image).to.match(/^https:\/\/eu.testshare.com/);
                expect(getPermalinkRes.body.permalinkData.permalinkInfo.dataResidency).to.be.equal('eu');
                testContext.expectErrorsInLogs((record) => record.message.includes('No thumbnail found on S3')); // Permalink thumbnail is generated asynchronously

                //@ts-ignore // this is a test to check the error handling for bad dataResidency
                response = await createPermalinkImageUnfurlingResource({ dump, index: 1, dataResidency: 'unexisting' });
                assert('error' in response, 'Expected error creating permalink');
                const { error } = response;
                expect(error).to.be.equal('Invalid data residency');

                await bas.close({ token });
            });

            test('Create permalink image unfurling and delete more resources', async function () {
                const response = await createPermalinkImageUnfurlingResource({ dump, index: 0 });
                assert(!('error' in response), 'Error creating permalink');
                const { permalinkID } = response;

                let getPermalinkRes = await bas.getPermalink({
                    id: permalinkID,
                    query: 'metadata',
                });
                expect(getPermalinkRes.body).to.not.have.property('error');
                testContext.expectErrorsInLogs((record) => record.message.includes('No thumbnail found on S3'));

                const response2 = await createPermalinkImageUnfurlingResource({ dump, index: 1 });
                assert(!('error' in response2), 'Error creating permalink');
                const permalinkID2 = response2.permalinkID;
                let getPermalinkRes2 = await bas.getPermalink({
                    id: permalinkID2,
                    query: 'metadata',
                });
                expect(getPermalinkRes2.body).to.not.have.property('error');
                testContext.expectErrorsInLogs((record) => record.message.includes('No thumbnail found on S3'));

                const resource = dump.Resources[0];
                const { ID: resourceID, BRANCHID: branchID } = resource;
                const deleteResourcesRes = await bas.deleteResources({
                    token,
                    branchID: branchID,
                    resourceIDs: [resourceID, response2.resourceID, dump.Resources[2].ID],
                });

                expect(deleteResourcesRes.body).to.have.property('archiveRevision');
                expect(deleteResourcesRes.body).to.have.property('heuristicArchiveSize');

                getPermalinkRes = await bas.getPermalink({
                    id: permalinkID,
                    query: 'metadata',
                });
                expect(getPermalinkRes.body).to.have.property('error');
                expect(getPermalinkRes.body).to.have.property('notFound', true);

                getPermalinkRes2 = await bas.getPermalink({
                    id: permalinkID2,
                    query: 'metadata',
                });
                expect(getPermalinkRes2.body).to.have.property('error');
                expect(getPermalinkRes2.body).to.have.property('notFound', true);
                await bas.close({ token });
            });

            test('Create snapshot', async function () {
                const body = await createSnapshotForFirstResource({ dump });
                assert(!('error' in body), 'Error creating snapshot');

                expect(body).to.have.keys(keysOfSetSnapshot);
                expect(body.image).to.match(/^https:\/\/us.testshare.com/);
                expect(body.name).to.be.equal('iPad');
                await bas.close({ token });
            });

            test('Create snapshot with data residency', async function () {
                let body;

                body = await createSnapshotForFirstResource({ dump, dataResidency: 'us' });
                assert(!('error' in body), 'Error creating snapshot');
                expect(body.image).to.match(/^https:\/\/us.testshare.com/);

                body = await createSnapshotForFirstResource({ dump, dataResidency: 'eu' });
                assert(!('error' in body), 'Error creating snapshot');
                expect(body.image).to.match(/^https:\/\/eu.testshare.com/);

                //@ts-ignore // this is a test to check the error handling for bad dataResidency
                const response = await createSnapshotForFirstResource({ dump, dataResidency: 'unexisting' });
                expect(response).to.have.property('error', 'Invalid data residency');
                await bas.close({ token });
            });

            test('Create snapshot for platform kind not implemented', async function () {
                await bas.createSnapshot({
                    resourceID: '',
                    branchID: '',
                    platformArchiveID: `${projectId}`,
                    platformSiteID: `${siteId}`,
                    platformKind: 'jira',
                    platformInfo: {
                        userID: 'xxx',
                        bucketDir: 'slack',
                    },
                });

                testContext.expectErrorsInLogs((err) =>
                    err.message.includes('Unexpected error in connector.generateSnapshot: Not implemented')
                );
            });

            test('Create snapshot with lambda payload null', async function () {
                testContext.LambdaMock.payload = 'null';
                const body = await createSnapshotForFirstResource({ dump });
                assert(!('error' in body), 'Error creating snapshot');

                expect(body).to.have.keys(keysOfSetSnapshot);
                expect(body.name).to.be.equal('iPad');

                testContext.expectErrorsInLogs((err) =>
                    err.message.includes('Unexpected error in connector.generateSnapshot: {"Payload":null}')
                );
                await bas.close({ token });
            });

            test('Create snapshot with lambda payload null and getSnapshot thumbnail', async function () {
                testContext.LambdaMock.payload = 'null';
                const body = await createSnapshotForFirstResource({ dump });
                assert(!('error' in body), 'Error creating snapshot');

                expect(body).to.have.keys(keysOfSetSnapshot);
                expect(body.name).to.be.equal('iPad');

                const id = body.image.split('/').pop();
                assert.isDefined(id, 'id is undefined');
                let getSnapshotRes = await bas.getSnapshot({
                    id,
                });

                expect(getSnapshotRes.status).to.eq(404);

                testContext.expectErrorsInLogs((err) =>
                    err.message.includes('Unexpected error in connector.generateSnapshot: {"Payload":null}')
                );
                testContext.expectErrorsInLogs((err) => err.message.includes('The specified key does not exist'));
                await bas.close({ token });
            });

            test('Set permalink with empty token', async function () {
                const resource = dump.Resources[0];
                const { ID: resourceID, BRANCHID: branchID } = resource;

                let permalinkRes = await bas.setPermalink({
                    token: '',
                    branchID,
                    resourceID,
                    check: true,
                });
                expect(permalinkRes.body).to.have.property('error', 'wrong credentials');
            });

            test('Set public_share permalink and try to fech it', async function () {
                const resource = dump.Resources[0];
                const { ID: resourceID, BRANCHID: branchID } = resource;

                let permalinkRes = await bas.setPermalink({
                    token,
                    branchID,
                    resourceID,
                    permalinkKind: 'public_share',
                });
                expect(permalinkRes.body).to.have.property('permalinkID');

                // Serve image not allowed on public_share permalinks
                const { permalinkID } = permalinkRes.body;

                // [PUBLIC_SHARE] we provide the thumbnail as permalink associated to the public share
                // let getPermalinkRes1 = await bas.getPermalink({id: permalinkID});
                // expect(getPermalinkRes1.status).to.be.equal(404);

                // Metadata allowed
                let getPermalinkRes2 = await bas.getPermalink({ id: permalinkID, query: `metadata` });
                expect(getPermalinkRes2.body).to.have.property('permalinkData');
            });

            test('Set public_share permalink and delete it', async function () {
                const permalinkUUID = 'someshortuuid';

                const permalinkRes = await bas.setPermalink({
                    token,
                    branchID: '',
                    resourceID: '',
                    permalinkKind: 'public_share',
                    permalinkInfo: {
                        permalinkUUID,
                    },
                });
                expect(permalinkRes.body).to.have.property('permalinkID');
                const { permalinkID } = permalinkRes.body;
                expect(permalinkID).to.be.equal(permalinkUUID);

                const deletePermalinkRes = await bas.deletePermalinkData({
                    token,
                    permalinkID,
                });
                expect(deletePermalinkRes.body).to.have.property('message');
                const { message } = deletePermalinkRes.body;
                expect(message).to.be.equal('permalink deleted');

                const getPermalinkRes2 = await bas.getPermalink({ id: permalinkID, query: `metadata` });
                expect(getPermalinkRes2.body).to.have.property('error');
                const { error } = getPermalinkRes2.body;
                expect(error).to.be.equal('No permalink found');
            });

            test('Check permalink with missing resource/branch', async function () {
                let permalinkRes = await bas.setPermalink({
                    token,
                    branchID: undefined,
                    resourceID: undefined,
                    check: true,
                });
                expect(permalinkRes.body).to.have.property('error', 'wrong parameters');
            });

            test('Check permalink with bad resource/branch', async function () {
                let permalinkRes = await bas.setPermalink({
                    token,
                    branchID: '123',
                    resourceID: '123',
                    check: true,
                });
                expect(permalinkRes.body).to.have.property('message', 'permalink not existing');
            });

            test('Set public_share, get 404 on png and first thumbnail in metadata', async function () {
                let permalinkRes = await bas.setPermalink({
                    token,
                    branchID: null,
                    resourceID: null,
                    permalinkKind: 'public_share',
                    permalinkInfo: {
                        edit: 'http://someurl.com',
                    },
                });
                expect(permalinkRes.body).to.have.property('image');
                expect(permalinkRes.body).to.have.property('edit');
                const { permalinkID } = permalinkRes.body;

                // [PUBLIC_SHARE] we provide the thumbnail as permalink associated to the public share
                // let getPermalinkRes1 = await bas.getPermalink({id: permalinkID});
                // expect(getPermalinkRes1.status).to.be.eq(404);

                let getPermalinkRes2 = await bas.getPermalink({ id: permalinkID, query: 'metadata' });
                expect(getPermalinkRes2.body).to.have.property('image');

                let getPermalinkRes3 = await bas.getPermalink({ id: permalinkID, query: 'thumbnail-only' });
                expect(getPermalinkRes3.status).to.be.eq(200);
            });

            test('Set permalink with BAS Admin Auth', async function () {
                const resource = dump.Resources[0];
                const { ID: resourceID, BRANCHID: branchID } = resource;
                let permalinkRes = await bas.setPermalinkAsBASAdmin({
                    branchID,
                    resourceID,
                    platformKind: kind,
                    platformArchiveID: `${projectId}`,
                    platformSiteID: `${siteId}`,
                    permalinkInfo: {},
                    platformInfo: {},
                });
                expect(permalinkRes.body).to.have.property('image');
            });

            test('Create or update permalink with image', async function () {
                const resource = dump.Resources[0];
                const {
                    ID: resourceID,
                    BRANCHID: branchID,
                    ATTRIBUTES: { thumbnailID },
                } = resource;
                let archiveRevision = dump.Info.ArchiveRevision;
                let permalinkRes = await bas.createOrUpdateImageLink({
                    token,
                    resourceID,
                    branchID,
                    permalinkInfo: { info: 1 },
                    imagePath: path.join(__dirname, 'image_640x480.png'),
                });

                expect(permalinkRes.body).to.have.property('permalinkID');
                expect(permalinkRes.body).to.have.property('dirty', false);
                expect(permalinkRes.body.image).to.match(/^https:\/\/us.testshare.com\//);
                let permalinkID = permalinkRes.body.permalinkID;

                // Permalink is stored in s3
                const { permalinkImageStorageAdapter } = testContext;
                const bucket = 'us-permalink-bucket';
                const permalinkKeyInBucket = permalinkImageStorageAdapter._getPermalinkKey({
                    permalinkID,
                    platformKind: kind,
                    dataResidency: 'us',
                });
                expect(testContext.s3Mock.buckets).to.have.property(bucket);
                expect(testContext.s3Mock.buckets[bucket]).to.have.property(permalinkKeyInBucket);

                let data = testContext.s3Mock.buckets[bucket][permalinkKeyInBucket];
                let imageData = fs.readFileSync(path.join(__dirname, 'image_640x480.png'));
                expect(data).to.deep.eq(imageData);

                // Update thumbnail outdates permalink
                const attributes = {
                    branchID,
                    resourceID,
                    image: 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABAQMAAAAl21bKAAAABlBMVEXMzMyWlpYU2uzLAAAACXBIWXMAAA7EAAAOxAGVKw4bAAAACklEQVQImWNgAAAAAgAB9HFkpgAAAABJRU5ErkJggg==',
                };
                const setThumbnailRes = await bas.setThumbnail({ token, thumbnailID, attributes, archiveRevision });
                expect(setThumbnailRes.body).to.have.key('archiveRevision');

                const getPermalinkRes = await bas.getPermalinkData({ token, resourceID, branchID });
                expect(getPermalinkRes.body).to.have.property('dirty', true);

                // Update permalink image
                let permalinkRes2 = await bas.createOrUpdateImageLink({
                    token,
                    resourceID,
                    branchID,
                    permalinkInfo: { info: 2 },
                    imagePath: path.join(__dirname, 'image_320x240.png'),
                });
                expect(permalinkRes2.body).to.have.property('permalinkID');
                expect(permalinkRes2.body.permalinkID).to.eq(permalinkID);

                data = testContext.s3Mock.buckets[bucket][permalinkKeyInBucket];
                imageData = fs.readFileSync(path.join(__dirname, 'image_320x240.png'));
                expect(data).to.deep.eq(imageData);

                // Permalink updated
                const getPermalinkRes2 = await bas.getPermalinkData({ token, permalinkID });
                expect(getPermalinkRes2.body).to.have.property('dirty', false);
            });

            test('Create or update permalink with data residency', async function () {
                let resourceID;
                let branchID;
                let permalinkID;
                let getPermalinkRes;

                const [firstResource, secondResource, thirdResource, fourthResource] = dump.Resources;

                // =============================================================
                // Test: The data residency is what the connector says it is
                // =============================================================

                cloudServerMock.setDataResidency('us');

                ({ ID: resourceID, BRANCHID: branchID } = firstResource);

                ({
                    body: { permalinkID },
                } = await bas.createOrUpdateImageLink({
                    token,
                    resourceID: resourceID,
                    branchID: branchID,
                    // When client-side (the editor) will call BAS's createOrUpdateImageLink it won't specify dataResidency in the request, but BAS will ask Cloud [connector] to figure it out:
                    // permalinkInfo: { dataResidency: undefined },
                }));

                getPermalinkRes = await bas.getPermalink({ id: permalinkID, query: 'metadata' });

                expect(getPermalinkRes.body.permalinkData.permalinkInfo.image).to.match(/^https:\/\/us.testshare.com\//);
                expect(getPermalinkRes.body.permalinkData.permalinkInfo.dataResidency).to.be.equal('us');

                // -------------------------------------------------------------

                cloudServerMock.setDataResidency('eu');

                ({ ID: resourceID, BRANCHID: branchID } = secondResource);

                ({
                    body: { permalinkID },
                } = await bas.createOrUpdateImageLink({
                    token,
                    resourceID: resourceID,
                    branchID: branchID,
                }));

                getPermalinkRes = await bas.getPermalink({ id: permalinkID, query: 'metadata' });

                expect(getPermalinkRes.body.permalinkData.permalinkInfo.image).to.match(/^https:\/\/eu.testshare.com\//);
                expect(getPermalinkRes.body.permalinkData.permalinkInfo.dataResidency).to.be.equal('eu');

                // =============================================================
                // Test: Further [update] attempts, even if the dataResidency was explicitly set in the request (which is not something the client-side (the editor) should do anyway), will not change an existing permalink's data residency
                // =============================================================

                cloudServerMock.setDataResidency('us');

                // Further [update] attempt:
                ({
                    body: { permalinkID },
                } = await bas.createOrUpdateImageLink({
                    token,
                    resourceID: resourceID,
                    branchID: branchID,
                }));

                getPermalinkRes = await bas.getPermalink({ id: permalinkID, query: 'metadata' });

                expect(getPermalinkRes.body.permalinkData.permalinkInfo.image).to.match(/^https:\/\/eu.testshare.com\//);
                expect(getPermalinkRes.body.permalinkData.permalinkInfo.dataResidency).to.be.equal('eu');

                // -------------------------------------------------------------

                ({
                    body: { permalinkID },
                } = await bas.createOrUpdateImageLink({
                    token,
                    resourceID: resourceID,
                    branchID: branchID,
                    // Data residency explicitly set in the request:
                    permalinkInfo: { dataResidency: 'us' },
                }));

                getPermalinkRes = await bas.getPermalink({ id: permalinkID, query: 'metadata' });

                expect(getPermalinkRes.body.permalinkData.permalinkInfo.image).to.match(/^https:\/\/eu.testshare.com\//);
                expect(getPermalinkRes.body.permalinkData.permalinkInfo.dataResidency).to.be.equal('eu');

                // =============================================================
                // Test: Fallback to the default behavior if the connector is acting up
                // =============================================================

                cloudServerMock.setDataResidency('unexisting');

                ({ ID: resourceID, BRANCHID: branchID } = thirdResource);

                ({
                    body: { permalinkID },
                } = await bas.createOrUpdateImageLink({
                    token,
                    resourceID: resourceID,
                    branchID: branchID,
                }));

                getPermalinkRes = await bas.getPermalink({ id: permalinkID, query: 'metadata' });

                // 'us' below because it is the default data residency:
                expect(getPermalinkRes.body.permalinkData.permalinkInfo.image).to.match(/^https:\/\/us.testshare.com\//);
                // 'undefined' below because Cloud's response wasn't valid:
                expect(getPermalinkRes.body.permalinkData.permalinkInfo.dataResidency).to.be.undefined;
                testContext.expectErrorsInLogs((record) => record.message.includes('Unexpected error in connector.getDataResidency'));

                // -------------------------------------------------------------

                cloudServerMock.setDataResidencyNotFound();

                ({ ID: resourceID, BRANCHID: branchID } = fourthResource);

                ({
                    body: { permalinkID },
                } = await bas.createOrUpdateImageLink({
                    token,
                    resourceID: resourceID,
                    branchID: branchID,
                }));

                getPermalinkRes = await bas.getPermalink({ id: permalinkID, query: 'metadata' });

                // 'us' below because it is the default data residency:
                expect(getPermalinkRes.body.permalinkData.permalinkInfo.image).to.match(/^https:\/\/us.testshare.com\//);
                // 'undefined' below because Cloud's response wasn't valid:
                expect(getPermalinkRes.body.permalinkData.permalinkInfo.dataResidency).to.be.undefined;
                testContext.expectErrorsInLogs((record) => record.message.includes('Unexpected error in connector.getDataResidency'));
            });

            test('Create or update permalink with image jpg', async function () {
                const resource = dump.Resources[0];
                const {
                    ID: resourceID,
                    BRANCHID: branchID,
                    ATTRIBUTES: { thumbnailID },
                } = resource;
                let archiveRevision = dump.Info.ArchiveRevision;
                let permalinkRes = await bas.createOrUpdateImageLink({
                    token,
                    resourceID,
                    branchID,
                    permalinkInfo: {
                        info: 1,
                        image: {
                            format: 'jpg',
                        },
                    },
                    imagePath: path.join(__dirname, 'image_640x480.png'),
                });

                expect(permalinkRes.body).to.have.property('permalinkID');
                expect(permalinkRes.body).to.have.property('dirty', false);
                expect(permalinkRes.body)
                    .to.have.property('image')
                    .that.matches(/\.jpg$/);
                let permalinkID = permalinkRes.body.permalinkID;

                // Permalink is stored in s3
                const { permalinkImageStorageAdapter } = testContext;
                const bucket = 'us-permalink-bucket';
                const permalinkKeyInBucket = permalinkImageStorageAdapter._getPermalinkKey({
                    permalinkID,
                    platformKind: kind,
                    dataResidency: 'us',
                });
                expect(testContext.s3Mock.buckets).to.have.property(bucket);
                expect(testContext.s3Mock.buckets[bucket]).to.have.property(permalinkKeyInBucket);

                let data = testContext.s3Mock.buckets[bucket][permalinkKeyInBucket];
                let imageData = fs.readFileSync(path.join(__dirname, 'image_640x480.png'));
                expect(data).to.deep.eq(imageData);

                // Update thumbnail outdates permalink
                const attributes = {
                    branchID,
                    resourceID,
                    image: 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABAQMAAAAl21bKAAAABlBMVEXMzMyWlpYU2uzLAAAACXBIWXMAAA7EAAAOxAGVKw4bAAAACklEQVQImWNgAAAAAgAB9HFkpgAAAABJRU5ErkJggg==',
                };
                const setThumbnailRes = await bas.setThumbnail({ token, thumbnailID, attributes, archiveRevision });
                expect(setThumbnailRes.body).to.have.key('archiveRevision');

                const getPermalinkRes = await bas.getPermalinkData({ token, resourceID, branchID });
                expect(getPermalinkRes.body).to.have.property('dirty', true);

                // Update permalink image
                let permalinkRes2 = await bas.createOrUpdateImageLink({
                    token,
                    resourceID,
                    branchID,
                    permalinkInfo: {
                        info: 2,
                        image: {
                            format: 'gif',
                        },
                    },
                    imagePath: path.join(__dirname, 'image_320x240.png'),
                });
                expect(permalinkRes2.body).to.have.property('permalinkID');
                expect(permalinkRes2.body.permalinkID).to.eq(permalinkID);
                //ignore any other format
                expect(permalinkRes.body)
                    .to.have.property('image')
                    .that.matches(/\.jpg$/);

                data = testContext.s3Mock.buckets[bucket][permalinkKeyInBucket];
                imageData = fs.readFileSync(path.join(__dirname, 'image_320x240.png'));
                expect(data).to.deep.eq(imageData);

                // Permalink updated
                const getPermalinkRes2 = await bas.getPermalinkData({ token, permalinkID });
                expect(getPermalinkRes2.body).to.have.property('dirty', false);
            });

            test('Check existance then create using UUID', async function () {
                const resource = dump.Resources[0];
                const { ID: resourceID, BRANCHID: branchID } = resource;
                let permalinkRes = await bas.setPermalink({
                    token,
                    branchID,
                    resourceID,
                    check: true,
                });
                expect(permalinkRes.body).to.have.property('message', 'permalink not existing');

                const editURL = 'https://url.for.editing.this.project';
                const permalinkUUID = 'someshortuuid';
                let setPermalinkRes = await bas.setPermalink({
                    token,
                    branchID,
                    resourceID,
                    permalinkInfo: {
                        permalinkUUID,
                        edit: editURL,
                    },
                });
                expect(setPermalinkRes.body).to.have.keys(keysOfSetPermalinkAPI);

                let setPermalinkRes2 = await bas.setPermalink({
                    token,
                    branchID,
                    resourceID,
                    check: true,
                });
                expect(setPermalinkRes2.body).not.to.have.property('message', 'permalink not existing');
            });

            test('Create without permalinkUUID', async function () {
                const resource = dump.Resources[0];
                const { ID: resourceID, BRANCHID: branchID } = resource;

                const editURL = 'https://url.for.editing.this.project';
                let setPermalinkRes = await bas.setPermalink({
                    token,
                    branchID,
                    resourceID,
                    permalinkInfo: {
                        edit: editURL,
                    },
                });
                expect(setPermalinkRes.body).to.have.keys(keysOfSetPermalinkAPI);
            });

            test('Delete existing and unexisting permalink created with setPermalink', async function () {
                const resource = dump.Resources[0];
                const { ID: resourceID, BRANCHID: branchID } = resource;

                const editURL = 'https://url.for.editing.this.project';
                let setPermalinkRes = await bas.setPermalink({
                    token,
                    branchID,
                    resourceID,
                    permalinkInfo: {
                        edit: editURL,
                    },
                });
                expect(setPermalinkRes.body).to.have.keys(keysOfSetPermalinkAPI);

                let deletePermalinkDataRes = await bas.deletePermalinkData({ token, branchID, resourceID });
                expect(deletePermalinkDataRes.body).to.have.property('message', 'permalink deleted');

                deletePermalinkDataRes = await bas.deletePermalinkData({ token, branchID, resourceID });
                expect(deletePermalinkDataRes.body).to.have.property('warning', 'no permalink found for the requested resource');
            });

            test('Delete permalinks created with createOrUpdateImageLink', async function () {
                const resource = dump.Resources[0];
                const { ID: resourceID, BRANCHID: branchID } = resource;
                let permalinkRes = await bas.createOrUpdateImageLink({
                    token,
                    resourceID,
                    branchID,
                    permalinkInfo: {},
                    imagePath: path.join(__dirname, 'image_640x480.png'),
                });

                expect(permalinkRes.body).to.have.property('permalinkID');

                const { permalinkID } = permalinkRes.body;

                const permalinkImageStorageAdapter = testContext.permalinkImageStorageAdapter;
                const permalinkKeyInBucket = permalinkImageStorageAdapter._getPermalinkKey({
                    permalinkID,
                    platformKind: kind,
                    dataResidency: 'us',
                });

                const bucket = 'us-permalink-bucket';
                expect(testContext.s3Mock.buckets).to.have.property(bucket);
                expect(testContext.s3Mock.buckets[bucket]).to.have.property(permalinkKeyInBucket);

                let deletePermalinkDataRes = await bas.deletePermalinkData({ token, permalinkID });
                expect(deletePermalinkDataRes.body).to.have.property('message', 'permalink deleted');
                expect(testContext.s3Mock.buckets[bucket]).to.not.have.property(permalinkKeyInBucket);
            });

            test('Create with deleted Archive ID', async function () {
                const deleteRes = await bas.delete({ token });
                expect(deleteRes.body).to.deep.eq({});

                const resource = dump.Resources[0];
                const { ID: resourceID, BRANCHID: branchID } = resource;

                const editURL = 'https://url.for.editing.this.project';
                let setPermalinkRes = await bas.setPermalink({
                    token,
                    branchID,
                    resourceID,
                    permalinkInfo: {
                        edit: editURL,
                    },
                });
                expect(setPermalinkRes.body).to.have.key('error');
            });

            test('Create with Missing BranchID information', async function () {
                const resource = dump.Resources[0];
                const { ID: resourceID } = resource;

                const editURL = 'https://url.for.editing.this.project';
                let setPermalinkRes = await bas.setPermalink({
                    token,
                    resourceID,
                    permalinkInfo: {
                        edit: editURL,
                    },
                });
                expect(setPermalinkRes.body).to.have.key('error');
            });

            test('Duplicate permalinkUUID is handled without errors', async function () {
                const permalinkUUID = uuid.v4();
                const resource = dump.Resources[0];
                const { ID: resourceID, BRANCHID: branchID } = resource;

                const editURL = 'https://url.for.editing.this.project';
                let setPermalinkRes = await bas.setPermalink({
                    token,
                    branchID,
                    resourceID,
                    permalinkInfo: {
                        permalinkUUID,
                        edit: editURL,
                    },
                });
                expect(setPermalinkRes.body).to.have.keys(keysOfSetPermalinkAPI);

                await bas.setPermalink({
                    token,
                    branchID,
                    resourceID,
                    permalinkInfo: {
                        permalinkUUID,
                        edit: editURL,
                    },
                });
                expect(testContext.loglines.some((x) => x.includes('Duplicated permalink'))).to.be.true;
                testContext.expectErrorsInLogs((err) => err.message.includes('Duplicated permalink'));

                await bas.setPermalink({
                    token,
                    branchID,
                    resourceID,
                    permalinkInfo: {
                        edit: editURL,
                    },
                });
            });

            test('get permalink metadata', async function () {
                const resource = dump.Resources[0];
                const { ID: resourceID, BRANCHID: branchID } = resource;

                const attributes = {
                    name: 'new name',
                };
                const setArchiveAttributesRes = await bas.setArchiveAttributes({ token, attributes });
                expect(setArchiveAttributesRes.body).to.have.property('archiveRevision');

                const editURL = 'https://url.for.editing.this.project';
                const permalinkUUID = 'someshortuuid';
                let setPermalinkRes = await bas.setPermalink({
                    token,
                    branchID,
                    resourceID,
                    permalinkInfo: {
                        permalinkUUID,
                        edit: editURL,
                    },
                });
                expect(setPermalinkRes.body).to.have.keys(keysOfSetPermalinkAPI);

                let getPermalinkRes2 = await bas.getPermalink({
                    id: permalinkUUID,
                    query: 'metadata',
                });
                expect(getPermalinkRes2.body).to.have.keys(
                    'id',
                    'image',
                    'resourceName',
                    'branchName',
                    'archiveRevision',
                    'projectName',
                    'permalinkData'
                );
                expect(getPermalinkRes2.body.permalinkData).to.have.property('permalinkInfo');
                expect(getPermalinkRes2.body.permalinkData.permalinkInfo).to.have.property('edit');
            });

            test('get permalink', async function () {
                const resource = dump.Resources[0];
                const { ID: resourceID, BRANCHID: branchID } = resource;
                let permalinkRes = await bas.setPermalink({
                    token,
                    branchID,
                    resourceID,
                    check: true,
                });
                expect(permalinkRes.body).to.have.property('message', 'permalink not existing');

                const editURL = 'https://url.for.editing.this.project';
                const permalinkUUID = 'someshortuuid';
                let setPermalinkRes = await bas.setPermalink({
                    token,
                    branchID,
                    resourceID,
                    permalinkInfo: {
                        permalinkUUID,
                        edit: editURL,
                    },
                });
                expect(setPermalinkRes.body).to.have.keys(keysOfSetPermalinkAPI);

                // exists
                let getPermalinkRes = await bas.getPermalink({
                    id: permalinkUUID,
                    query: 'exists',
                });
                expect(getPermalinkRes.body).to.have.property('permalinkID', permalinkUUID);

                // thumbnail-only
                let getPermalinkRes3 = await bas.getPermalink({
                    id: permalinkUUID,
                    query: 'thumbnail-only',
                });

                const base64PermalinkImage = getPermalinkRes3.body.toString('base64');
                expect(base64PermalinkImage).to.equal(mockThumbnailBase64);

                // doesn't exist
                let getPermalinkRes4 = await bas.getPermalink({
                    id: 'doesnotexists',
                    query: 'exists',
                });
                expect(getPermalinkRes4.body).to.have.property('error');
                expect(getPermalinkRes4.body).to.have.property('notFound', true);

                // Get permalink without having uploaded an image must return an error
                let getPermalinkRes5 = await bas.getPermalink({
                    id: permalinkUUID,
                });
                expect(getPermalinkRes5.status).to.eq(404);
                testContext.expectErrorsInLogs((err) => err.message.includes('The specified key does not exist'));

                // Update with image
                let createOrUpdateImageLinkRes = await bas.createOrUpdateImageLink({
                    token,
                    branchID,
                    resourceID,
                    imagePath: path.join(__dirname, 'image_640x480.png'),
                    permalinkInfo: {
                        permalinkUUID,
                        edit: editURL,
                    },
                });
                expect(createOrUpdateImageLinkRes.body).to.have.property('permalinkID');

                let imageData = fs.readFileSync(path.join(__dirname, 'image_640x480.png'));

                let getPermalinkRes6 = await bas.getPermalink({
                    id: permalinkUUID,
                });

                expect(getPermalinkRes6.body.toString('base64')).to.eq(imageData.toString('base64'));
                expect(getPermalinkRes6.status).to.eq(200);
            });

            test('Permalinks deleted upon restore project has taken place', async function () {
                const resourceOne = dump.Resources[0];
                const resourceTwo = dump.Resources[1];

                const { ID: resourceOneID, BRANCHID: branchOneID } = resourceOne;
                const { ID: resourceTwoID, BRANCHID: branchTwoID } = resourceTwo;

                let createOrUpdateImageLinkOneRes = await bas.createOrUpdateImageLink({
                    token,
                    branchID: branchOneID,
                    resourceID: resourceOneID,
                    imagePath: path.join(__dirname, 'image_640x480.png'),
                    permalinkInfo: {},
                });
                expect(createOrUpdateImageLinkOneRes.body).to.have.property('permalinkID');
                const permalinkOneTimestamp = testContext.clock.now();
                const permalinkOneID = createOrUpdateImageLinkOneRes.body.permalinkID;

                testContext.clock.advance(60 * 10 * 1000); // 10 mins

                let createOrUpdateImageLinkTwoRes = await bas.createOrUpdateImageLink({
                    token,
                    branchID: branchTwoID,
                    resourceID: resourceTwoID,
                    imagePath: path.join(__dirname, 'image_640x480.png'),
                    permalinkInfo: {},
                });
                expect(createOrUpdateImageLinkTwoRes.body).to.have.property('permalinkID');
                // const permalinkTwoTimestamp = testContext.clock.now();
                const permalinkTwoID = createOrUpdateImageLinkTwoRes.body.permalinkID;

                testContext.clock.advance(60 * 10 * 1000); // 10 mins

                // Both permalink exists
                let getPermalinkOneRes = await bas.getPermalink({
                    id: permalinkOneID,
                });
                expect(getPermalinkOneRes.status).to.eq(200);

                let getPermalinkTwoRes = await bas.getPermalink({
                    id: permalinkTwoID,
                });
                expect(getPermalinkTwoRes.status).to.eq(200);

                // Restore bmpr to permalinkOneTimestamp
                let restoreOneRes = await bas.restore({
                    platformArchiveName: 'restored project',
                    bmprPath: path.join(__dirname, '../connectors/templates/2.0/webdemo.bmpr'),
                    bmprTimestamp: `${permalinkOneTimestamp}`,
                });
                expect(restoreOneRes.body).to.have.property('platformArchiveID');

                // Only one permalink exists
                let getPermalinkOneRes1 = await bas.getPermalink({
                    id: permalinkOneID,
                });
                expect(getPermalinkOneRes1.status).to.eq(200);

                let getPermalinkTwoRes1 = await bas.getPermalink({
                    id: permalinkTwoID,
                });
                expect(getPermalinkTwoRes1.status).to.eq(404);
            });
        });

        test('setArchiveAttributes', async function () {
            const attributes = {
                projectName: 'new name',
            };
            const setArchiveAttributesRes = await bas.setArchiveAttributes({ token, attributes });
            expect(setArchiveAttributesRes.body).to.have.property('archiveRevision');
        });

        test('get user auth token', async function () {
            const response = await bas.getUserAuthToken({ token });
            expect(response.body).to.have.property('userAuthToken');
        });
    });

    suite('Session role manipulation', function () {
        let token: string, userID: string, dump: BmprDump, rtcChannel: string, rtcSubscriberKey: string;

        setup(async function () {
            const createRes = await bas.createMultipart(path.join(__dirname, '../connectors/templates/2.0/webdemo.bmpr'));
            expect(createRes.body).to.have.property('platformArchiveID', `${projectId}`);

            let openRes = await bas.open();
            ({ token, userID, dump, rtcChannel, rtcSubscriberKey } = openRes.body);
            expect(token).not.to.be.undefined;
            expect(userID).not.to.be.undefined;
            expect(dump).not.to.be.undefined;

            // Set presence
            testContext.mockBackendRTCClient.setReturnHerenowUuids([userID]);
        });

        test('Mark Cloud projects as deleted', async function () {
            const dataResidency = 'us';
            const {
                body: { permalinkID },
            } = await createPermalinkOnFirstResource({ kind, token, dump, dataResidency, bas });

            const postCustomAPIRes = await bas.postCustomAPI({
                kind: 'cloud',
                api: 'mark-projects-as-deleted',
                basicAuth: 'admin',
                data: {
                    projectIds: [projectId],
                },
            });
            expect(JSON.parse(postCustomAPIRes.text)).to.deep.equal({ success: 'ok' });

            // Verify that sessions have been invalidated
            let response = await bas.setArchiveAttributes({ token, attributes: { projectName: 'new name' } });
            expect(response.body.error).to.equal('Not authorized');
            testContext.expectErrorsInLogs((err) => err.message.includes('Not authorized: ROLE_NO_ACCESS < ROLE_ADMIN'));

            // Verify that permalink has been deleted
            let getPermalinkRes1 = await bas.getPermalink({
                id: permalinkID,
                query: 'exists',
            });
            expect(getPermalinkRes1.body).to.have.property('error');
            expect(getPermalinkRes1.body).to.have.property('notFound', true);

            // Verify that permalink images have been removed from storage
            const { permalinkImageStorageAdapter } = testContext;
            const permalinkBucket = 'us-permalink-bucket';
            const permalinkKeyInBucket = permalinkImageStorageAdapter._getPermalinkKey({ permalinkID, platformKind: kind, dataResidency });

            expect(testContext.s3Mock.buckets).to.have.property(permalinkBucket);
            expect(testContext.s3Mock.buckets[permalinkBucket]).to.not.have.property(permalinkKeyInBucket);
            expectSessionsChangedRtcMessage([{ token, role: 0 }]);
        });

        test('Invalidate Cloud user session', async function () {
            const postCustomAPIRes = await bas.postCustomAPI({
                kind: 'cloud',
                api: 'invalidate-user-sessions',
                basicAuth: 'admin',
                data: {
                    userIds: [cloudUserId],
                },
            });
            expect(JSON.parse(postCustomAPIRes.text)).to.deep.equal({ success: 'ok' });

            // Verify that sessions have been invalidated
            let response = await bas.setArchiveAttributes({ token, attributes: { projectName: 'new name' } });
            expect(response.body.error).to.equal('Not authorized');
            testContext.expectErrorsInLogs((err) => err.message.includes('Not authorized: ROLE_NO_ACCESS < ROLE_ADMIN'));

            expectSessionsChangedRtcMessage([{ token, role: 0 }]);
        });

        test('Change Cloud project sessions roles, directly', async function () {
            // Target session by identifying user directly by ID
            let postCustomAPIRes = await bas.postCustomAPI({
                kind: 'cloud',
                api: 'update-project-sessions',
                basicAuth: 'admin',
                data: {
                    projects: [
                        {
                            projectId: projectId,
                            users: [{ userId: cloudUserId, role: 'COMMENTER' }],
                        },
                    ],
                },
            });
            expect(JSON.parse(postCustomAPIRes.text)).to.deep.equal({ success: 'ok' });

            // Verify that session has been updated
            let response = await bas.setArchiveAttributes({ token, attributes: { projectName: 'new name' } });
            expect(response.body.error).to.equal('Not authorized');
            testContext.expectErrorsInLogs((err) => err.message.includes('Not authorized: ROLE_COMMENTER < ROLE_ADMIN'));

            expectSessionsChangedRtcMessage([{ token, role: 2 }]);
        });

        test('Change Cloud project sessions roles, indirectly', async function () {
            // Target session indirectly
            const postCustomAPIRes = await bas.postCustomAPI({
                kind: 'cloud',
                api: 'update-project-sessions',
                basicAuth: 'admin',
                data: {
                    projects: [
                        {
                            projectId: projectId,
                            otherUsersRole: 'VIEWER',
                        },
                    ],
                },
            });
            expect(JSON.parse(postCustomAPIRes.text)).to.deep.equal({ success: 'ok' });

            // Verify that session has been updated
            let response = await bas.setArchiveAttributes({ token, attributes: { projectName: 'new name' } });
            expect(response.body.error).to.equal('Not authorized');
            testContext.expectErrorsInLogs((err) => err.message.includes('Not authorized: ROLE_VIEWER < ROLE_ADMIN'));

            expectSessionsChangedRtcMessage([{ token, role: 1 }]);
        });

        function expectSessionsChangedRtcMessage(sessions: { token: string; role: number }[]) {
            const publishedMessages = testContext.mockBackendRTCClient.published;
            const relevantMessages = publishedMessages.filter(
                (msg) => msg.payload && 'operation' in msg.payload && msg.payload.operation === 'changeSessionRoles'
            );
            expect(relevantMessages.length).to.equal(1);
            expect(relevantMessages[0].payload).to.have.property('operation', 'changeSessionRoles');
            expect('sessions' in relevantMessages[0].payload && relevantMessages[0].payload.sessions).to.deep.equal(sessions);
        }
    });
});
