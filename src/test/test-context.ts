/* global testContext */
/* eslint-env mocha */
import http from 'http';
import { roleForAccessTable, type RoleForAccessTable } from '../role-for-access-table.ts';
import * as bmprUtilsMod from '@balsamiq/bmpr/lib/BmprUtils.js';
import { buildMainLogger, type Logger, type Payload } from '@balsamiq/logging';
import { AwsKmsAdapterMock, JwtKmsService } from '@balsamiq/saas-utils';
import assert from 'assert';
import { expect } from 'chai';
import type { Application, Request } from 'express';
import path from 'path';
import { fileURLToPath } from 'url';
import * as uuid from 'uuid';
import { createApp } from '../app.js';
import { BlockList } from '../blocklist.ts';
import { callWithLegacyCallback } from '../calling-style.ts';
import { VirtualClock } from '../clock.ts';
import type { Config, loadConfig } from '../configLoader.ts';
import { initializeConnectors } from '../connectors/connectors-registry.ts';
import { DBConnector } from '../database.ts';
import { Metrics } from '../metrics.ts';
import { MySQLDriverPool } from '../mysql-driver.ts';
import { RATE_LIMITERS, makeRateLimiterConfiguration } from '../rate-limitation.ts';
import { RedisAdapter } from '../redisAdapter.ts';
import { RtcAdapter } from '../rtc-adapter.ts';
import { S3PermalinksImageStorageAdapter } from '../s3adapter.ts';
import { makeServerUtils, type ServerUtils } from '../server_utils.js';
import { SessionManager, type SessionData } from '../session-manager.ts';
import { BuildNumberTracker } from '../track_build_number.ts';
import { Wireframe2imageAdapter } from '../wireframe2image-adapter.ts';
import {
    BackendRTCClientMock,
    CloudServerMock,
    CloudWatchMock,
    LambdaMock,
    MockConfluenceApiClient,
    S3Mock,
    StaticFileRepositoryMock,
    StaticServerMock,
} from './utils/mocks.ts';
import { loadTestConfig } from './utils/testConfigLoader.ts';
import { formatLogForConsole } from '../logging-helpers.ts';
import { checkServerAPICredentialsFactory } from '../utils.ts';
import type { AppContext } from '../app-context.ts';
import type { BASRequest } from '../request-context.ts';
import { ConfluenceAdapter } from '../atlassian-adapter.ts';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
export class TestContext implements AppContext {
    // The following properties are initialized in setup()
    unhandledPromiseRejectionErrors!: Error[];
    configInfo!: ReturnType<typeof loadTestConfig>;
    config!: Config;
    cloudServerMock!: CloudServerMock;
    staticServerMock!: StaticServerMock;
    s3Mock!: S3Mock;
    LambdaMock!: LambdaMock;
    mockBackendRTCClient!: BackendRTCClientMock;
    httpServer!: http.Server<typeof http.IncomingMessage, typeof http.ServerResponse>;
    clock!: VirtualClock;
    app!: Application;
    serverURL!: string;
    basAdminUser!: string;
    basAdminSecret!: string;
    loglines: string[] = [];
    logrecords: Payload[] = [];
    metrics!: Metrics;
    permalinkImageStorageAdapter!: S3PermalinksImageStorageAdapter;
    w2iAdapter!: Wireframe2imageAdapter;
    jwtKmsAdapter!: JwtKmsService;
    redisAdapter!: RedisAdapter;
    serverID!: string;
    sessionManager!: SessionManager;
    connectors!: ReturnType<typeof initializeConnectors>;
    logger!: Logger;

    serverUtils!: ServerUtils;
    rateLimiterConfiguration!: ReturnType<typeof makeRateLimiterConfiguration>;
    blockList!: BlockList;
    roleForAccessTable!: RoleForAccessTable;
    bmprUtilsMod: any;
    srcDirName!: string;
    mySqlDriverInstance!: MySQLDriverPool;
    getConnector!: ReturnType<typeof initializeConnectors>['getConnector'];
    rtcAdapter!: RtcAdapter;
    buildNumberTracker!: BuildNumberTracker;
    checkServerAPICredentials!: (req: Request) => Promise<boolean>;
    confluenceAdapter!: ConfluenceAdapter;
    mockConfluenceApiClient!: MockConfluenceApiClient;
    staticFileRepository!: StaticFileRepositoryMock;

    basRequests: BASRequest[] = [];
    notifyBASRequest = (req: BASRequest) => {
        req.bas.onFinished(() => {}); // Make sure the _requestFinishedPromise is initialized
        this.basRequests.push(req);
    };

    constructor() {
        // Unhandled promise rejection handler
        process.removeAllListeners('unhandledRejection');
        process.on('unhandledRejection', (err: Error) => {
            this.handleUnhandledPromiseRejection(err);
        });

        // Uncaught exception handler
        process.removeAllListeners('uncaughtException');
        process.on('uncaughtException', (err) => {
            console.error('Uncaught exception', err);
        });
    }

    async setup() {
        this.unhandledPromiseRejectionErrors = [];
        // Setup remote rtc server mock
        this.basAdminUser = 'bas-admin';
        this.basAdminSecret = 'admin-secret';
        this.srcDirName = path.join(__dirname, '..');

        this.cloudServerMock = new CloudServerMock({
            cloudBasicAuthCredentials: {
                username: 'bas',
                password: 'pass',
            },
            srcDirName: this.srcDirName,
        });
        await this.cloudServerMock.start();

        if (!this.cloudServerMock.serverURL) {
            throw new Error('Cloud server URL not set');
        }

        this.configInfo = loadTestConfig({
            basAdminUser: this.basAdminUser,
            basAdminSecret: this.basAdminSecret,
            cloudServerBaseUrl: this.cloudServerMock.serverURL,
        });
        this.configInfo.config.port = 0; // 0 = the operating system will assign a random port at the first run. Subsequent runs will reuse the same port.

        this.staticServerMock = new StaticServerMock(this.srcDirName);
        await this.staticServerMock.start();
        assert(this.staticServerMock.serverURL, 'Static server URL is not set. Please check the static server mock implementation.');

        this.configInfo.config.proxyConfig.push({
            prefix: '/bw-atlassian/',
            host: this.staticServerMock.serverURL,
            path: '/bw-atlassian/',
        });

        this.bmprUtilsMod = bmprUtilsMod;

        this.roleForAccessTable = roleForAccessTable;

        this.logger = buildMainLogger((payload) => {
            // NOTE: Payload coming from @balsamiq/logging needs to be cleaned up before being passed to bunyan
            const cleanedPayload = Object.assign({}, payload) as Partial<typeof payload>;

            delete cleanedPayload.level;
            delete cleanedPayload.timestamp;
            delete cleanedPayload.message;

            this.loglines.push(formatLogForConsole(payload));
            this.logrecords.push(payload);
        }).getLogger({ context: 'test' });

        this.config = this.configInfo.config as ReturnType<typeof loadConfig>['config'];

        this.logger.info('Initializing TEST BAS environment', { action: 'initialization' });

        this.clock = new VirtualClock();

        this.metrics = new Metrics({
            logger: this.logger,
            namespace: this.config.metricNamespace,
            buildNumber: this.config.buildNumber,
            dbCloudWatch: new CloudWatchMock(),
            cloudWatch: new CloudWatchMock(),
        });

        const mySqlSecrets = await this.config.mySQLConfig.credentialsSecret.getSecret(this.logger);
        this.mySqlDriverInstance = await new MySQLDriverPool(
            process.env['BAS_DB_HOST'] || mySqlSecrets.host,
            process.env['BAS_DB_USER'] || mySqlSecrets.username,
            process.env['BAS_DB_PASSWORD'] || mySqlSecrets.password,
            mySqlSecrets.port,
            this.metrics
        ).initialize(this.config.clusterSize, this.logger);

        await new Promise<void>((resolve, reject) =>
            this.mySqlDriverInstance.waitForMySQLToBeReady((error) => {
                if (error) {
                    reject(error);
                } else {
                    resolve();
                }
            })
        );

        this.s3Mock = new S3Mock();

        this.LambdaMock = new LambdaMock({ payload: JSON.stringify({ response: { success: true } }) });

        this.mockBackendRTCClient = new BackendRTCClientMock();

        this.permalinkImageStorageAdapter = new S3PermalinksImageStorageAdapter(
            this.config.permalinkS3Storage,
            this.config.defaultDataResidencyName,
            this.s3Mock
        );

        this.w2iAdapter = new Wireframe2imageAdapter({
            lambda: this.LambdaMock,
            name: 'lambda_test',
            dataResidencies: {
                us: { bucketRegion: 'us_region_test', bucketName: 'us_bucket_test', baseDir: 'us_basedir_test' },
                eu: { bucketRegion: 'eu_region_test', bucketName: 'eu_bucket_test', baseDir: 'eu_basedir_test' },
            },
        });

        this.jwtKmsAdapter = new JwtKmsService(new AwsKmsAdapterMock(null), { environment: 'test' });

        // we do not use any lock at connector level, but we recover from momentary inconsistency
        this.redisAdapter = new RedisAdapter(
            { port: this.config.redisPort, host: this.config.redisURL, database: this.config.redisDB },
            this.logger
        );
        await this.redisAdapter.init();

        const connectorLockType = null;
        this.sessionManager = new SessionManager(
            connectorLockType,
            this.mySqlDriverInstance,
            this.metrics,
            this.configInfo,
            this.redisAdapter,
            this.logger
        );
        await this.sessionManager.isReady;

        const serverPrefix = 'bas-test-';
        this.serverID = serverPrefix + uuid.v1();

        this.getConnector = (kind: string | null) => {
            return this.connectors.getConnector(kind);
        };

        this.rtcAdapter = new RtcAdapter({ backendRTCClient: this.mockBackendRTCClient, logger: this.logger, metrics: this.metrics });

        this.checkServerAPICredentials = checkServerAPICredentialsFactory(this.config);

        this.buildNumberTracker = new BuildNumberTracker({
            metrics: this.metrics,
            logger: this.logger,
            redisAdapter: this.redisAdapter,
        });

        this.serverUtils = makeServerUtils({
            sessionManager: this.sessionManager,
            mySqlDriverInstance: this.mySqlDriverInstance,
            logger: this.logger,
            getConnector: this.getConnector,
            config: this.config,
            metrics: this.metrics,
            buildNumberTracker: this.buildNumberTracker,
            rtcAdapter: this.rtcAdapter,
            clock: this.clock,
        });

        let rateLimiters = [...RATE_LIMITERS];
        this.rateLimiterConfiguration = makeRateLimiterConfiguration(rateLimiters, this.metrics, this.redisAdapter, this.logger);

        this.blockList = new BlockList({ redisAdapter: this.redisAdapter });

        this.mockConfluenceApiClient = new MockConfluenceApiClient(this.clock);

        this.staticFileRepository = new StaticFileRepositoryMock(this.config.proxyConfig);

        this.confluenceAdapter = new ConfluenceAdapter({
            redisAdapter: this.redisAdapter,
            logger: this.logger,
            config: this.config,
            clock: this.clock,
            apiClient: this.mockConfluenceApiClient,
        });

        this.connectors = initializeConnectors(
            this.sessionManager,
            this.logger,
            this.metrics,
            this.permalinkImageStorageAdapter,
            this.config,
            this.serverUtils,
            this.w2iAdapter,
            this.redisAdapter,
            this.confluenceAdapter,
            this.clock
        );

        this.app = createApp(this);

        this.httpServer = http.createServer(this.app);
        await new Promise<void>((resolve) =>
            this.httpServer.listen(0, '127.0.0.1', () => {
                const serverAddress = this.httpServer.address();
                assert(
                    serverAddress !== null,
                    'Server address is null. Please make sure the server is listening on a network address and has not been closed.'
                );
                assert(typeof serverAddress !== 'string', 'Server address is likely a localsocket. Please use a network address.');
                const address = serverAddress.address;
                const port = serverAddress.port;
                this.serverURL = `http://${address}:${port}`; // IPv6 compatible address
                resolve();
            })
        );
    }

    async withDBSession(func: (dbConnector: DBConnector) => unknown | Promise<unknown>) {
        const sessionData = await callWithLegacyCallback<SessionData>((cb) => this.sessionManager.createSession(this.logger, 'test', cb));
        try {
            return await func(sessionData.dbConnector);
        } finally {
            try {
                await callWithLegacyCallback((cb) => sessionData.sessionManager.releaseSession(sessionData, cb));
            } catch (err) {
                // Ignore error
            }
        }
    }

    async waitForAllBASRequestsToFinish() {
        await Promise.all(this.basRequests.map((req) => req.bas._requestFinishedPromise));
    }

    async teardown() {
        await testContext.waitForAllBASRequestsToFinish();
        this.basRequests = [];
        await testContext.clearMysqlDB();
        await testContext.clearRedisDB();

        try {
            global.testContext.expectNoErrorsInLogs();
        } catch (err) {
            testContext.showLogs();
            throw err;
        }

        const unhandledPromiseRejectionError = global.testContext.findUnhandledPromiseRejectionError();
        if (unhandledPromiseRejectionError !== null) {
            console.error('Unhandled promise rejection', unhandledPromiseRejectionError);
            throw new Error('Found unhandled promise rejection');
        }

        await this.redisAdapter.quit();
        await new Promise((resolve) => this.mySqlDriverInstance.shutdown(resolve));
        await new Promise((resolve) => this.httpServer.close(resolve));
        await this.cloudServerMock.stop();
        await this.staticServerMock.stop();
    }

    private handleUnhandledPromiseRejection = (error: Error) => {
        this.unhandledPromiseRejectionErrors.push(error);
    };

    private findUnhandledPromiseRejectionError() {
        if (this.unhandledPromiseRejectionErrors.length === 0) {
            return null;
        }
        return this.unhandledPromiseRejectionErrors[0];
    }

    expectNoErrorsInLogs() {
        const unexpectedErrors = this.logrecords
            .filter((r) => 'level' in r && r.level === 'ERROR')
            .filter((r) => ('return' in r ? !r.return : true)) // Exclude errors from returnJSON... we might refactor this one day
            .filter((r) => ['Session closed'].includes(r.message) === false) // Exclude legacy errors that should be removed in the future
            .filter((r) => !r.message.includes('Ignore this line'));

        if (unexpectedErrors.length > 0) {
            console.log(unexpectedErrors);
            expect.fail('Unexpected errors found in log');
        }
    }

    expectErrorsInLogs(findFn: (record: Payload) => boolean) {
        let foundErrors = this.logrecords.filter((record) => findFn(record));
        if (foundErrors.length === 0) {
            expect.fail('Expected error messages in log, but none found');
        }
        // Remove asserted rows from logs
        this.logrecords = this.logrecords.filter((record) => !findFn(record));
    }

    showLogs() {
        for (let line of this.loglines || []) {
            process.stdout.write(line);
        }
    }

    private async clearRedisDB() {
        const redisAdapter = new RedisAdapter(
            { host: this.config.redisURL, port: this.config.redisPort, database: this.config.redisDB },
            this.logger
        );
        await redisAdapter.init();
        await redisAdapter.flushdb();
        await redisAdapter.quit();
    }

    private async clearMysqlDB() {
        return await new Promise<void>((resolve) => {
            this.mySqlDriverInstance.getConnection((connection) => {
                if ('error' in connection) {
                    throw new Error(`Error connecting to MySQL: ${connection.error}`);
                }
                connection.query(`DROP DATABASE IF EXISTS ${this.config.mySQLConfig.basDBName}`, () => {
                    // BAS
                    connection.query(`DROP DATABASE IF EXISTS ${this.config.mySQLConfig.permalinksDBName}`, () => {
                        // PERMALINKS
                        connection.query(
                            `SELECT SCHEMA_NAME FROM information_schema.schemata WHERE SCHEMA_NAME LIKE '${this.config.archiveIDPrefix}%' ORDER BY SCHEMA_NAME`,
                            (err, rows /*, fields*/) => {
                                // recursively delete all test archive DBs
                                let i = -1;
                                const deleteTestArchiveDB = () => {
                                    i++;
                                    if (i < rows.length) {
                                        connection.query(`DROP DATABASE ${rows[i].SCHEMA_NAME}`, () => {
                                            deleteTestArchiveDB();
                                        });
                                    } else {
                                        resolve(); // All test Archives deleted
                                    }
                                };
                                deleteTestArchiveDB();
                            }
                        );
                    });
                });
            });
        });
    }
}

setup(async function () {
    try {
        const testContext = new TestContext();
        await testContext.setup();
        global.testContext = testContext;
    } catch (err) {
        console.error(err);
    }
});

teardown(async function () {
    if (!global.testContext) {
        return; // Initialization failed
    }

    if (this.currentTest && this.currentTest.state !== 'passed') {
        testContext.showLogs();
    }

    await global.testContext.teardown();

    // NOTE: testContext was not declared optional to facilitate the access in the tests.
    // @ts-ignore
    delete global.testContext;
});

let interrupted = false;
process.on('SIGINT', async function () {
    if (interrupted) {
        return;
    }
    interrupted = true;

    if (global.testContext) {
        console.log(`Cleaning up...`);
        try {
            await global.testContext.teardown();
        } catch (err) {
            console.error('Error during cleanup:', err);
        }
    }
    console.log('Exiting.');
    process.exit(0);
});
