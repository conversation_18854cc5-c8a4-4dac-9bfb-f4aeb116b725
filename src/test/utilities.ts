/* global testContext */
/* eslint-env mocha */

import assert from 'node:assert';
import { expect } from 'chai';
import request from 'supertest';
import express from 'express';

import { runningDerivative } from '../session-manager.ts';
import { basRequest<PERSON><PERSON>ler, globalErrorHandler } from '../middlewares.ts';
import { augmentRequestObject } from '../request-context.ts';
import { mainGardeningJob } from '../entrypoint_main_gardening.ts';

suite('main gardening', function () {
    test('main gardening', async function () {
        await new Promise<void>((resolve) => {
            const { logger, sessionManager, serverUtils, clock } = testContext;

            // Some jobs are scheduled only to run in the weekend
            clock.set(1747563412810); // Sun May 18 2025 12:16:52 -> Sunday, before 13:00 UTC

            mainGardeningJob(logger, sessionManager, serverUtils, clock, 1, () => {
                resolve();
            });
        });
    });
});

suite('DB Utilities', function () {
    test('getLockedQueries', async function () {
        await testContext.withDBSession(async (dbConnection) => {
            await dbConnection.getLockedQueries();
        });
    });
});

suite('global error handler middleware', function () {
    const unexpectedErrorMessage = 'Unexpected error';
    let app: express.Express;
    setup(function () {
        app = express();
        app.use(function (req, res, next) {
            augmentRequestObject(req, res, testContext);
            next();
        });
        app.get(
            '/unexpected-error',
            basRequestHandler(async function (req, res) {
                throw new Error(unexpectedErrorMessage);
            })
        );
        app.get(
            '/unexpected-error-headers-sent',
            basRequestHandler(async function (req, res) {
                // set headers and send them without also sending a body
                res.setHeader('Content-Type', 'application/json');
                res.setHeader('Cache-Control', 'no-cache, no-store');
                res.flushHeaders();

                const err = new Error(`Ignore this line. The default express's error handler always logs to the console.`);
                err.stack = undefined; // remove stack trace to avoid logging it
                throw err;
            })
        );
        app.use(globalErrorHandler);
    });

    test('should return 500 for unexpected errors', async function () {
        const httpClient = request(app);
        await httpClient.get('/unexpected-error').expect(500);
        testContext.expectErrorsInLogs((log) => log.message.includes(unexpectedErrorMessage));
    });

    test('should handle unexpected errors with headers sent', async function () {
        const httpClient = request(app);
        try {
            await httpClient.get('/unexpected-error-headers-sent');
        } catch (err) {
            // Throw because express trucantes the connection on error after headers have been sent
        }
        testContext.expectErrorsInLogs((log) => log.message.includes('Headers already sent'));
    });
});

suite('sessionManager', function () {
    test('runAndLogShowFullProcessList', async function () {
        await testContext.sessionManager.runAndLogShowFullProcessList(testContext.logger);
        expect(testContext.loglines.find((line) => line.includes('SHOW FULL PROCESSLIST'))).to.not.be.undefined;
    });

    test('checkConsistency', async function () {
        const result = await testContext.sessionManager.checkConsistency(testContext.metrics, null, 1);
        expect(result.historyOfPendingSessionsCount.length).to.eq(1);
    });
});

suite('runningDerivative', function () {
    test('suicide for rate too high', function () {
        let suicided: number | null = null;
        const CONFIG = {
            minimumHistoryInSec: 60,
            historyDurationInSec: 120,
            ratePerMinThreshold: 20,
            suicide: (avgNewPendingSessionsPerMin: number) => {
                suicided = avgNewPendingSessionsPerMin;
            },
        };
        let state = null;
        state = runningDerivative({
            now: 1000,
            numPendingSessions: 10,
            state,
            ...CONFIG,
        });
        expect(suicided).to.be.null;

        state = runningDerivative({
            now: 2000,
            numPendingSessions: 20,
            state,
            ...CONFIG,
        });

        state = runningDerivative({
            now: 3000,
            numPendingSessions: 20,
            state,
            ...CONFIG,
        });
        expect(suicided).to.be.null;

        state = runningDerivative({
            now: 4000,
            numPendingSessions: 40,
            state,
            ...CONFIG,
        });
        expect(suicided).to.be.null;

        state = runningDerivative({
            now: 5000,
            numPendingSessions: 20,
            state,
            ...CONFIG,
        });
        expect(suicided).to.be.null;

        expect(state).to.deep.equal({
            historyOfPendingSessionsCount: [
                {
                    avgNewPendingSessionsPerMin: 0,
                    newPendingSessionsPerMin: 0,
                    numPendingSessions: 10,
                    ts: 1000,
                },
                {
                    avgNewPendingSessionsPerMin: 300,
                    newPendingSessionsPerMin: 600,
                    numPendingSessions: 20,
                    ts: 2000,
                },
                {
                    avgNewPendingSessionsPerMin: 200,
                    newPendingSessionsPerMin: 0,
                    numPendingSessions: 20,
                    ts: 3000,
                },
                {
                    avgNewPendingSessionsPerMin: 450,
                    newPendingSessionsPerMin: 1200,
                    numPendingSessions: 40,
                    ts: 4000,
                },
                {
                    avgNewPendingSessionsPerMin: 120,
                    newPendingSessionsPerMin: -1200,
                    numPendingSessions: 20,
                    ts: 5000,
                },
            ],
        });

        // Now make it suicide
        let now = 5000;
        let numPendingSessions = 20;
        while (!suicided) {
            now += 1000;
            numPendingSessions += 1; // 1 per second = 60 per min > 20 which is the threshold
            state = runningDerivative({ now, numPendingSessions, state, ...CONFIG });
        }
        expect(suicided).to.equal(64.83870967741936);
        expect(state.historyOfPendingSessionsCount.length).to.equal(62);
        expect(state.historyOfPendingSessionsCount[state.historyOfPendingSessionsCount.length - 1]).to.deep.equal({
            avgNewPendingSessionsPerMin: 64.83870967741936,
            newPendingSessionsPerMin: 60,
            numPendingSessions: 77,
            ts: 62000,
        });
    });

    test('do not suicide, because growth rate is slow, even if total number is high', function () {
        let suicided = null;
        const CONFIG = {
            minimumHistoryInSec: 60,
            historyDurationInSec: 120,
            ratePerMinThreshold: 80,
            suicide: (avgNewPendingSessionsPerMin: number) => {
                suicided = avgNewPendingSessionsPerMin;
            },
        };
        let state: ReturnType<typeof runningDerivative> | null = null;

        let now = 1000;
        let numPendingSessions = 10;
        for (let i = 0; i < 10000; i += 1) {
            now += 1000;
            numPendingSessions += 1; // 1 per second = 60 per min < 80 which is the threshold
            state = runningDerivative({ now, numPendingSessions, state, ...CONFIG });
        }
        expect(state).to.not.be.undefined;
        expect(state).to.not.be.null;
        assert(state);

        expect(suicided).to.be.null;
        expect(state.historyOfPendingSessionsCount.length).to.equal(120);
        expect(state.historyOfPendingSessionsCount[state.historyOfPendingSessionsCount.length - 1]).to.deep.equal({
            avgNewPendingSessionsPerMin: 60,
            newPendingSessionsPerMin: 60,
            numPendingSessions: 10010,
            ts: 10001000,
        });
    });
});
