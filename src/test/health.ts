/* global testContext */
/* eslint-env mocha */
import { expect } from 'chai';
import { BASApiClient } from './utils/apiclient.ts';
import request from 'supertest';
import type express from 'express';

suite('Health APIs', () => {
    let app: express.Application;
    let bas: BASApiClient;
    setup(async function () {
        app = testContext.app;
        bas = new BASApiClient(app);
    });

    test('/health', async function () {
        const res = await bas.health();
        expect(res.status).to.equal(200);
        expect(res.body.status).to.equal('ok');
    });

    test('/health?stat=true', async function () {
        const res = await bas.health({ stat: true });
        expect(res.status).to.equal(200);
        expect(res.body.status).to.equal('ok');
        expect(res.body.stat).to.not.be.undefined;
    });

    test('/health?db=true', async function () {
        const res = await bas.health({ db: true });
        expect(res.status).to.equal(200);
        expect(res.body.dbConnection).to.equal('ok');
        expect(res.body.status).to.equal('ok');
    });

    test('Unauthorized /health?webdemo=true', async function () {
        const res = await bas.health({ webdemo: true });
        expect(res.status).to.equal(401);
    });

    test('/health?reset=bad_secret', async function () {
        const res = await bas.health({ reset: 'bad_secret' });
        expect(res.status).to.equal(200);
        expect(res.body.status).to.equal('ok');
    });

    test('/health?reset=edeef418-ddf6-4f1c-bfe5-e60237938adc', async function () {
        const res = await bas.health({ reset: 'edeef418-ddf6-4f1c-bfe5-e60237938adc', serverID: 'not_this_server' });
        expect(res.status).to.equal(200);
        expect(res.body.status).to.contain('Ignoring the reset command');
    });

    test('/metrics', async function () {
        const res = await bas.metrics();
        expect(res.status).to.equal(200);
        expect(res.body.status).to.equal('ok');
    });

    test('404 for unknown endpoints', async function () {
        await request(app).get('/unknown_endpoint').expect(404);
    });

    test('redirect on root /', async function () {
        await request(app).get('/').expect(302);
    });
});
