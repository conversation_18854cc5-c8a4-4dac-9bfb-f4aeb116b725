import { ConfluenceRedisExpirationKeyListener, confluenceMakeRedisKey } from '../connectors/confluence.js';
import { JiraRedisExpirationKeyListener, jiraMakeRedisKey } from '../connectors/jira.js';
import { doAddKeyInRedis } from '../utils.ts';
import { expect } from 'chai';

async function sleep(ms: number) {
    return new Promise((resolve) => setTimeout(resolve, ms));
}

suite('Apps Watcher ', () => {
    test('Test Confluence Expiration Key Listener', async () => {
        const { sessionManager, serverUtils, logger, metrics, config, redisAdapter, loglines } = testContext;

        await ConfluenceRedisExpirationKeyListener(sessionManager, serverUtils, logger, metrics, config, true, redisAdapter);

        const res = await doAddKeyInRedis(confluenceMakeRedisKey, redisAdapter, logger, 'sessionToken', 1);
        expect(res).to.be.deep.equal({ success: 'ok' });

        await sleep(1200); // Wait until key is expired

        // Check the listener is working by looking for the log line
        let tries = 3;
        while (tries-- > 0) {
            if (loglines.some((line) => line.includes('Session closed'))) {
                break;
            }
            await sleep(500);
        }
        expect(tries).to.be.greaterThan(0);
    });

    test('Test Jira Expiration Key Listener', async () => {
        const { sessionManager, serverUtils, logger, metrics, config, redisAdapter, loglines } = testContext;

        await JiraRedisExpirationKeyListener(sessionManager, serverUtils, logger, metrics, config, true, redisAdapter);

        const res = await doAddKeyInRedis(jiraMakeRedisKey, redisAdapter, logger, 'sessionToken', 1);
        expect(res).to.be.deep.equal({ success: 'ok' });

        await sleep(1200); // Wait until key is expired

        // Check the listener is working by looking for the log line
        let tries = 3;
        while (tries-- > 0) {
            if (loglines.some((line) => line.includes('Session closed'))) {
                break;
            }
            await sleep(500);
        }
        expect(tries).to.be.greaterThan(0);
    });
});
