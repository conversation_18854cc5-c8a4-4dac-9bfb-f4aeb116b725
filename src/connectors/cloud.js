/** @noformat */
/*
 * Copyright Balsamiq Studios, Inc.  All rights reserved.  http://www.balsamiq.com
 *
 */

//A connector that mimics a file repository like S3

import fs from 'fs';
import bmprUtilsMod from '@balsamiq/bmpr/lib/BmprUtils.js';
import * as uuid from 'uuid';
import con from '../constants.ts';
import { DATA_RESIDENCIES } from '../environment-variables-schemas.ts';
import superagent from 'superagent';
import shortUUID from 'short-uuid';
import { encodeBase36, getShortIDFromResourceOrBranchID, pipeStreamToResponse, decodeJWT } from '../utils.ts'
import { callSaneFunctionFromLegacy } from '../calling-style.ts';
import * as Consts from '@balsamiq/bmpr/lib/BalsamiqArchiveConstants.js'
import {deletePermalinksImages} from "../permalinks.js";
import { assertBASRequest } from '../request-context.ts';
import { getPermalinksDataHelper, createOrUpdateImageUnfurling, getResourceNameAndIDs } from '../utils.ts';


let BAS_USER_AGENT = 'Balsamiq BAS Server';

/**
 * @class CloudConnector
 * @implements {JSDocTypes.Connector}
 * @param {JSDocTypes.SessionManager} sessionManager 
 * @param {JSDocTypes.Logger} logger 
 * @param {JSDocTypes.Metrics} metrics 
 * @param {JSDocTypes.S3PermalinksImageStorageAdapter} permalinkImageStorageAdapter 
 * @param {JSDocTypes.Config} config 
 * @param {JSDocTypes.ServerUtils} serverUtils 
 * @param {JSDocTypes.Wireframe2imageAdapter} w2iAdapter 
 * @param {JSDocTypes.RedisAdapter} _redisAdapter
 * @param {JSDocTypes.Clock} clock 
 */
let CloudConnector = function (sessionManager, logger, metrics, permalinkImageStorageAdapter, config, serverUtils, w2iAdapter, _redisAdapter, clock) {
    this.logger = logger.getLogger({module: 'cloud'});
    this.metrics = metrics;
    this.permalinkImageStorageAdapter = permalinkImageStorageAdapter;
    this.w2iAdapter = w2iAdapter;
    this.serverUtils = serverUtils;
    this.config = config;
    this.projectsMaxAge = config.cloudProjectsMaxAgeInDays * 24 * 3600 * 1000; // In milliseconds
    this.sessionManager = sessionManager;
    this.archiveIDPrefix = config.archiveIDPrefix;
    this.baseUrl = config.baseUrl;
    this.cloudBaseUrl = config.cloudBaseUrl;
    this.clock = clock;
    this.jwtSecretsCache = {
        'cloud::cloud-server': config.cloudConfiguration.jwtSecret,
    };
    this.cloudServerBaseUrl = config.cloudConfiguration.cloudServerBaseUrl;
    this.cloudBasicAuthCredentials = config.cloudConfiguration.cloudBasicAuthCredentials;

    logger = this.logger.getLogger({action: 'startup'});
    logger.info("#CLOUD# Connector");
};

CloudConnector.prototype.getUniqueUserId = function (user) {
    return user.USERNAME;
};

CloudConnector.prototype.maxAge = function () {
    return this.projectsMaxAge;
};

/**
 * 
 * @returns {string}
 */
CloudConnector.prototype.generateArchiveID = function () {
    return this.archiveIDPrefix + "cloud_" + uuid.v1().replace(/-/g, '_');
};

CloudConnector.prototype.allowsArchiveCreation = function (dbConnector, platformToken, callback) {
    this._decodeToken(dbConnector, platformToken, function (result) {
        if (result.error) {
            callback(result);
        } else {
            callback({}); // no error = YES, you're allowed to create
        }
    });
};

CloudConnector.prototype._decodeToken = function (dbConnector, platformToken, callback) {
    callSaneFunctionFromLegacy(
        decodeJWT({dbConnector, platformToken, connectorId: 'cloud', noVerify: false, secretsCache: this.jwtSecretsCache}),
        callback,
    );
};

CloudConnector.prototype.checkUserInfo = function(logger, dbConnector, userInfo, authToken, callback) {
    logger = logger.getLogger({action: "checkUserInfo", module: "cloud"})
    callSaneFunctionFromLegacy(
        (async () => {
            let decoded = await decodeJWT({dbConnector, platformToken: authToken, connectorId: 'cloud', noVerify: false, secretsCache: this.jwtSecretsCache});
            if (decoded.claims.user && (userInfo.userId !== decoded.claims.user.userId || userInfo.displayName !== decoded.claims.user.fullName || userInfo.email !== decoded.claims.user.email || userInfo.name !== `cloudUserId-${decoded.claims.user.userId}`)) {
                logger.error(`User info mismatch. User info: ${JSON.stringify(userInfo)} – decoded token: ${JSON.stringify(decoded)}`);
                throw new Error('User info mismatch');
            }
            if (decoded.claims.isAnonymous) {
                if (!userInfo.anonymous || !userInfo.name.startsWith('cloudAnonymousUserId-')) {
                    logger.error(`Anonymous user info mismatch. User info: ${JSON.stringify(userInfo)} – decoded token: ${JSON.stringify(decoded)}`);
                    throw new Error('Anonymous user info mismatch, invalid claim');
                }
            }
            return {success: "ok"};
        })(),
        callback,
    );
};


CloudConnector.prototype.getUserKeyFromPlatformToken = function ({ platformToken, dbConnector }) {
    if (!platformToken) {
        return Promise.resolve(null);
    }

    return new Promise((resolve) => {
        this._decodeToken(dbConnector, platformToken, function (result) {
            if (result.error) {
                resolve(null);
            }
            const claims = result.claims;
            if (claims && claims.userId) {
                resolve(buildSessionUsernameFromCloudUserId(claims.userId));
            } else {
                resolve(null);
            }
        });
    });
};

/**
 * @param {JSDocTypes.Logger} logger
 * @param {JSDocTypes.DBConnector} dbConnector
 * @param {string} token
 * @param {string} platformSiteID
 * @param {string} platformArchiveID
 * @param {object} userInfo
 * @param {function} callback
 */
CloudConnector.prototype.getRole = function (logger, dbConnector, token, platformSiteID, platformArchiveID, userInfo, callback) {
    logger = logger.getLogger({action: "getRole", module: "cloud"});
    this._decodeToken(dbConnector, token, function (result) {
        try {
            if (result.error) {
                callback(result);
                return;
            }
            let claims = result.claims;

            if (parseInt(platformSiteID) !== claims.siteId) {
                logger.info('platformSiteID ' + platformSiteID + ' does not match siteId ' + claims.siteId + ' inside token');
                callback({error: 'platformSiteID does not match siteId inside token'});
                return;
            }

            if (parseInt(platformArchiveID) !== claims.projectId) {
                logger.info('platformArchiveID ' + platformArchiveID + ' does not match projectId ' + claims.projectId + ' inside token');
                callback({error: 'platformArchiveID does not match projectId inside token'});
                return;
            }

            let role = {
                "NO_ACCESS": con.ROLE_NO_ACCESS,
                "VIEWER": con.ROLE_VIEWER,
                "COMMENTER": con.ROLE_COMMENTER,
                "EDITOR": con.ROLE_EDITOR,
                "ADMIN": con.ROLE_ADMIN
            }[claims.role];

            callback({role: role});
        } catch (err) {
            callback({error: err});
        }
    }.bind(this));
};

CloudConnector.prototype.getBufferFromTemplate = function (callback) {
  let archiveID = this.generateArchiveID();
  let template = "./connectors/templates/" + con.BMPR_CURRENT_SCHEMA_VERSION + "/NewProject.bmpr";

  fs.readFile(template, function (err, data) {
    if (err) {
      callback({error: err});
    } else {
      callback({buffer: data, id: archiveID});
    }
  }.bind(this));
};

CloudConnector.prototype.userConnectedToArchive = function (logger, dbConnector, sessionToken, callback) {
    //logger.info("#CLOUD# userConnectedToArchive " + sessionToken);
    callback && callback({});
};

//calls back callback with status and a Buffer object with the data in it
CloudConnector.prototype.create = function (logger, dbConnector, authToken, platformSiteID, platformArchiveName, platformInfo, buffer, callback) {
  // N/A, projects are always created by uploading a BMPRs (even simply a template, but it's kept on the Cloud side)
  callback && callback({error: "Not available"});
};

CloudConnector.prototype.createToken = function (logger, platformSiteID, platformArchiveID, callback) {
    logger = logger.getLogger({
        action: "createToken",
        module: "cloud",
        platformSiteID: platformSiteID,
        platformArchiveID: platformArchiveID
    });
    let callbackCalled = false;

    function callbackIfNotCalledYet(obj) {
        if (!callbackCalled) {
            callbackCalled = true;
            callback(obj);
        }
    }

    let callbackWithError = (function callbackWithError(obj) {
        logger.error(obj.code + ': ' + obj.error);
        this.metrics.addValue('cloud-create-token', 1, 'Count');
        callbackIfNotCalledYet(obj);
    }).bind(this);

    let username = this.cloudBasicAuthCredentials.username;
    let password = this.cloudBasicAuthCredentials.password;
    let url = this.cloudServerBaseUrl + '/s' + encodeBase36(platformSiteID) + '/p' + encodeBase36(platformArchiveID) + '/create_token';

    superagent.post(url)
        .set('Authorization', 'Basic ' + Buffer.from(username + ':' + password).toString('base64'))
        .set('User-Agent', BAS_USER_AGENT)
        .send()
        .end((err, res) => {
            if (err) {
                callbackWithError({code: 'ERR_CLOUD_CREATE_TOKEN_2', error: 'Unexpected error calling Cloud (Project ID ' + platformArchiveID + ', Site ID ' + platformSiteID + ') at ' + url + ': ' + err});
            } else if (res.status === 404) {
                callbackWithError({code: 'ERR_CLOUD_CREATE_TOKEN_4', error: 'Project not found in Cloud (Project ID ' + platformArchiveID + ', Site ID ' + platformSiteID + ') at ' + url, notFound: true});
            } else if (res.status !== 200) {
                callbackWithError({code: 'ERR_CLOUD_CREATE_TOKEN_3', error: 'Unexpected ' + res.status + ' status code when calling Cloud (Project ID ' + platformArchiveID + ', Site ID ' + platformSiteID + ') at ' + url});
            } else {
                callbackIfNotCalledYet(res.body);
            }
        });
}

//calls back callback with status and a Buffer object with the data in it
CloudConnector.prototype.loadFromPlatform = function (logger, dbConnector, authToken, platformSiteID, platformArchiveID, options, platformInfo, callback) {
    logger = logger.getLogger({
        action: "loadFromPlatform",
        module: "cloud",
        platformSiteID: platformSiteID,
        platformArchiveID: platformArchiveID
    });
    let callbackCalled = false;

    function callbackIfNotCalledYet(obj) {
        if (!callbackCalled) {
            callbackCalled = true;
            callback(obj);
        }
    }

    let callbackWithError = (function callbackWithError(obj) {
        logger.error(obj.code + ': ' + obj.error);
        this.metrics.addValue('cloud-load-from-platform-errors', 1, 'Count');
        callbackIfNotCalledYet(obj);
    }).bind(this);

    let username = this.cloudBasicAuthCredentials.username;
    let password = this.cloudBasicAuthCredentials.password;
    let bmprUrl = this.cloudServerBaseUrl + '/s' + encodeBase36(platformSiteID) + '/p' + encodeBase36(platformArchiveID) + '/bmpr_data';
    superagent.get(bmprUrl)
        .set('Authorization', 'Basic ' + Buffer.from(username + ':' + password).toString('base64'))
        .set('User-Agent', BAS_USER_AGENT)
        .responseType('buffer') // Ensures the response is returned as a Buffer
        .end((err, res) => {
            if (err) {
                callbackWithError({code: 'ERR_CLOUD_LOAD_FROM_PLATFORM_2', error: 'Unexpected error calling Cloud (Project ID ' + platformArchiveID + ', Site ID ' + platformSiteID + ') at ' + bmprUrl + ': ' + err});
            } else if (res.status === 404) {
                callbackWithError({code: 'ERR_CLOUD_LOAD_FROM_PLATFORM_4', error: 'Project not found in Cloud (Project ID ' + platformArchiveID + ', Site ID ' + platformSiteID + ') at ' + bmprUrl, notFound: true});
            } else if (res.status !== 200) {
                callbackWithError({code: 'ERR_CLOUD_LOAD_FROM_PLATFORM_3', error: 'Unexpected ' + res.status + ' status code when calling Cloud (Project ID ' + platformArchiveID + ', Site ID ' + platformSiteID + ') at ' + bmprUrl});
            } else {
                let bmprBuffer = res.body;
                callbackIfNotCalledYet({
                    id: this.generateArchiveID(),
                    buffer: bmprBuffer
                });
            }
        });
};

CloudConnector.prototype.deleteFromPlatform = function (logger, dbConnector, authToken, platformSiteID, platformArchiveID, callback)
{
  callback && callback({});
};

/**
 * 
 * @param {JSDocTypes.Logger} logger 
 * @param {JSDocTypes.DBConnector} dbConnector 
 * @param {JSDocTypes.User | null} user 
 * @param {string} archiveID 
 * @param {string | null} newPlatformArchiveName 
 * @param {number} archiveRevision 
 * @param {Buffer} buffer 
 * @param {BmprDump} dump 
 * @param {{ fromClose?: boolean, fromRestore?: boolean }} options 
 * @param {JSDocTypes.BASLegacyCallback<{}>} callback 
 */
CloudConnector.prototype.save = function (logger, dbConnector, user, archiveID, newPlatformArchiveName, archiveRevision, buffer, dump, options, callback) {
    options = options || {};
    let fromClose = options.fromClose, fromRestore = options.fromRestore, force = options.force;
    logger = logger.getLogger({action: "save", module: "cloud"});
    let callbackCalled = false;

    function callbackIfNotCalledYet(obj) {
        if (!callbackCalled) {
            callbackCalled = true;
            callback(obj);
        }
    }

    let callbackWithError = (function callbackWithError(obj) {
        logger.error('SAVE_CLOUD_ERROR: ' + obj.error);
        this.metrics.addValue('save-cloud-errors', 1, 'Count');
        callbackIfNotCalledYet(obj);
    }).bind(this);
    try {
        dbConnector.getPlatformData(archiveID, function(platformData) {
            if (!platformData.PLATFORM_INFO) {
                callbackWithError({error: 'Missing PLATFORM_INFO'});
                return;
            }

            let platformInfo = JSON.parse(platformData.PLATFORM_INFO);
            let platformArchiveName = newPlatformArchiveName || platformData.PLATFORM_ARCHIVE_NAME;
            let platformArchiveID = platformData.PLATFORM_ARCHIVE_ID;
            let platformSiteID = platformData.PLATFORM_SITE_ID;
            let wasAlreadyUpdated = !force && platformInfo.archiveRevisionOnPlatform !== undefined && platformInfo.archiveRevisionOnPlatform >= archiveRevision;

            let _updateArchivePlatformData = (function () {
                platformInfo.archiveRevisionOnPlatform = archiveRevision;
                if (fromRestore) {
                    // In case of API_RESTORE we know that Cloud will also store a copy of the BMPR being restored
                    // as a snapshot of the new state, which has an archiveRevision incremented by 1.
                    // Therefore we also archiveRevisionOnPlatform, in order to reflect this knowledge.
                    platformInfo.archiveRevisionOnPlatform += 1;
                }
                dbConnector.updateArchivePlatformData(platformData.BAS_ARCHIVE_ID, platformData.PLATFORM_KIND, platformData.PLATFORM_SITE_ID, platformArchiveID, platformArchiveName, platformInfo, function(obj) {
                    if (obj.error) {
                        callbackWithError({error: "ERROR saving " + archiveID + ": " + obj.error});
                    } else {
                        logger.info("successfully saved " + archiveID);
                        callbackIfNotCalledYet({ platformArchiveID: platformArchiveID });
                    }
                }.bind(this));
            }).bind(this);

            if (wasAlreadyUpdated) {
                if (fromRestore) {
                    // In case of API_RESTORE we want to update "archiveRevisionOnPlatform" anyway. See comment inside the function
                    _updateArchivePlatformData();
                } else {
                    callbackIfNotCalledYet({platformArchiveID: platformArchiveID, wasAlreadyUpdated: true});
                }
                return;
            }

            let username = this.cloudBasicAuthCredentials.username;
            let password = this.cloudBasicAuthCredentials.password;
            let saveSnapshotUrl = this.cloudServerBaseUrl + '/s' + encodeBase36(platformSiteID) + '/p' + encodeBase36(platformArchiveID) + '/snapshot';
            let postWithRetries = function (currentAttempt, maxAttempts, cb) {
                const request = superagent.post(saveSnapshotUrl)
                    .set('Authorization', 'Basic ' + Buffer.from(username + ':' + password).toString('base64'))
                    .set('User-Agent', BAS_USER_AGENT)
                    .field('fromClose', JSON.stringify(fromClose))
                    .field('fromRestore', JSON.stringify(fromRestore))
                    .field('revisionNumber', JSON.stringify(platformInfo.archiveRevisionOnPlatform))

                if (user && user.USERINFO){
                    let userInfo = JSON.parse(user.USERINFO);
                    if (userInfo.userId) {
                        request.field('userId', userInfo.userId);
                    }
                }

                request.attach('file', buffer, 'file.bmpr');

                request.end((err, res) => {
                    if (err) {
                        cb({error: 'Unexpected error calling Cloud (Project ID ' + platformArchiveID + ', Site ID ' + platformSiteID + ') at ' + saveSnapshotUrl + ': ' + err});
                    } else if (res.status >= 500 && res.status <= 599) {
                        // 5xx errors: wait seconds and retry (3 sec, 6 sec, 9 sec, etc.)
                        if (currentAttempt <= maxAttempts) {
                            logger.info('RETRYING - Unexpected ' + res.status + ' status code when calling Cloud (Project ID ' + platformArchiveID + ', Site ID ' + platformSiteID + ') at ' + saveSnapshotUrl);
                            setTimeout(function () {
                                postWithRetries(currentAttempt + 1, maxAttempts, cb);
                            }, currentAttempt * 3000);
                        } else {
                            cb({error: 'Unexpected ' + res.status + ' status code when calling Cloud (Project ID ' + platformArchiveID + ', Site ID ' + platformSiteID + ') at ' + saveSnapshotUrl});
                        }
                    } else if (res.status !== 200) {
                        cb({error: 'Unexpected ' + res.status + ' status code when calling Cloud (Project ID ' + platformArchiveID + ', Site ID ' + platformSiteID + ') at ' + saveSnapshotUrl});
                    } else {
                        cb({});
                    }
                });
            }.bind(this);
            postWithRetries(1, 5, function (result) {
                if (result.error) {
                    callbackWithError(result);
                } else {
                    _updateArchivePlatformData();
                }
            });
        }.bind(this));
    } catch (e) {
        logger.error("Unhandled exception in Cloud connector save: " + e.toString(), e);
        callbackWithError({error: 'Unhandled exception'});
    }
};

CloudConnector.prototype.everyoneLeft = function(archiveID, callback) {
    //do nothing
    callback({});
};

CloudConnector.prototype.setPlatformArchiveName = function (platformArchiveID, authToken, platformArchiveName, callback)
{
    callback({});
};

CloudConnector.prototype.aboutToSetArchiveAttributes = function (logger, sessionData, authToken, attributes, platformData, dbConnector, callback) {
    logger = logger.getLogger({action: "aboutToSetArchiveAttributes", module: "cloud"});
    try {
        if (platformData.PLATFORM_INFO) {
            let username = this.cloudBasicAuthCredentials.username;
            let password = this.cloudBasicAuthCredentials.password;
            let platformArchiveID = platformData.PLATFORM_ARCHIVE_ID;
            let platformSiteID = platformData.PLATFORM_SITE_ID;
            let userInfo = JSON.parse(sessionData.user.USERINFO);
            let setAttributesUrl = this.cloudServerBaseUrl + '/s' + encodeBase36(platformSiteID) + '/p' + encodeBase36(platformArchiveID) + '/set_bas_archive_attributes';
            superagent.post(setAttributesUrl)
                .set('Authorization', 'Basic ' + Buffer.from(username + ':' + password).toString('base64'))
                .set('User-Agent', BAS_USER_AGENT)
                .send({ attributes: attributes, userId: userInfo.userId })
                .end((err, res) => {
                    if (err) {
                        logger.error('Unexpected error calling Cloud (Project ID ' + platformArchiveID + ', Site ID ' + platformSiteID + ') at ' + setAttributesUrl + ': ' + err);
                        callback({error: 'Error calling back Cloud server'});
                    } else if (res.status !== 200) {
                        logger.error('Unexpected ' + res.status + ' status code when calling Cloud (Project ID ' + platformArchiveID + ', Site ID ' + platformSiteID + ') at ' + setAttributesUrl);
                        callback({error: 'Error calling back Cloud server'});
                    } else {
                        callback({});
                    }
                });
        } else {
            callback({error: 'Missing PLATFORM_INFO'});
        }
    } catch (e) {
        logger.error("Unhandled exception in Cloud connector aboutToSetArchiveAttributes: " + e.toString(), e);
        callback({error: 'Missing PLATFORM_INFO'});
    }
};

CloudConnector.prototype.getAuthTokenFromPlatform = function(logger, platformInfo, callback) {
    callback({
        platformToken: null
    });
};

CloudConnector.prototype.logUserEvent = function(logger, dbConnector, ip, userInfo, userEvent, platformData, callback) {
    logger = logger.getLogger({action: 'logUserEvent', module: 'cloud'});

    if (!platformData.PLATFORM_INFO) {
        callback({error: 'missing PLATFORM_INFO'});
        return;
    }

    let username, password;
    let platformArchiveID = platformData.PLATFORM_ARCHIVE_ID;
    let platformSiteID = platformData.PLATFORM_SITE_ID;
    let logUserEventUrl = this.cloudServerBaseUrl + '/s' + encodeBase36(platformSiteID) + '/p' + encodeBase36(platformArchiveID) + '/notify_user_event';

    username = this.cloudBasicAuthCredentials.username;
    password = this.cloudBasicAuthCredentials.password;

    userEvent.sourceIP = ip;
    userEvent.userId = userInfo.userId;
    superagent.post(logUserEventUrl)
        .set('Authorization', 'Basic ' + Buffer.from(username + ':' + password).toString('base64'))
        .set('User-Agent', BAS_USER_AGENT)
        .send(userEvent)
        .end((err, res) => {
            if (err) {
                logger.error('Unexpected error calling Cloud (Project ID ' + platformArchiveID + ', Site ID ' + platformSiteID + ') at ' + logUserEventUrl + ': ' + err);
                callback({error: 'Error calling back Cloud server'});
            } else if (res.status !== 200) {
                logger.error('Unexpected ' + res.status + ' status code when calling Cloud (Project ID ' + platformArchiveID + ', Site ID ' + platformSiteID + ') at ' + logUserEventUrl);
                callback({error: 'Error calling back Cloud server'});
            } else {
                logger.info("OK: " + userEvent.type);
                callback({});
            }
        });
};

// Only used by the Cloud gardening
CloudConnector.prototype.publishSpacesUsageStats = async function(logger, spacesUsageStats) {
    logger = logger.getLogger({action: 'publishSpacesUsageStats', module: 'cloud'});

    const publishSpacesUsageStatsUrl = this.cloudServerBaseUrl + '/bas_space_usage_stats';
    const username = this.cloudBasicAuthCredentials.username;
    const password = this.cloudBasicAuthCredentials.password;

    await new Promise((resolve, reject) => {
        superagent.post(publishSpacesUsageStatsUrl)
            .set('Authorization', 'Basic ' + Buffer.from(username + ':' + password).toString('base64'))
            .set('User-Agent', BAS_USER_AGENT)
            .send({ spacesUsageStats })
            .end((err, res) => {
                if (err) {
                    reject(err);
                } else if (res.status !== 200) {
                    reject(new Error('Unexpected ' + res.status + ' status code when calling Cloud at ' + publishSpacesUsageStatsUrl));
                } else {
                    logger.info("Successfully published Space usage stats");
                    resolve();
                }
            });
    });
};

CloudConnector.prototype.getProjectMembers = function(logger, dbConnector, ip, userInfo, data, platformData, callback) {
    logger = logger.getLogger({action: 'getProjectMembers', module: 'cloud'});

    if (!platformData.PLATFORM_INFO) {
        callback({error: 'missing PLATFORM_INFO'});
        return;
    }

    let username, password;
    let platformArchiveID = platformData.PLATFORM_ARCHIVE_ID;
    let platformSiteID = platformData.PLATFORM_SITE_ID;
    let getProjectMembersUrl = this.cloudServerBaseUrl + '/s' + encodeBase36(platformSiteID) + '/p' + encodeBase36(platformArchiveID) + '/project_members';

    username = this.cloudBasicAuthCredentials.username;
    password = this.cloudBasicAuthCredentials.password;

    data.sourceIP = ip;
    data.userId = userInfo.userId;

    superagent.post(getProjectMembersUrl)
        .set('Authorization', 'Basic ' + Buffer.from(username + ':' + password).toString('base64'))
        .set('User-Agent', BAS_USER_AGENT)
        .send(data)
        .end((err, res) => {
            if (err) {
                logger.error('Unexpected error calling Cloud (Project ID ' + platformArchiveID + ', Site ID ' + platformSiteID + ') at ' + getProjectMembersUrl + ': ' + err);
                callback({error: 'Error calling back Cloud server'});
            } else if (res.status !== 200) {
                logger.error('Unexpected ' + res.status + ' status code when calling Cloud (Project ID ' + platformArchiveID + ', Site ID ' + platformSiteID + ') at ' + getProjectMembersUrl);
                callback({error: 'Error calling back Cloud server'});
            } else {
                // logger.info("OK: getProjectMembers");
                callback(res.body);
            }
        });
};

CloudConnector.prototype.projectExistsOnPlatform = function(logger, dbConnector, platformSiteID, platformArchiveID, callback) {
    this.projectHasBeenSavedOnPlatform(logger, dbConnector, platformSiteID, platformArchiveID, function(obj) {
        let ret;
        if (obj.error) {
            ret = {
                error: obj.error,
                code: obj.code,
                platformSiteID: platformSiteID,
                platformArchiveID: platformArchiveID
            };
        } else {
            ret = {
                status: true,
                response: obj.status
            };
        }

        callback(ret);
    });
};


CloudConnector.prototype.storePermalinkImage = async function ({dataStream, mimeType, permalinkID, permalinkInfo}) {
    const format = permalinkInfo?.image?.format ? permalinkInfo.image.format : "png";
    const dataResidency = permalinkInfo?.dataResidency ?? this.config.defaultDataResidencyName;
    return await this.permalinkImageStorageAdapter.uploadPermalinkImage({
        dataStream,
        mimeType,
        permalinkID,
        platformKind: 'cloud',
        format,
        dataResidency,
    });
};

CloudConnector.prototype.deletePermalinkImages = async function ({permalinkIDs, format = "png", dataResidency = this.config.defaultDataResidencyName}) {
    return await this.permalinkImageStorageAdapter.deletePermalinkImages({
        permalinkIDs,
        platformKind: 'cloud',
        format,
        dataResidency,
    });
};

CloudConnector.prototype.deletePermalinkThumbnailImages = async function ({permalinkIDs, format = "png", dataResidency = this.config.defaultDataResidencyName}) {
    return await this.permalinkImageStorageAdapter.deletePermalinkThumbnailImages({
        permalinkIDs,
        platformKind: 'cloud',
        format,
        dataResidency,
    });
};

/**
 * 
 * @param {object} param0 
 * @param {JSDocTypes.Logger} param0.logger
 * @param {string} param0.platformKind
 * @param {string} param0.platformSiteID
 * @param {string} param0.platformArchiveID
 * @param {string} param0.resourceID
 * @param {string} param0.branchID
 * @param {Record<string, unknown>} param0.platformInfo
 * @param {JSDocTypes.PermalinkData['permalinkInfo']} param0.permalinkInfo
 * @param {JSDocTypes.DataResidencyName} param0.dataResidency
 * @returns {Promise<string>}
 */
CloudConnector.prototype.generateSnapshot = async function ({logger, platformKind, platformSiteID, platformArchiveID, resourceID, branchID, platformInfo, permalinkInfo, dataResidency}) {
    try {
        if (this.w2iAdapter) {
            const _this = this;
            const platformToken = await new Promise(function(resolve, reject) {
                _this.createToken(logger, platformSiteID, platformArchiveID,function (obj) {
                    if (obj.error) {
                        reject(obj.error);
                    } else {
                        resolve(obj.token);
                    }
                });
            });

            await this.w2iAdapter.invoke({
                baseUrl: this.baseUrl,
                branchID: branchID,
                resourceID: resourceID,
                platformArchiveID: platformArchiveID,
                platformSiteID: platformSiteID,
                platformArchiveName: platformArchiveID,
                platformToken: platformToken,
                platformInfo : platformInfo,
                platformKind: platformKind,
                permalinkInfo,
                authorizationHeaderString: await this.serverUtils.getBASAuthorizationHeaderString(logger),
                dataResidency,
            });
        }
    }
    catch (error) {
        // error action handled by the caller
        throw error;
    }
};

/**
 * 
 * @param {object} param0 
 * @param {JSDocTypes.Logger} param0.logger
 * @param {JSDocTypes.PermalinkData} param0.permalinkData
 * @param {object} [param0.attachment]
 * @param {import('stream').Readable} param0.attachment.file
 * @param {string} param0.attachment.mimeType
 * @param {string} [param0.suffix]
 * @returns {Promise<unknown>}
 */
CloudConnector.prototype.generatePermalinkImage = async function ({logger, permalinkData, attachment, suffix}) {
    const {platformKind, platformSiteID, platformArchiveID, resourceID, branchID, platformInfo, permalinkInfo} = permalinkData;
    const dataResidency = permalinkInfo?.dataResidency ? permalinkInfo.dataResidency : this.config.defaultDataResidencyName;
    let ret = {};
    try {
        if (this.w2iAdapter) {
            const _this = this;
            const platformToken = await new Promise(function(resolve, reject) {
                _this.createToken(logger, platformSiteID, platformArchiveID,function (obj) {
                    if (obj.error) {
                        reject(new Error(obj.error));
                    } else {
                        resolve(obj.token);
                    }
                });
            });

            ret = await this.w2iAdapter.invoke({
                baseUrl: this.baseUrl,
                branchID: branchID,
                resourceID: resourceID,
                platformArchiveID: platformArchiveID,
                platformSiteID: platformSiteID,
                platformArchiveName: platformArchiveID,
                platformToken: platformToken,
                platformInfo : platformInfo,
                platformKind: platformKind,
                permalinkID: [permalinkData.permalinkID, suffix].join(''),
                permalinkInfo,
                thumbnailSize: 392,
                authorizationHeaderString: await this.serverUtils.getBASAuthorizationHeaderString(logger),
                dataResidency,
            });
        }
        else {
            // attachment format is "png" by design
            if (attachment && attachment.file && attachment.mimeType) {
                await this.storePermalinkImage({
                    dataStream: attachment.file,
                    mimeType: attachment.mimeType,
                    permalinkID: permalinkData.permalinkID,
                });
            }
        }
    }
    catch (error) {
        // error action handled by the caller
        throw error;
    }

    return ret;
};

CloudConnector.prototype.getPermalink = function ({permalinkData, releaseSessionIfNotReleased, req, res}, cb) {
    callSaneFunctionFromLegacy(this.asyncGetPermalink({permalinkData, releaseSessionIfNotReleased, req, res}), cb);
};

CloudConnector.prototype.asyncGetPermalinkThumbnail = async function (permalinkData, logger) {
    const {permalinkID, platformKind, permalinkInfo} = permalinkData;
    const format = permalinkInfo?.image?.format ? permalinkInfo.image.format : "png";
    const dataResidency = permalinkInfo?.dataResidency ?? this.config.defaultDataResidencyName;
    try {
        return await this.permalinkImageStorageAdapter.getPermalinkImageBase64({permalinkID, platformKind, format, dataResidency});
    } catch (err) {
        // permalink image could be not ready yet
        await this.generatePermalinkImage({logger, permalinkData});
        return await this.permalinkImageStorageAdapter.getPermalinkImageBase64({permalinkID, platformKind, format, dataResidency});
    }
}

CloudConnector.prototype.asyncGetPermalink = async function ({permalinkData, releaseSessionIfNotReleased, req, res}) {
    const {permalinkID, platformKind, permalinkInfo} = permalinkData;
    const format = permalinkInfo?.image?.format ? permalinkInfo.image.format : "png";
    const dataResidency = permalinkInfo?.dataResidency ?? this.config.defaultDataResidencyName;
    const _pipeStream = async () => {
        const headers = await this.permalinkImageStorageAdapter.getPermalinkHeadersWithMetadata({platformKind, permalinkID, format, dataResidency});
        const stream = await this.permalinkImageStorageAdapter.getPermalinkImageStream({platformKind, permalinkID, format, dataResidency});
        await releaseSessionIfNotReleased();
        return await pipeStreamToResponse(stream, res, headers);
    }
    try {
        return await _pipeStream();
    } catch (err) {
        try {
            await this.generatePermalinkImage({logger: req.bas.logger, permalinkData});
            return await _pipeStream();
        }
        catch (error) {
            // error action handled by the caller
            throw error;
        }
    }
    return {};
};

CloudConnector.prototype.makePermalinkID = function () {
    return shortUUID.generate();
};

/**
 * 
 * @param {string} baseDir 
 * @param {string} permalinkID 
 * @param {string} format 
 * @param {JSDocTypes.DataResidencyName} dataResidency 
 * @returns {string}
 */
CloudConnector.prototype.getSnapshotUrl = function (baseDir, permalinkID, format, dataResidency) {
    return this.config.shareUrls[dataResidency] + `/${baseDir}/${permalinkID}.${format}`;
};

/**
 * 
 * @param {PermalinkData} permalinkData 
 * @returns {PermalinkExtra}
 */
CloudConnector.prototype.getPermalinkExtraFromPermalinkData = function (permalinkData) {
    const {permalinkInfo, permalinkID, platformSiteID, platformArchiveID, resourceID, branchID} = permalinkData;
    const kindForPath = "cloud";
    const format = permalinkInfo?.image?.format ? permalinkInfo.image.format : "png";
    const dataResidency = permalinkInfo?.dataResidency ? permalinkInfo.dataResidency : this.config.defaultDataResidencyName;
    const imageURL = bmprUtilsMod.composeImagePermalinkUrl(this.config.shareUrls[dataResidency], permalinkID, kindForPath, format);

    let editURL = `${this.cloudBaseUrl}/s${encodeBase36(platformSiteID)}/p${encodeBase36(platformArchiveID)}/r${getShortIDFromResourceOrBranchID(resourceID)}`;
    if (branchID !== 'Master') {
        editURL += `/b${getShortIDFromResourceOrBranchID(branchID)}`;
    }

    return {
        image: imageURL ,
        edit: editURL,
    };
};

CloudConnector.prototype.projectHasBeenSavedOnPlatform = function(logger, dbConnector, platformSiteID, platformArchiveID, callback) {
    logger = logger.getLogger({
        action: "projectHasBeenSavedOnPlatform",
        module: "cloud",
        platformSiteID: platformSiteID,
        platformArchiveID: platformArchiveID
    });
    let callbackCalled = false;

    function callbackIfNotCalledYet(obj) {
        if (!callbackCalled) {
            callbackCalled = true;
            callback(obj);
        }
    }

    let callbackWithError = (function callbackWithError(obj) {
        logger.error(obj.code + ': ' + obj.error);
        this.metrics.addValue('cloud-archive-exists-on-platform-errors', 1, 'Count');
        callbackIfNotCalledYet(obj);
    }).bind(this);

    let username = this.cloudBasicAuthCredentials.username;
    let password = this.cloudBasicAuthCredentials.password;
    let hasSnapshotUrl = this.cloudServerBaseUrl + '/s' + encodeBase36(platformSiteID) + '/p' + encodeBase36(platformArchiveID) + '/has_snapshot';
    superagent.get(hasSnapshotUrl)
        .set('Authorization', 'Basic ' + Buffer.from(username + ':' + password).toString('base64'))
        .set('User-Agent', BAS_USER_AGENT)
        .end((err, res) => {
            if (err) {
                callbackWithError({code: 'ERR_CLOUD_ARCHIVE_EXISTS_ON_PLATFORM_2', error: 'Unexpected error calling Cloud (Project ID ' + platformArchiveID + ', Site ID ' + platformSiteID + ') at ' + hasSnapshotUrl + ': ' + err});
            } else if (res.status === 404) {
                callbackWithError({code: 'ERR_CLOUD_ARCHIVE_EXISTS_ON_PLATFORM_4', error: 'Project non existent (404) in Cloud (Project ID ' + platformArchiveID + ', Site ID ' + platformSiteID + ') at ' + hasSnapshotUrl});
            } else if (res.status !== 200) {
                callbackWithError({code: 'ERR_CLOUD_ARCHIVE_EXISTS_ON_PLATFORM_3', error: 'Unexpected ' + res.status + ' status code when calling Cloud (Project ID ' + platformArchiveID + ', Site ID ' + platformSiteID + ') at ' + hasSnapshotUrl});
            } else {
                callbackIfNotCalledYet({
                    status: res.body.hasSnapshot
                });
            }
        });
};

CloudConnector.prototype.markProjectsAsDeletedByProjectIds = async function ({ projectIds, logger }) {
    if (projectIds.length > 0) {
        await this.sessionManager.withSession(logger, 'cloud:markProjectsAsDeletedByProjectIds', async sessionData => {
            const projectIdsAsStrings = projectIds.map(pId => `${pId}`);
            // Invalidate sessions
            const archiveIds = await sessionData.dbConnector.getArchiveIDsfromPlatformArchiveIDsAndKind(projectIdsAsStrings, 'cloud');
            const sessions = await sessionData.dbConnector.getSessionsFromArchiveIds(archiveIds);
            const { affectedRows: numInvalidatedSessions } = await sessionData.dbConnector.updateSessionsPermissionsBySessionTokens({ tokens: sessions.map(session => session.TOKEN), permissions: con.ROLE_NO_ACCESS });

            this._broadcastChangeSessionRolesMessage(sessions.map(session => ({ archiveId: session.ARCHIVE_ID, token: session.TOKEN, role: 'NO_ACCESS' })));

            logger.info(`Invalidated ${numInvalidatedSessions} sessions for archives ${archiveIds.join(', ')}`);

            const { permalinksData } = await sessionData.dbConnector.getPermalinkDataByPlatformKindAndPlatformArchiveIDs({
                platformKind: 'cloud',
                platformArchiveIDs: projectIdsAsStrings
            });

            if (permalinksData.length > 0) {
                try {
                    await deletePermalinksImages(permalinksData, this);
                } catch (e) {
                    logger.warn(`Failed deleting permalink images from S3. ProjectIds: ${projectIdsAsStrings}. PermalinkIDs: ${permalinkIDs}. Error: ${e.message}`);
                }
                await sessionData.dbConnector.deletePermalinksByPlatformKindAndPlatformArchiveIDs({ platformKind: 'cloud', platformArchiveIDs: projectIdsAsStrings });
            }
        });
    }
    return {success: 'ok'};
};

CloudConnector.prototype.invalidateSessionsByUserIds = async function ({ userIds, logger }) {
    return await this.sessionManager.withSession(logger, 'cloud:invalidateSessionsByUserIds', async sessionData => {
        // Invalidate sessions
        const usernames = userIds.map(buildSessionUsernameFromCloudUserId);
        const sessions = await sessionData.dbConnector.getSessionsForUsernames({ usernames });
        const { affectedRows: numInvalidatedSessions } = await sessionData.dbConnector.updateSessionsPermissionsBySessionTokens({ tokens: sessions.map(session => session.TOKEN), permissions: con.ROLE_NO_ACCESS });

        this._broadcastChangeSessionRolesMessage(sessions.map(session => ({ archiveId: session.ARCHIVE_ID, token: session.TOKEN, role: 'NO_ACCESS' })));

        logger.info(`Invalidated ${numInvalidatedSessions} sessions for usernames ${usernames.join(', ')}`);
        return {success: 'ok'};
    });
};

function buildSessionUsernameFromCloudUserId(userId) {
    return `cloudUserId-${userId}`;
}

function getSessionRoleFromString(role) {
    return con[`ROLE_${role}`];
}

CloudConnector.prototype.deleteImageUnfurlingData = async function ({ p_req, logger }) {
    return await this.sessionManager.withSession(logger, 'cloud:delete-image-unfurling-data', async sessionData => {
        let response;

        // get permalinks data from body
        const resp = await getPermalinksDataHelper({
            projectId: p_req.body.projectId,
            siteId: p_req.body.siteId,
            kind: 'cloud',
            dbConnector: sessionData.dbConnector,
            connector: this
        });

        // looking for a permalinks image to be deleted
        const permalinksData = resp && resp.permalinksData ? resp.permalinksData : undefined;
        if (permalinksData && Array.isArray(permalinksData)) {
            // The reason we group by data residency is because ultimately S3 API isn't
            // meant to be called on different regions for multiple objects, therefore by
            // grouping permalinks by data residency we make sure to call S3 client once
            // per data residency (or AWS region) in lower levels of abstraction:
            const permalinksDataGroupedByDataResidency = permalinksData.reduce((result, permalinkData) => {
                const dataResidency = permalinkData.permalinkInfo?.dataResidency || this.config.defaultDataResidencyName;
                result[dataResidency] = result[dataResidency] || [];
                result[dataResidency].push(permalinkData);
                return result;
            }, {});

            for (const [dataResidency, permalinksData] of Object.entries(permalinksDataGroupedByDataResidency)) {
                const permalinkIDs_unfurling_png = permalinksData
                    .filter(({ permalinkKind, permalinkInfo }) => {
                        const isImageUnfurling = permalinkKind === Consts.PermalinkKind.image_unfurling;
                        const format = permalinkInfo?.image?.format;
                        return isImageUnfurling && (!format || format === "png");
                    })
                    .map(({ permalinkID }) => permalinkID);

                const permalinkIDs_unfurling_jpg = permalinksData
                    .filter(({ permalinkKind, permalinkInfo }) => {
                        const isImageUnfurling = permalinkKind === Consts.PermalinkKind.image_unfurling;
                        const format = permalinkInfo?.image?.format;
                        return isImageUnfurling && (format && format === "jpg");
                    })
                    .map(({ permalinkID }) => permalinkID);

                if (permalinkIDs_unfurling_png.length > 0) {
                    await this.deletePermalinkImages({permalinkIDs: permalinkIDs_unfurling_png, format: "png", dataResidency});
                    await this.deletePermalinkThumbnailImages({permalinkIDs: permalinkIDs_unfurling_png, format: "png", dataResidency});
                }
                if (permalinkIDs_unfurling_jpg.length > 0) {
                    await this.deletePermalinkImages({permalinkIDs: permalinkIDs_unfurling_jpg, format: "jpg", dataResidency});
                    await this.deletePermalinkThumbnailImages({permalinkIDs: permalinkIDs_unfurling_jpg, format: "jpg", dataResidency});
                }
            }

            const permalinkIDs = permalinksData.map(({ permalinkID }) => permalinkID);

            if (permalinkIDs.length > 0) {
                response = await sessionData.dbConnector.deletePermalinksByPermalinkIDs({
                    platformKind: 'cloud',
                    platformSiteID: p_req.body.siteId,
                    platformArchiveID: p_req.body.projectId, permalinkIDs,
                    permalinkKind: Consts.PermalinkKind.image_unfurling});
            }
        }

        return response ? response : {affectedRows: 0};
    });
}

CloudConnector.prototype.getOrCreatePermalinkUnfurling = async function ({ p_req, logger }) {
    return await this.sessionManager.withSession(logger, 'cloud:get-create-image-unfurling', async sessionData => {
        let permalinkResponse;

        // gets resource info from body
        const {resourceInfo} = await getResourceNameAndIDs({
            config: this.config,
            sessionManager: this.sessionManager,
            dbConnector: sessionData.dbConnector,
            connector: this,
            sessionData,
            platformKind: 'cloud',
            platformSiteID: p_req.body.siteId,
            platformArchiveID: p_req.body.projectId,
            platformInfo: {},
            resourceID: p_req.body.resourceId,
            branchID: p_req.body.branchId,
            logger,
        });

        const permalinkInfo = p_req.body.permalinkInfo ? p_req.body.permalinkInfo : {};

        if (permalinkInfo.dataResidency && !DATA_RESIDENCIES.includes(permalinkInfo.dataResidency)) {
            return { error: 'Invalid data residency' };
        }

        // if no resource found returns an error
        if(!(resourceInfo && resourceInfo.resourceID && resourceInfo.branchID)) {
            return {msg: 'No resource found', notFound: true};
        }

        // get permalinks data from body
        const resp = await getPermalinksDataHelper({
            projectId: p_req.body.projectId,
            siteId: p_req.body.siteId,
            kind: 'cloud',
            dbConnector: sessionData.dbConnector,
            connector: this
        });

        // looking for a permalink matching the resource info
        const permalinksData = resp && resp.permalinksData ? resp.permalinksData : undefined;
        if (permalinksData && Array.isArray(permalinksData)) {
            permalinkResponse = permalinksData.find(
                (permalink) => {
                    return permalink.permalinkKind === Consts.PermalinkKind.image_unfurling && permalink.resourceID.indexOf(resourceInfo.resourceID) === 0 && permalink.branchID.indexOf(resourceInfo.branchID) === 0;
                }
            );
        }

        if (!permalinkResponse) {
            //if no permalink found it will be created
            permalinkResponse = await createOrUpdateImageUnfurling({
                resourceInfo,
                platformKind: 'cloud',
                platformSiteID: p_req.body.siteId,
                platformArchiveID: p_req.body.projectId,
                resourceID: resourceInfo.resourceID,
                branchID: resourceInfo.branchID,
                dbConnector: sessionData.dbConnector,
                permalinkInfo,
                platformInfo: {},
                connector: this,
                clock: this.clock,
                logger: p_req.bas.logger});
        }
        else {
            // in case of permalink already present we can use the _thumbnail on S3
            const extensionMatch = permalinkResponse?.image.match(/(\.\w+)$/);
            if (extensionMatch) {
                permalinkResponse.image = permalinkResponse.image.replace(/(\.\w+)$/, '_thumbnail$1');
            }

            permalinkResponse = {
                ...permalinkResponse,
                projectName: resourceInfo.projectName,
                name: resourceInfo.name,
            }
        }

        return permalinkResponse ? permalinkResponse : {error: 'unable to create a permalink unfurling'};
    });
}

CloudConnector.prototype.updateProjectSessions = async function ({ projects, logger }) {
    return await this.sessionManager.withSession(logger, 'cloud:updateProjectSessions', async sessionData => {
        let sessionRoles = [];
        for (let { projectId, users, otherUsersRole } of projects) {
            let archive = await sessionData.dbConnector.getArchivefromPlatformArchiveIDAndKind(`${projectId}`, 'cloud');
            if (archive) {
                const archiveId = archive.BAS_ARCHIVE_ID;
                if (otherUsersRole) {
                    const userRoleMap = new Map();
                    for (let { userId, role } of users) {
                        userRoleMap.set(buildSessionUsernameFromCloudUserId(userId), role);
                    }
                    const allSessions = await sessionData.dbConnector.getSessionsByArchiveId(archiveId);
                    for (let session of allSessions) {
                        const roleString = userRoleMap.get(session.USERNAME) || otherUsersRole;
                        const desiredRoleForSession = getSessionRoleFromString(roleString);
                        if (session.PERMISSIONS !== desiredRoleForSession) {
                            await sessionData.dbConnector.updateSessionPermissionsByToken({ token: session.TOKEN, permissions: desiredRoleForSession });
                            sessionRoles.push({ archiveId, token: session.TOKEN, role: roleString });
                        }
                    }
                } else {
                    for (let { userId, role } of users) {
                        let sessions = await sessionData.dbConnector.getSessionsByArchiveIdAndUsername(archiveId, buildSessionUsernameFromCloudUserId(userId));
                        sessionRoles = sessionRoles.concat(sessions.map(session => ({ archiveId, token: session.TOKEN, role })));
                        await sessionData.dbConnector.updateSessionsPermissionsBySessionTokens({ tokens: sessions.map(session => session.TOKEN), permissions: getSessionRoleFromString(role) });
                    }
                }
            }
        }
        this._broadcastChangeSessionRolesMessage(sessionRoles);
        logger.info(`Updated ${sessionRoles.length} sessions roles`);
        return {success: 'ok'};
    });
};

CloudConnector.prototype.updateUserData = async function ({ users, logger }) {
    return await this.sessionManager.withSession(logger, 'cloud:updateUserData', async sessionData => {
        let userDataMap = new Map(users.map(({ userId, email, fullName }) => [userId, { email, fullName }]));
        let userIds = users.map(user => user.userId);
        const usernames = userIds.map(buildSessionUsernameFromCloudUserId);
        const allSessions = await sessionData.dbConnector.getSessionsForUsernames({ usernames });
        let sessionsToUpdate = [];
        for (let session of allSessions) {
            if (session.USERINFO) {
                let userInfo = JSON.parse(session.USERINFO);
                if (userDataMap.has(userInfo.userId)) {
                    let userInfoToUpdate = false;
                    let { email, fullName } = userDataMap.get(userInfo.userId);
                    if (email !== userInfo.email) {
                        userInfo.email = email;
                        userInfoToUpdate = true;
                    }
                    if (fullName !== userInfo.displayName) {
                        userInfo.displayName = fullName;
                        userInfoToUpdate = true;
                    }
                    if (userInfoToUpdate) {
                        sessionsToUpdate.push({ token: session.TOKEN, userInfo });
                    }
                }
            }
        }
        for (let { token, userInfo } of sessionsToUpdate) {
            await sessionData.dbConnector.updateSessionUserInfoByToken({ token, userInfo });
        }
        logger.info(`Updated ${sessionsToUpdate.length} session userInfo`);
        return {success: 'ok'};
    });
};

CloudConnector.prototype._broadcastChangeSessionRolesMessage = function (sessionRoles) {
    // Separate the session tokens into individual lists for each archive
    let sessionsMap = new Map();
    for (let { archiveId, token, role } of sessionRoles) {
        if (!sessionsMap.has(archiveId)) {
            sessionsMap.set(archiveId, []);
        }
        sessionsMap.get(archiveId).push({ token, role: getSessionRoleFromString(role) });
    }

    // Broadcast an RTC message for each archive
    for (let [archiveId, sessions] of sessionsMap.entries()) {
        if (sessions.length > 0) {
            this.serverUtils.broadcastRTCMessage(archiveId, undefined, undefined, undefined, { operation: 'changeSessionRoles', sessions });
        }
    }
};

CloudConnector.prototype.directAPICallRouter = async function (method, p_req, p_res) {
    assertBASRequest(p_req);


    let logger = p_req.bas.logger.getLogger({action: "cloud/" + p_req.params.api, module: "cloud"});
    logger.info("direct api call " + p_req.params.api);

    if (method === "POST" && p_req.params.api === "mark-projects-as-deleted") {
        if (!(await p_req.bas.verifyAdminCredentials())) {
            return {error: 'Unauthorized'};
        } else {
            return await this.markProjectsAsDeletedByProjectIds({
                projectIds: p_req.body.projectIds,
                logger,
            });
        }
    }

    if (method === "POST" && p_req.params.api === "invalidate-user-sessions") {
        if (!(await p_req.bas.verifyAdminCredentials())) {
            return {error: 'Unauthorized'};
        } else {
            return await this.invalidateSessionsByUserIds({
                userIds: p_req.body.userIds,
                logger
            });
        }
    }

    if (method === "POST" && p_req.params.api === "update-project-sessions") {
        if (!(await p_req.bas.verifyAdminCredentials())) {
            return {error: 'Unauthorized'};
        } else {
            return await this.updateProjectSessions({
                projects: p_req.body.projects.map(project => ({
                    projectId: project.projectId,
                    users: project.users || [],
                    otherUsersRole: project.otherUsersRole || null,
                })),
                logger
            });
        }
    }

    if (method === "POST" && p_req.params.api === "update-user-data") {
        if (!(await p_req.bas.verifyAdminCredentials())) {
            return {error: 'Unauthorized'};
        } else {
            return await this.updateUserData({
                users: p_req.body.users,
                logger
            });
        }
    }

    if (method === "POST" && p_req.params.api === "get-create-image-unfurling") {
        if (!(await p_req.bas.verifyAdminCredentials())) {
            return {error: 'Unauthorized'};
        } else {
            return await this.getOrCreatePermalinkUnfurling({
                p_req,
                logger
            });
        }
    }

    if (method === "POST" && p_req.params.api === "delete-image-unfurling-data") {
        if (!(await p_req.bas.verifyAdminCredentials())) {
            return {error: 'Unauthorized'};
        } else {
            return await this.deleteImageUnfurlingData({
                p_req,
                logger
            });
        }
    }

    logger.error("unsupported API");
    return {error: "Unsupported API"};
};

CloudConnector.prototype.directAPICall = function(method, p_req, p_res) {
    this.directAPICallRouter(method, p_req, p_res)
        .then(result => {
            if (!result) {
                result = { success: 'ok' };
            }
            returnJson(p_res, result);
        })
        .catch(err => {
            p_req.bas.logger.error('Unexpected error on API Call', err);
            returnJson(p_res, {error: err.message});
        });
};

CloudConnector.prototype.getDataResidency = async function(logger, platformSiteID, platformArchiveID) {
    logger = logger.getLogger({action: 'getDataResidency', module: 'cloud'});

    const url = this.cloudServerBaseUrl + '/s' + encodeBase36(platformSiteID) + '/p' + encodeBase36(platformArchiveID) + '/data_residency';
    const username = this.cloudBasicAuthCredentials.username;
    const password = this.cloudBasicAuthCredentials.password;

    const response = await new Promise((resolve, reject) => {
        superagent.get(url)
            .set('Authorization', 'Basic ' + Buffer.from(username + ':' + password).toString('base64'))
            .set('User-Agent', BAS_USER_AGENT)
            .send({ siteId: platformSiteID, projectId: platformArchiveID })
            .end((err, res) => {
                if (err) {
                    logger.info('Unexpected error calling Cloud (Project ID ' + platformArchiveID + ', Site ID ' + platformSiteID + ') at ' + url + ': ' + err);
                    reject(new Error('Error calling Cloud server'));
                } else if (res.status !== 200) {
                    logger.info('Unexpected ' + res.status + ' status code when calling Cloud (Project ID ' + platformArchiveID + ', Site ID ' + platformSiteID + ') at ' + url);
                    reject(new Error('Error calling Cloud server'));
                } else {
                    resolve(res.body);
                }
            });
    });

    if(
      !response ||
      typeof response != 'object' ||
      !('dataResidency' in response) ||
      !([null, ...DATA_RESIDENCIES].includes(response.dataResidency))
    ) {
        logger.info('Unexpected ' + JSON.stringify(response) + ' response calling Cloud (Project ID ' + platformArchiveID + ', Site ID ' + platformSiteID + ') at ' + url);
        throw new Error(`Invalid dataResidency response from Cloud server`);
    }

    return response.dataResidency;
}

function returnJson(res, data) {
    const jsonData = JSON.stringify(data);
    res.setHeader('Content-Type', 'application/json');
    res.setHeader('Content-Length', Buffer.byteLength(jsonData));
    res.write(jsonData);
    res.end();
}

export {
    CloudConnector
};
