// A connector that mimics a file repository like S3
import * as Consts from '@balsamiq/bmpr/lib/BalsamiqArchiveConstants.js';
import bmprUtilsMod from '@balsamiq/bmpr/lib/BmprUtils.js';
import type { Logger } from '@balsamiq/logging';
import assert from 'assert';
import type { Request, Response } from 'express';
import fs from 'fs';
import shortUUID from 'short-uuid';
import { Readable } from 'stream';
import * as uuid from 'uuid';
import type { BASLegacyCallback } from '../calling-style.ts';
import { callSaneFunctionFromLegacy } from '../calling-style.ts';
import type { Config } from '../configLoader.ts';
import con from '../constants.ts';
import type { PermalinkExtra, User } from '../database.ts';
import type { PermalinkData } from '../model.ts';
import type { DBConnector } from '../database.ts';
import type { DataResidencyName } from '../environment-variables-schemas.ts';
import type { Metrics } from '../metrics.ts';
import type { RedisAdapter } from '../redisAdapter.ts';
import type { BASRequest } from '../request-context.ts';
import { type PermalinkImageFormat, type S3PermalinksImageStorageAdapter } from '../s3adapter.ts';
import type { ServerUtils } from '../server_utils.js';
import type { SessionData, SessionManager } from '../session-manager.ts';
import { pipeStreamToResponse } from '../utils.ts';
import type { Wireframe2imageAdapter } from '../wireframe2image-adapter.ts';
import type { Connector } from './connectors-registry.ts';

export class WebDemoConnector implements Connector {
    logger: Logger;
    sessionManager: SessionManager;
    generalAccessPlatformToken: string;
    config: Config;
    archiveIDPrefix: string;
    permalinkImageStorageAdapter: S3PermalinksImageStorageAdapter;
    w2iAdapter: Wireframe2imageAdapter;
    baseUrl: string;
    serverUtils: ServerUtils;

    constructor(
        sessionManager: SessionManager,
        logger: Logger,
        metrics: Metrics,
        config: Config,
        permalinkImageStorageAdapter: S3PermalinksImageStorageAdapter,
        w2iAdapter: Wireframe2imageAdapter,
        serverUtils: ServerUtils,
        _redisAdapter: RedisAdapter
    ) {
        this.logger = logger.getLogger({ module: 'wd' });
        this.sessionManager = sessionManager;
        this.logger.info('#WD# Connector', { action: 'startup' });
        this.generalAccessPlatformToken = '46d7b03c-d63e-47b2-9ebe-6e2a8c09012b';
        this.config = config;
        this.archiveIDPrefix = config.archiveIDPrefix;
        this.permalinkImageStorageAdapter = permalinkImageStorageAdapter;
        this.w2iAdapter = w2iAdapter;
        this.baseUrl = config.baseUrl;
        this.serverUtils = serverUtils;
    }

    getUniqueUserId(user: User): string {
        return 'wd_' + user.USERNAME;
    }

    maxAge(): number {
        // 1 h
        return 60 * 60 * 1000;
    }

    generateArchiveID(): string {
        return this.archiveIDPrefix + 'wd_' + uuid.v1().replace(/-/g, '_');
    }

    allowsArchiveCreation(dbConnector: DBConnector, platformToken: string, callback: BASLegacyCallback<{}>): void {
        callback({}); // no error = YES, you're allowed to create
    }

    getRole(
        logger: Logger,
        dbConnector: DBConnector,
        token: string,
        platformSiteID: string,
        platformArchiveID: string,
        userInfo: any,
        callback: (result: { role: number }) => void
    ): void {
        callback({ role: con.ROLE_ADMIN });
    }

    getBufferFromSpecificTemplate(templateName: string, callback: BASLegacyCallback<{ buffer: Buffer; id: string }>): void {
        const archiveID = this.generateArchiveID();
        const template = `./connectors/templates/${con.BMPR_CURRENT_SCHEMA_VERSION}/${templateName}.bmpr`;
        fs.readFile(template, (err, data) => {
            if (err) {
                callback({ error: err });
            } else {
                callback({ buffer: data, id: archiveID });
            }
        });
    }

    getBufferFromTemplate(callback: BASLegacyCallback<{ buffer: Buffer; id: string }>): void {
        const archiveID = this.generateArchiveID();
        const template = `./connectors/templates/${con.BMPR_CURRENT_SCHEMA_VERSION}/webdemo.bmpr`;
        fs.readFile(template, (err, data) => {
            if (err) {
                callback({ error: err });
            } else {
                callback({ buffer: data, id: archiveID });
            }
        });
    }

    userConnectedToArchive(logger: Logger, dbConnector: DBConnector, sessionToken: string, callback?: (result: {}) => void): void {
        // logger.info("#WD# userConnectedToArchive " + sessionToken);
        callback && callback({});
    }

    // calls back callback with status and a Buffer object with the data in it
    create(
        logger: Logger,
        dbConnector: DBConnector,
        authToken: string,
        platformSiteID: string,
        platformArchiveName: string,
        platformInfo: any,
        buffer: Buffer,
        callback?: BASLegacyCallback<void>
    ): void {
        // N/A, doesn't hold the truth
        callback && callback({ error: 'Not available' });
    }

    // calls back callback with status and a Buffer object with the data in it
    loadFromPlatform(
        logger: Logger,
        dbConnector: DBConnector,
        authToken: string,
        platformSiteID: string,
        platformArchiveID: string,
        options: null,
        platformInfo: Record<string, unknown>,
        callback: BASLegacyCallback<{ id: string; buffer: Buffer }>
    ): void {
        // N/A, doesn't hold the truth
        callback && callback({ error: 'Call create first', ignore: true });
    }

    deleteFromPlatform(
        logger: Logger,
        dbConnector: DBConnector,
        authToken: string,
        platformSiteID: string,
        platformArchiveID: string,
        callback?: (result: { warning: string }) => void
    ): void {
        callback && callback({ warning: 'Not really holding the truth' });
    }

    // options: { fromClose, fromRestore }
    save(
        logger: Logger,
        dbConnector: DBConnector,
        user: User | null,
        archiveID: string,
        newPlatformArchiveName: string | null,
        archiveRevision: number,
        buffer: Buffer,
        dump: BmprDump,
        options: { fromClose?: boolean; fromRestore?: boolean },
        callback: BASLegacyCallback<{}>
    ): void {
        // faking the save updating platform info revision number
        dbConnector.getPlatformData(archiveID, (platformData: any) => {
            if (platformData.error) {
                callback(platformData);
            } else {
                const platformInfo = platformData.PLATFORM_INFO ? JSON.parse(platformData.PLATFORM_INFO) : {};
                platformInfo.archiveRevisionOnPlatform = archiveRevision;
                dbConnector.updateArchivePlatformData(
                    platformData.BAS_ARCHIVE_ID,
                    platformData.PLATFORM_KIND,
                    platformData.PLATFORM_SITE_ID,
                    platformData.PLATFORM_ARCHIVE_ID,
                    newPlatformArchiveName,
                    platformInfo,
                    callback
                );
            }
        });
    }

    directAPICall(method: string, req: Request, res: Response): void {
        const data = JSON.stringify({ error: 'Not available' });
        res.setHeader('Content-Type', 'text/json');
        res.setHeader('Content-Length', Buffer.byteLength(data));
        res.write(data);
        res.end();
    }

    everyoneLeft(archiveID: string, callback: (result: {}) => void): void {
        // do nothing
        callback({});
    }

    setPlatformArchiveName(
        platformArchiveID: string,
        authToken: string,
        platformArchiveName: string,
        callback: (result: {}) => void
    ): void {
        // do nothing
        callback({});
    }

    aboutToSetArchiveAttributes(
        logger: Logger,
        sessionData: SessionData,
        authToken: string,
        attributes: any,
        platformData: any,
        dbConnector: DBConnector,
        callback: (result: {}) => void
    ): void {
        // do nothing
        callback({});
    }

    getAuthTokenFromPlatform(logger: Logger, platformInfo: any, callback?: (result: { platformToken: string }) => void): void {
        callback && callback({ platformToken: 'dummyToken' });
    }

    logUserEvent(
        logger: Logger,
        dbConnector: DBConnector,
        ip: string,
        userInfo: any,
        userEvent: any,
        platformData: any,
        callback: (result: {}) => void
    ): void {
        // nothing to do
        callback({});
    }

    getProjectMembers(
        logger: Logger,
        dbConnector: DBConnector,
        ip: string,
        userInfo: any,
        userEvent: any,
        platformData: any,
        callback: (result: {}) => void
    ): void {
        // nothing to do
        callback({});
    }

    projectHasBeenSavedOnPlatform(
        logger: Logger,
        dbConnector: DBConnector,
        platformSiteID: string,
        platformArchiveID: string,
        callback: (result: { status: boolean }) => void
    ): void {
        callback({ status: true });
    }

    projectExistsOnPlatform(
        logger: Logger,
        dbConnector: DBConnector,
        platformSiteID: string,
        platformArchiveID: string,
        callback: (result: { status: boolean }) => void
    ): void {
        callback({ status: true });
    }

    checkUserInfo(
        logger: Logger,
        dbConnector: DBConnector,
        userInfo: any,
        authToken: string,
        callback?: BASLegacyCallback<{ success: string }>
    ): void {
        if (authToken === this.generalAccessPlatformToken) {
            callback && callback({ success: 'ok' });
        } else {
            callback && callback({ error: 'wrong access token' });
        }
    }

    getUserKeyFromPlatformToken({ platformToken, dbConnector }: { platformToken: string; dbConnector: DBConnector }): Promise<null> {
        return Promise.resolve(null);
    }

    async storePermalinkImage({
        dataStream,
        mimeType,
        permalinkID,
        permalinkInfo,
    }: {
        dataStream: Readable;
        mimeType: string;
        permalinkID: string;
        permalinkInfo?: PermalinkData['permalinkInfo'];
    }): Promise<unknown> {
        const format = permalinkInfo?.image?.format ? permalinkInfo.image.format : 'png';
        const dataResidency = permalinkInfo?.dataResidency ?? this.config.defaultDataResidencyName;
        return await this.permalinkImageStorageAdapter.uploadPermalinkImage({
            dataStream,
            mimeType,
            permalinkID,
            platformKind: 'wd',
            format,
            dataResidency,
        });
    }

    async deletePermalinkImages({
        permalinkIDs,
        format = 'png',
        dataResidency = this.config.defaultDataResidencyName,
    }: {
        permalinkIDs: string[];
        format?: PermalinkImageFormat;
        dataResidency?: DataResidencyName;
    }): Promise<unknown> {
        return await this.permalinkImageStorageAdapter.deletePermalinkImages({
            permalinkIDs,
            platformKind: 'wd',
            format,
            dataResidency,
        });
    }

    async deletePermalinkThumbnailImages({ permalinkIDs }: { permalinkIDs: string[] }): Promise<unknown> {
        return await this.permalinkImageStorageAdapter.deletePermalinkThumbnailImages({
            permalinkIDs,
            platformKind: 'wd',
        });
    }

    async generatePermalinkImage({
        logger,
        permalinkData,
        attachment,
        suffix,
    }: {
        logger: Logger;
        permalinkData: PermalinkData;
        attachment?: {
            file: Readable;
            mimeType: string;
        };
        suffix?: string;
    }): Promise<unknown> {
        const { platformKind, platformSiteID, platformArchiveID, resourceID, branchID, platformInfo, permalinkInfo } = permalinkData;
        let ret = {};
        try {
            if (this.w2iAdapter) {
                const platformToken = this.generalAccessPlatformToken;

                ret = await this.w2iAdapter.invoke({
                    baseUrl: this.baseUrl,
                    branchID: branchID,
                    resourceID: resourceID,
                    platformArchiveID: platformArchiveID,
                    platformSiteID: platformSiteID,
                    platformToken: platformToken,
                    platformInfo: platformInfo,
                    platformKind: platformKind,
                    permalinkInfo,
                    permalinkID: [permalinkData.permalinkID, suffix].join(''),
                    thumbnailSize: 392,
                    authorizationHeaderString: await this.serverUtils.getBASAuthorizationHeaderString(logger),
                    dataResidency: this.config.defaultDataResidencyName,
                });
            } else {
                // attachment format is "png" by design
                if (attachment && attachment.file && attachment.mimeType) {
                    await this.storePermalinkImage({
                        dataStream: attachment.file,
                        mimeType: attachment.mimeType,
                        permalinkID: permalinkData.permalinkID,
                    });
                }
            }
        } catch (error) {
            // error action handled by the caller
            throw error;
        }

        return ret;
    }

    getPermalink(
        {
            permalinkData,
            releaseSessionIfNotReleased,
            req,
            res,
        }: {
            permalinkData: PermalinkData;
            releaseSessionIfNotReleased: () => Promise<void>;
            req: BASRequest;
            res: Response;
        },
        cb: (result: any) => void
    ): void {
        callSaneFunctionFromLegacy(this.asyncGetPermalink({ permalinkData, releaseSessionIfNotReleased, req, res }), cb);
    }

    async asyncGetPermalinkThumbnail(permalinkData: PermalinkData, logger: Logger): Promise<string> {
        const { permalinkID, platformKind, permalinkInfo } = permalinkData;
        const format = permalinkInfo?.image?.format ? permalinkInfo.image.format : 'png';
        const dataResidency = permalinkInfo?.dataResidency ?? this.config.defaultDataResidencyName;
        try {
            return await this.permalinkImageStorageAdapter.getPermalinkImageBase64({ permalinkID, platformKind, format, dataResidency });
        } catch (err) {
            // permalink image could be not ready yet
            await this.generatePermalinkImage({ logger, permalinkData });
            return await this.permalinkImageStorageAdapter.getPermalinkImageBase64({ permalinkID, platformKind, format, dataResidency });
        }
    }

    async asyncGetPermalink({
        permalinkData,
        releaseSessionIfNotReleased,
        req,
        res,
    }: {
        permalinkData: PermalinkData;
        releaseSessionIfNotReleased: () => Promise<void>;
        req: BASRequest;
        res: Response;
    }): Promise<{}> {
        const { permalinkID, platformKind, permalinkInfo } = permalinkData;
        const format = permalinkInfo?.image?.format ? permalinkInfo.image.format : 'png';
        const dataResidency = permalinkInfo?.dataResidency ?? this.config.defaultDataResidencyName;
        const _pipeStream = async () => {
            const stream = await this.permalinkImageStorageAdapter.getPermalinkImageStream({
                platformKind,
                permalinkID,
                format,
                dataResidency,
            });
            await releaseSessionIfNotReleased();
            assert(stream !== undefined);
            assert(stream instanceof Readable, 'stream is not a Readable stream');
            return await pipeStreamToResponse(stream, res, [['Content-Type', 'image/png']]);
        };
        try {
            return await _pipeStream();
        } catch (err) {
            try {
                await this.generatePermalinkImage({ logger: req.bas.logger, permalinkData });
                return await _pipeStream();
            } catch (error) {
                // error action handled by the caller
                throw error;
            }
        }
    }

    makePermalinkID(): string {
        return shortUUID.generate();
    }

    getPermalinkExtraFromPermalinkData(permalinkData: PermalinkData): PermalinkExtra {
        const { permalinkInfo, permalinkID, permalinkKind } = permalinkData;
        const kindForPath = permalinkKind === Consts.PermalinkKind.public_share ? 'default' : 'wd';
        const format = permalinkInfo?.image?.format ? permalinkInfo.image.format : 'png';
        const image = bmprUtilsMod.composeImagePermalinkUrl(
            this.config.shareUrls[this.config.defaultDataResidencyName],
            permalinkID,
            kindForPath,
            format
        );
        return {
            image:
                permalinkKind === Consts.PermalinkKind.image || permalinkKind === Consts.PermalinkKind.image_unfurling ? image : undefined,
            edit: permalinkInfo.edit ? permalinkInfo.edit : undefined,
            comment: permalinkInfo.comment ? permalinkInfo.comment : undefined,
            view: permalinkInfo.view ? permalinkInfo.view : undefined,
        };
    }

    getSnapshotUrl(baseDir: string, permalinkID: string, format: string, dataResidency: DataResidencyName): string {
        return '';
    }

    generateSnapshot = async function ({
        logger,
        platformKind,
        platformSiteID,
        platformArchiveID,
        resourceID,
        branchID,
        platformInfo,
        permalinkInfo,
        dataResidency,
    }: {
        logger: Logger;
        platformKind: string;
        platformSiteID: string;
        platformArchiveID: string;
        resourceID: string;
        branchID: string;
        platformInfo: Record<string, unknown>;
        permalinkInfo: PermalinkData['permalinkInfo'];
        dataResidency: DataResidencyName;
    }) {
        throw new Error('Not implemented');
    };
}
