/** @noformat */
import * as jwt from './lib/jwt.js';
import moment from 'moment';
import superagent from 'superagent';
import httpreq from 'httpreq';
import fs from 'fs';
import FormData from 'form-data';
import UrlModule from 'url';
import * as uuid from 'uuid';
import pathUtil from 'path';
import JSZip from 'jszip';
import { parseRedisKey, doAddKeyInRedis, pipeStreamToResponse, pipeToNewRequest, removePrefix, isImage, decodeJWT } from '../utils.ts';
import con from '../constants.ts';


// var et = require('elementtree');
import shortUUID from 'short-uuid';

import { callSaneFunctionFromLegacy, isBASLegacyErrorObject } from '../calling-style.ts';
import { verifyAtlassianJWT, returnRenderConfluenceImage, ************************, verifyCallFromAtl<PERSON>ian, isAtlassianNewLifeCycleActive } from '../utils.ts';

import bmprUtilsMod from '@balsamiq/bmpr/lib/BmprUtils.js';
import { callWithLegacyCallback } from '../calling-style.ts';
import * as Consts from '@balsamiq/bmpr/lib/BalsamiqArchiveConstants.js'

const connector_id = "confluence";

const anonAvatarURL = "https://bas20.balsamiq.com/bw-atlassian/pict/anonymous.png";

const confluence_watcher_tag = 'confluence_watcher';

// TODO: this is currently initialized in the ConfluenceConnector constructor.
/** @type { JSDocTypes.ConfluenceAdapter } */
let confluenceAdapter;

/**
 * @class ConfluenceConnector
 * @implements {JSDocTypes.Connector}
 * @param {JSDocTypes.SessionManager} sessionManager
 * @param {JSDocTypes.Logger} logger
 * @param {JSDocTypes.Metrics} metrics
 * @param {JSDocTypes.Config} config
 * @param {JSDocTypes.ServerUtils} serverUtils
 * @param {JSDocTypes.S3PermalinksImageStorageAdapter} permalinkImageStorageAdapter
 * @param {JSDocTypes.Wireframe2imageAdapter} w2iAdapter
 * @param {JSDocTypes.RedisAdapter} redisAdapter
 * @param {JSDocTypes.AtlassianApiAdapter} confluenceAdapter
 */
var ConfluenceConnector = function (sessionManager, logger, metrics, config, serverUtils, permalinkImageStorageAdapter, w2iAdapter, redisAdapter, _confluenceAdapter) {
    this.logger = logger.getLogger({module: 'confluence'});
    this.metrics = metrics;
    this.sessionManager = sessionManager;
    this.config = config;
    this.archiveIDPrefix = config.archiveIDPrefix;
    this.serverUtils = serverUtils;
    this.redisAdapter = redisAdapter;
    this.permalinkImageStorageAdapter = permalinkImageStorageAdapter;
    this.w2iAdapter = w2iAdapter;
    this.baseUrl = config.baseUrl;
    confluenceAdapter = _confluenceAdapter;

    this.logger.info("#CONFLUENCE# Connector", {action: "startup"});
};

ConfluenceConnector.prototype.getUniqueUserId = function (user) {
    return "confluence_" + user.USERNAME;
};

/**
 * @returns {number}
 */
ConfluenceConnector.prototype.maxAge = function () {
    // 30 gg
    return 30 * 24 * 60 * 60 * 1000;
};

/**
 * @returns {string}
 */
ConfluenceConnector.prototype.generateArchiveID = function () {
    return this.archiveIDPrefix + generateArchiveID();
};

/**
 * @param {JSDocTypes.DBConnector} dbConnector
 * @param {string} platformToken
 * @param {() => void} callback
 */
ConfluenceConnector.prototype.allowsArchiveCreation = function (dbConnector, platformToken, callback) {
    callback({}); // no error = YES, you're allowed to create
};

ConfluenceConnector.prototype.checkUserInfo = function (logger, dbConnector, userInfo, platformToken, callback) {
    var claims;
    var userAccountId;
    var sessionManager = this.sessionManager;
    logger = logger.getLogger({
        module: "confluence",
        action: "checkUserInfo"
    });
    let config = this.config;

    if (userInfo.isAnonymous) {
        callback({success: "ok"});
    } else {
        try {
            claims = jwt.decode(platformToken, '', true);
        } catch (e) {
            logger.error("Unable to decode JWT, platform token is " + platformToken);
            claims = {};
        }

        if (claims.sub) {
            userAccountId = claims.sub;
            if (userAccountId !== userInfo.name) {
                callback({error: `userID is not matching: ${userAccountId} !== ${userInfo.name}`});
            } else {
                if (userInfo.avatarUrl || userInfo.displayName || userInfo.email) {
                    sessionManager.createSession(logger, "checkUserInfo", function (obj) {
                        var sessionData, dbConnector;
                        if (obj.error) {
                            callback({error: "Cannot get db session: " + obj.error});
                        } else {
                            sessionData = obj;
                            dbConnector = sessionData.dbConnector;
                            getAccessTokenFromJWT(logger, dbConnector, platformToken, "READ ACT_AS_USER", function (obj) {
                                let instanceObj;
                                if (obj.error) {
                                    callback && callback(obj)
                                } else {
                                    sessionManager.releaseSession(sessionData);
                                    instanceObj = obj.instanceObj;
                                    if (instanceObj.error) {
                                        callback({error: "Cannot get connector data: " + instanceObj.error});
                                    } else {
                                            getUserDetails(dbConnector, config, instanceObj.baseUrl, userInfo.name, platformToken, obj.accessToken, {}, logger, function (obj) {
                                            let errorMessage;
                                            // {
                                            //     "avatarUrl": userInfo.avatarUrl,
                                            //     "fullName": userInfo.displayName,
                                            //     "id": userInfo.accountId,
                                            //     "userName": userInfo.name,
                                            //     "email": userInfo.email
                                            // }
                                            if (obj.error) {
                                                callback && callback({error: `error retrieving user details: ${userInfo.name} from ${instanceObj.baseUrl}`});
                                            } else {
                                                if (userInfo.avatarUrl && userInfo.avatarUrl !== obj.avatarUrl) {
                                                    errorMessage = `avatarUrl is not matching: ${userInfo.avatarUrl} !== ${obj.avatarUrl}`;
                                                } else if (userInfo.displayName && userInfo.displayName !== obj.fullName) {
                                                    errorMessage = `displayName is not matching: ${userInfo.displayName} !== ${obj.fullName}`;
                                                } else if (userInfo.email && userInfo.email !== obj.email) {
                                                    errorMessage = `displayName is not matching: ${userInfo.email} !== ${obj.email}`;
                                                }

                                                if (errorMessage) {
                                                    callback && callback({error: "user info check failed: " + errorMessage});
                                                } else {
                                                    callback && callback({success: "ok"});
                                                }
                                            }
                                        });
                                    }
                                }
                            });
                        }
                    });
                } else {
                    // nothing to check
                    callback && callback({success: "ok"});
                }
            }
        } else {
            callback({error: "missing user account ID information"});
        }
    }
};

/**
 * @param {Object} param0
 * @param {string} param0.platformToken
 * @param {JSDocTypes.DBConnector} param0.dbConnector
 * @param {JSDocTypes.Logger} param0.logger
 * @returns
 */
ConfluenceConnector.prototype.getUserKeyFromPlatformToken = function ({ platformToken, dbConnector, logger }) {
    if (!platformToken) {
        return Promise.resolve(null);
    }

    return decodeJWT({
        connectorId: 'confluence',
        dbConnector,
        platformToken,
        noVerify:false,
        logger,
    }).then(result => {
        const { sub, iss } = result.claims;
        return `confluence-${sub}-${iss}`;
    }).catch(err => {
        logger.info("Cannot decode JWT token", {fn: "getUserKeyFromPlatformToken", module: "confluence", err, platformToken});
        return null;
    });
};

/**
 * @param {JSDocTypes.Logger} logger
 * @param {JSDocTypes.DBConnector} dbConnector
 * @param {string} token
 * @param {string} platformSiteID
 * @param {string} platformArchiveID
 * @param {object} userInfo
 * @param {function} callback
 */
ConfluenceConnector.prototype.getRole = function(logger, dbConnector, platformToken, platformSiteID, platformArchiveID, userInfo, callback)
{
    var requesterClaims;
    logger = logger.getLogger({action: "getRole", module: "confluence", platformArchiveID: platformArchiveID, platformSiteID: platformSiteID});
    var ret = decodePlatformSiteID(platformSiteID, userInfo);
    var platformInfo;
    const config = this.config;

    if (ret.error) {
        logger.error("Malformed platformSiteID: " + platformSiteID);
        callback(ret);
    } else {
        if (userInfo.isAnonymous) {
            // no platformToken has been provided, we are in a public share link scenario
            // FIX: security report: https://tracker.bugcrowd.com/balsamiq/submissions/3f345adf-c8c2-416e-b612-ad8c0ee32de6
            callSaneFunctionFromLegacy(dbConnector.hasPublicShare({platformKind: "confluence", platformSiteID, platformArchiveID}), function(obj) {
                if (obj.hasPublicShare) {
                    if (userInfo.platformInfo) {
                        platformInfo = userInfo.platformInfo;
                        // retrieve the token from servlet
                        this.getAuthTokenFromPlatform(logger, platformInfo, function (obj) {
                            if (obj.error) {
                                logger.error("anonymous user, unable to get platform token for archive ", userInfo);
                                callback && callback({error: "anonymous user, unable to get token info for archive"});
                            } else {
                                platformToken = obj.platformToken;
                                logger.info("anonymous user, we fallback giving VIEWER permissions, platformArchiveID " + platformArchiveID, userInfo);
                                callback && callback({role: con.ROLE_VIEWER, platformToken: platformToken});
                            }
                        }.bind(this));
                    } else {
                        logger.error("anonymous user, missing platform info " + platformArchiveID, userInfo);
                        callback && callback({error: "anonymous user, missing platform info"});
                    }
                } else {
                    logger.error("anonymous user, no public share for archive " + platformSiteID + " " + platformArchiveID);
                    callback && callback({error: "anonymous user, no public share"});
                }
            }.bind(this));
        } else {
            dbConnector.getConnectorData("confluence", ret.iss, function (obj) {
                var sharedSecret, secondsValidityLeft, exp;

                if (obj.error) {
                    callback && callback(obj);
                } else {
                    sharedSecret = obj.sharedSecret;
                    try {
                        requesterClaims = jwt.decode(platformToken, sharedSecret, false);
                    } catch (err) {
                        logger.error("invalid platform token: " + err, err, {platformToken});
                        requesterClaims = {};
                    }

                    if (requesterClaims && requesterClaims.iss && requesterClaims.iss === ret.iss) {
                        // Test expiration timestamp from the JWT
                        exp = requesterClaims.exp || 0;
                        secondsValidityLeft = exp - (new Date().getTime() / 1000);
                        if (secondsValidityLeft < 0) {
                            logger.warn('WARNING JWT expired (' + (-Math.round(secondsValidityLeft)) + ' seconds ago)', {platformToken, userInfo, action: dbConnector.sessionData.action});
                            // TODO: as a preliminary step we just log the case where the JWT is expired
                            // callback({error: 'JWT expired'});
                            // return;
                        }

                        getRoleAccordingToUserPermission(logger, dbConnector, obj, platformToken, userInfo.platformInfo, config, callback);
                    } else {
                        callback({error: "not authorized"});
                    }
                }
            });
        }
    }
};

function _checkDeprecationHeaders(httpResponse, logger, path = null, appName = null) {
    logger = logger.getLogger({checkDeprecationHeaders: path});
    if (httpResponse?.headers?.warning && httpResponse?.headers?.deprecation && httpResponse?.headers?.link) {
        logger.warn(`API deprecated: warning - ${httpResponse.headers.warning} deprecation - ${httpResponse.headers.deprecation} link - ${httpResponse.headers.link}`);
    } else {
        if (!(appName && appName.includes("production"))) {
            logger.info("API not deprecated"); // TODO: remove this logline (we're interested only in deprecated APIs)
        }
    }
}

var getRoleAccordingToUserPermission = function (logger, dbConnector, instanceObj, platformToken, platformInfo, config, callback) {
    var scopes = "WRITE ACT_AS_USER";
    logger = logger.getLogger({action: "getRoleAccordingToUserPermission", module: "confluence", contentID: platformInfo.contentID});

    if (platformInfo && platformInfo.contentID) {
        try {
            // JWT signature has been already tested
            var claims = jwt.decode(platformToken, '', true);
            var iss = claims.iss;
            var userKey, userAccountId;
            if (claims.sub) {
                // GDPR: userKey deprecated, use urn:atlassian:connect:useraccountid instead
                userAccountId = claims.sub;
            } else if (claims.context && claims.context.user && claims.context.user.userKey) {
                userKey = claims.context.user.userKey;
            }

            if (instanceObj.oauthClientId && (userKey || userAccountId)) {
                if (userAccountId) {
                    dbConnector.pauseSession(function (obj) {
                        var resumeSession;
                        if (obj.error) {
                            callback && callback(obj);
                        } else {
                            resumeSession = obj.resumeSession;
                            var finalise = function (userPermission) {
                                let role;
                                resumeSession("confluence-getRoleAccordingToUserPermission", function (obj) {
                                    if (obj.error) {
                                        callback && callback(obj);
                                    } else {
                                        if (userPermission.hasUserUpdatePermission) {
                                            role = con.ROLE_ADMIN;
                                        } else if (userPermission.hasUserReadPermission) {
                                            role = con.ROLE_VIEWER;
                                        } else {
                                            role = con.ROLE_NO_ACCESS;
                                        }
                                        callback && callback({
                                            role: role
                                        });
                                    }
                                });
                            };

                            getAccessToken(instanceObj, userKey, userAccountId, scopes, false, function (obj) {
                                if (obj.error) {
                                    finalise(obj);
                                } else {
                                    callSaneFunctionFromLegacy(confluenceAdapter.convertIdsToTypes(instanceObj, [platformInfo.contentID], obj.accessToken), (response) => {
                                        if (response.error) {
                                            finalise(response);
                                        } else {
                                            callSaneFunctionFromLegacy((async () => {
                                                if (!(platformInfo.contentID in response.results)) {
                                                    throw new Error(`Content ID ${platformInfo.contentID} not found in response`);
                                                }
                                                let allowedOperationsResult;
                                                if (response.results[platformInfo.contentID].startsWith("page")) {
                                                    allowedOperationsResult = await confluenceAdapter.getPageOperation(instanceObj, platformInfo.contentID, obj.accessToken);
                                                } else if (response.results[platformInfo.contentID].startsWith("blog")) {
                                                    allowedOperationsResult = await confluenceAdapter.getBlogpostOperation(instanceObj, platformInfo.contentID, obj.accessToken);
                                                }
                                                const allowedOperations = {};
                                                if (allowedOperationsResult.operations) {
                                                    allowedOperationsResult.operations.forEach(element => {
                                                        if (element.operation === "update" && (element.targetType === "page" || element.targetType === "blogpost")) {
                                                            allowedOperations.hasUserUpdatePermission = true;
                                                        } else if (element.operation === "read" && (element.targetType === "page" || element.targetType === "blogpost")) {
                                                            allowedOperations.hasUserReadPermission = true;
                                                        }
                                                    });
                                                }
                                                return allowedOperations;
                                            })(), finalise);
                                        }
                                    });
                                }
                            });
                        }
                    })
                } else {
                    // This should never happen: monitoring JWT deprecated impersonation API
                    logger.error("GDPR Unexpected use of claims.context.user.userKey ", null, {instanceObj});
                    callback({error: "Unexpected use of claims.context.user.userKey. Claim sub " + claims.sub});
                }
            } else {
                // This should never happen: monitoring JWT deprecated impersonation API
                logger.error("GDPR still using JWT authentication " + iss);
                callback({error: "Unexpected error, missing authentication parameters"});
            }
        } catch (error) {
            callback && callback({error: error});
        }
    } else {
        callback && callback({error: "malformed parameter"});
    }
};

ConfluenceConnector.prototype.getBufferFromTemplate = function(callback)
{
    var templateName = "NewProject";
    doGetBufferFromTemplate(templateName, callback);
};

function doGetBufferFromTemplate(templateName, callback) {
    var archiveID = generateArchiveID();
    var templatePath =  "./connectors/templates/" + con.BMPR_CURRENT_SCHEMA_VERSION + "/";

    fs.readFile(templatePath + templateName + ".bmpr", function (err, data) {
        if (err) {
            callback({error:err});
        } else {
            callback({buffer:data, id: archiveID});
        }
    });
}

ConfluenceConnector.prototype.userConnectedToArchive = function (logger, dbConnector, sessionToken, callback) {
    doAddKeyInRedis(makeRedisKey, this.redisAdapter, logger, sessionToken);
    callback && callback({});
};

ConfluenceConnector.prototype.create = function (logger, dbConnector, authToken, platformSiteID, platformArchiveName, platformInfo, buffer, callback)
{
    // authToken: JWT
    // platformSiteID: unique id = (iss, contentID)
    // platformArchiveName: attachment name without "bmpr" extention
    // platformInfo: {contentID, iss}
    // callback: function({newly_created_platformArchiveID})
    var contentID = platformInfo.contentID;
    var filename;
    var queryObject;
    var name = platformArchiveName;
    let config = this.config;

    if (platformInfo && platformInfo.draftStatus) {
        queryObject = {status: 'draft'};
    }

    filename = name + ".bmpr";
    doSaveToPlatform(logger, dbConnector, authToken, contentID, queryObject, filename, buffer, config, function(obj) {
        if (obj.error)
        {
            callback && callback(obj);
        } else
        {
            callback && callback({platformArchiveID: obj.id});
        }
    });
};

ConfluenceConnector.prototype.loadFromPlatform = function(logger, dbConnector, authToken, platformSiteID, platformArchiveID, options, platformInfo, callback)
{
    // authToken: JWT
    // platformSiteID: unique id = (iss, contentID) [NOT NECESSARY]
    // platformArchiveID: attachmentID
    // platformArchiveName: attachment name without "bmpr" extension? [NOT NECESSARY]
    // platformInfo: {contentID, iss} [NOT NECESSARY]
    // callback: function({id: platformArchiveID, buffer: buffer})
    var queryObject = null;
    if (platformInfo && platformInfo.draftStatus) {
        queryObject = {status: 'draft'};
    }

    doAuthFetchExt(logger, dbConnector, authToken, platformArchiveID, queryObject, true, this.config, callback);
};

/**
 * 
 * @param {JSDocTypes.Logger} logger 
 * @param {JSDocTypes.DBConnector} dbConnector 
 * @param {JSDocTypes.User | null} user 
 * @param {string} archiveID 
 * @param {string | null} newPlatformArchiveName 
 * @param {number} archiveRevision 
 * @param {Buffer} buffer 
 * @param {BmprDump} dump 
 * @param {{ fromClose?: boolean, fromRestore?: boolean }} options 
 * @param {JSDocTypes.BASLegacyCallback<{}>} callback 
 */
ConfluenceConnector.prototype.save = function(logger, dbConnector, user, archiveID, newPlatformArchiveName, archiveRevision, buffer, dump, options, callback)
{
    logger = logger.getLogger({action: "save", module: "confluence"});
    var forceFlush = dump && dump.forceFlush;
    let config = this.config;
    if (!user || !user.PLATFORM_TOKEN) {
        callback && callback({error: "missing user or user.PLATFORM_TOKEN"});
        return;
    }
    dbConnector.getPlatformData(archiveID, function(platformData) {
        if (platformData.error) {
            callback(platformData);
        }
        else {
            if (platformData.PLATFORM_INFO)
            {
                var platformInfo = JSON.parse(platformData.PLATFORM_INFO);
                var contentID = platformInfo.contentID;
                // [WORKAROUND] newPlatformArchiveName and platformData.PLATFORM_ARCHIVE_NAME could be ""
                var platformArchiveName = newPlatformArchiveName || platformData.PLATFORM_ARCHIVE_NAME || "BalsamiqProject_" + archiveID;
                var prevPlatformArchiveID = platformData.PLATFORM_ARCHIVE_ID;
                // var platformSiteID = platformData.PLATFORM_SITE_ID;
                var platformArchiveID;

                if (forceFlush || platformInfo.archiveRevisionOnPlatform < archiveRevision || newPlatformArchiveName) {
                    logger.info("saving " + prevPlatformArchiveID + " " + archiveID + (newPlatformArchiveName ? " with new platformArchiveName " + newPlatformArchiveName : ""));
                    // TODO: Monitoring JWT deprecated impersonation API
                    doUpdate(logger, dbConnector, user, contentID, prevPlatformArchiveID, platformArchiveName, buffer, this.metrics, config, function(obj) {
                        if (obj.error) {
                            logger.error("saving, failed " + archiveID + " " + obj.error);
                            callback(obj);
                        } else {

                            if (obj.id) {
                                platformArchiveID = obj.id;
                            } else {
                                logger.warn("Unexpected platformArchiveId is null after saving " + archiveID + " " + prevPlatformArchiveID, obj);
                                platformArchiveID = prevPlatformArchiveID;
                            }

                            platformInfo.archiveRevisionOnPlatform = archiveRevision;
                            dbConnector.updateArchivePlatformData(platformData.BAS_ARCHIVE_ID, platformData.PLATFORM_KIND, platformData.PLATFORM_SITE_ID, platformArchiveID, platformArchiveName, platformInfo, function(obj) {
                                if (obj.error) {
                                    logger.error("updateArchivePlatformData failed, " + archiveID + " " + obj.error);
                                    callback(obj);
                                } else {
                                    logger.info("successful saving, " + archiveID);
                                    callback({platformArchiveID: platformArchiveID, prevPlatformArchiveID: prevPlatformArchiveID});
                                }
                            }.bind(this));
                        }
                    }.bind(this));
                } else
                {
                    logger.info("saving unnecessary: archive revision is not changed, " + archiveID);
                    callback({platformArchiveID: platformData.PLATFORM_ARCHIVE_ID, wasAlreadyUpdated: true});
                }
            } else
            {
                // might be already closed by someone else
                // the recovery is not trivial: the editor should reopen the archive and apply again the unsaved changes
                callback({error: "Unable to save: BAR already closed", busy: true});
            }
        }
    }.bind(this));
};

ConfluenceConnector.prototype.deleteFromPlatform = function (logger, dbConnector, authToken, platformSiteID, platformArchiveID, callback) {
    doRemoveExt(logger, dbConnector, authToken, platformArchiveID, this.config, callback);
};

/**
 *
 * @param {JSDocTypes.SessionManager} sessionManager
 * @param {JSDocTypes.ServerUtils} serverUtils
 * @param {JSDocTypes.Logger} logger
 * @param {string} sessionToken
 * @param {JSDocTypes.Config} config
 * @param {JSDocTypes.BASLegacyCallback<{success: string}>} callback
 */
export let doCheckPermissionAndUpdateUserSession = function (sessionManager, serverUtils, logger, sessionToken, config, callback) {
    logger = logger.getLogger({
        action: "checkPermissionAndUpdateUserSession",
        module: "confluence"
    });

    // logger.info("check permissions for session " + sessionToken);

    let finalise = function (sessionData, obj) {
        // release session
        sessionManager.releaseSession(sessionData, function (/*sessionObj*/) {
            if (obj.error) {
                logger.error(obj.error);
            }

            callback && callback(obj);
        });
    };

    sessionManager.createSession(logger, "checkPermissionAndUpdateUserSession", function (obj) {
        let sessionData, dbConnector;
        if (obj.error) {
            callback && callback({error: obj.error});
        } else {
            sessionData = obj;
            dbConnector = obj.dbConnector;
            dbConnector.getUser(sessionToken, function (obj) {
                if (obj.error) {
                    // session closed
                    finalise(sessionData, obj);
                } else {
                    let archiveID = obj.ARCHIVE_ID;
                    let permission = parseInt(obj.PERMISSIONS);
                    let platformToken = obj.PLATFORM_TOKEN;

                    dbConnector.getPlatformData(archiveID, function (obj) {
                        if (obj.error) {
                            finalise(sessionData, obj);
                        } else {
                            if (!obj.PLATFORM_ARCHIVE_ID) {
                                finalise(sessionData, {skip: "nothing to do, archive is not loaded"});
                                return;
                            }

                            let platformSiteID = obj.PLATFORM_SITE_ID;
                            let platformInfo = JSON.parse(obj.PLATFORM_INFO);
                            let ret = decodePlatformSiteID(platformSiteID);
                            if (ret.error) {
                                finalise(sessionData, ret);
                                return;
                            }

                            try {
                                platformInfo = JSON.parse(obj.PLATFORM_INFO);
                            } catch (err) {
                                finalise(sessionData, {error: err});
                                return;
                            }

                            dbConnector.getConnectorData("confluence", ret.iss, function (obj) {
                                let instanceObj;
                                if (obj.error) {
                                    finalise(sessionData, obj);
                                } else {
                                    instanceObj = obj;
                                    getRoleAccordingToUserPermission(logger, dbConnector, instanceObj, platformToken, platformInfo, config, function (obj) {
                                        let role;
                                        if (obj.error) {
                                            finalise(sessionData, obj);
                                        } else {
                                            role = obj.role || con.ROLE_NO_ACCESS;
                                            // update the permission only if more restrictive (i.e. we avoid to give more permission to a read/anonymous only session)
                                            if (role < permission) {
                                                callSaneFunctionFromLegacy(dbConnector.updateSessionsPermissionsBySessionTokens({
                                                    tokens: [sessionToken],
                                                    permissions: [role]
                                                }), function (obj) {
                                                    serverUtils.broadcastRTCMessage(archiveID, undefined, undefined, undefined, {
                                                        operation: 'changeSessionRoles',
                                                        sessions: [{token: sessionToken, role}]
                                                    });

                                                    finalise(sessionData, obj);
                                                })
                                            } else {
                                                finalise(sessionData, {success: "nothing to do"});
                                            }
                                        }
                                    }.bind(this));
                                }
                            }.bind(this));
                        }
                    }.bind(this));
                }
            }.bind(this));
        }
    }.bind(this));
};

/**
 *
 * @param {string} sessionToken
 * @param {string} channelId
 * @returns {string}
 */
function makeRedisKey(sessionToken, channelId) {
    return confluence_watcher_tag + '::' + sessionToken + "::" + channelId;
}

/**
 *
 * @param {JSDocTypes.SessionManager} sessionManager
 * @param {JSDocTypes.ServerUtils} serverUtils
 * @param {JSDocTypes.Logger} logger
 * @param {JSDocTypes.Metrics} metrics
 * @param {JSDocTypes.Config} config
 * @param {boolean} configureRedisForKeySpaceEvents
 * @param {JSDocTypes.RedisAdapter} redisAdapter
 */
let doRunRedisExpirationKeyListener = async function (sessionManager, serverUtils, logger, metrics, config, configureRedisForKeySpaceEvents, redisAdapter) {
    logger = logger.getLogger({module: "confluence", action: "watcher"});

    const subRedisAdapter = redisAdapter.duplicate(logger);
    await subRedisAdapter.init();

    if (configureRedisForKeySpaceEvents) {
        logger.info("Confluence: configuring redis server to notify keyspace events");
        try {
            await subRedisAdapter.enableKeyspaceEvents();
        } catch(err) {
            logger.warn("Confluence: unable to set redis server to notify keyspace events: " + err);
        }
    }

    logger.info("Confluence: starting the redis listener");
    await subRedisAdapter.subscribeToKeyspaceEvents(async (key, channel) => {
        try {
            const parsedKey = parseRedisKey(confluence_watcher_tag, key);
            if (parsedKey && parsedKey.type === confluence_watcher_tag) {
                try {
                    const result = await callWithLegacyCallback(
                        cb => doCheckPermissionAndUpdateUserSession(sessionManager, serverUtils, logger, parsedKey.sessionToken, config, cb)
                    );

                    if (!result.skip) {
                        await doAddKeyInRedis(makeRedisKey, redisAdapter, logger, parsedKey.sessionToken);
                    }
                } catch (error) {
                    if (error.message !== "Session closed") {
                        logger.error("Confluence: ERROR renewing watcher for " + parsedKey.sessionToken + " " + error.message, error);
                    } else {
                        logger.info("Confluence: session closed, no need to renew watcher for " + parsedKey.sessionToken);
                    }
                }
            }
        } catch (err) {
            logger.warn("Confluence: catch exception on redis listener, ignore key expired, probably not a valid watcher " + channel + " " + err.message + ' ' + key);
        }
    });
    logger.info("Confluence: Redis Listener subscribed to KeyspaceEvents");
};


function getAccessToken(connectorData, userKey, userAccountId, scopes, useCache, callback) {
    callSaneFunctionFromLegacy(confluenceAdapter.getAccessToken(connectorData, userKey, userAccountId, scopes, useCache), callback);
}

function getAccessTokenFromJWT(logger, dbConnector, platformToken, scopes, callback) {
    var claims, iss, userKey, userAccountId;
    logger = logger.getLogger({action: "getAccessTokenFromJWT", module: "confluence"});
    try {
        // JWT signature will be checked right after
        claims = jwt.decode(platformToken, '', true);
        iss = claims.iss;

        if (claims.sub) {
            // GDPR: userKey deprecated, use urn:atlassian:connect:useraccountid instead
            userAccountId = claims.sub;
        } else if (claims.context && claims.context.user && claims.context.user.userKey) {
            userKey = claims.context.user.userKey;
        }
    } catch (error) {
        // malformed JWT, jwt.decode can raise an exception
        callback({error: error});
        return;
    }

    dbConnector.getConnectorData(connector_id, iss, function (connectorData) {
        if (connectorData.sharedSecret && connectorData.oauthClientId && (userKey || userAccountId)) {
            try {
                // shared secret could have changed in the meantime
                jwt.decode(platformToken, '', true);
            } catch (error) {
                logger.error("JWT signature check failed " + error, error);
                callback({error: "JWT signature check failed"});
                return;
            }

            if (userAccountId) {
                logger.info("userAccountId " + userAccountId, connectorData);

                getAccessToken(connectorData, userKey, userAccountId, scopes, true, function (obj) {
                    if (obj.error) {
                        callback(obj);
                    } else {
                        logger.info("TOKEN access token status " + obj.status + " " + (userKey || userAccountId));
                        callback({
                            accessToken: obj.accessToken,
                            iss: iss,
                            userKey: userKey,
                            userAccountId: userAccountId,
                            instanceObj: connectorData
                        });
                    }
                });
            } else {
                // deprecated userKey
                logger.warn("GDPR Unexpected use of claims.context.user.userKey. Claim sub " + claims.sub, connectorData);
                callback({error: "Unexpected use of claims.context.user.userKey. Claim sub " + claims.sub});
            }
        } else {
            logger.warn("Missing userKey or accountID " + JSON.stringify(claims) + " jwt: " + platformToken);
            callback({error: "unable to retrieve the oauth token, missing userKey or userAccountId " + JSON.stringify(claims) + " jwt: " + platformToken, migrationGDPR: true});
        }
    });
}

function doRemoveExt(logger, dbConnector, jwtToken, platformArchiveID, config, callback) {
    logger = logger.getLogger({action: "doRemoveExt", module: "confluence"});
    try {
        var path = "/rest/api/content/" + platformArchiveID + "?privacyMode=true";
        var userKey, userAccountId;
        // JWT signature will be checked right after
        var claims = jwt.decode(jwtToken, '', true);
        var iss = claims.iss;
        if (claims.sub) {
            // GDPR: userKey deprecated, use urn:atlassian:connect:useraccountid instead
            userAccountId = claims.sub;
        } else if (claims.context && claims.context.user && claims.context.user.userKey) {
            userKey = claims.context.user.userKey;
        }

        var finalise = function (err, res){
            _checkDeprecationHeaders(res, logger, path, config.appName);
            var result = {result: res};
            if (err){
                result.error = "Failed to delete the resource: " + err;
            } else {
                if (res.statusCode == 204)
                {
                    // ok
                } else if (res.statusCode == 404) {
                    // already deleted?
                    result.error = "Unable to delete the attachment: file not found";
                    result.busy = true;
                }
                else
                {
                    result.error = "Forbidden: you don't have permission to remove attachments from this content";
                }
            }
            callback && callback(result);
        };

        dbConnector.getConnectorData(connector_id, iss, function(instanceObj) {
            var scopes = "DELETE ACT_AS_USER";
            if (instanceObj.error || !instanceObj.sharedSecret)
            {
                var ret = instanceObj.error ? instanceObj : {error: "The instance seems to non be authenticated. Please try to reinstall the plugin"};
                callback && callback(ret);
            } else
            {
                if (instanceObj.sharedSecret && instanceObj.oauthClientId && (userKey || userAccountId)) {
                    try {
                        jwt.decode(jwtToken, instanceObj.sharedSecret, false);
                    } catch (error) {
                        logger.error("JWT signature check failed " + error);
                        callback({error: "JWT signature check failed"});
                        return;
                    }

                    if (userAccountId) {
                        getAccessToken(instanceObj, userKey, userAccountId, scopes, true, function(obj) {
                            var url = instanceObj.baseUrl + path;
                            if (obj.error) {
                                callback && callback(obj);
                            } else {
                                httpreq.delete(url, {
                                    url: url,
                                    json: true,
                                    headers: {
                                        "X-Atlassian-Token": "no-check",
                                        "Authorization": "Bearer " + obj.accessToken,
                                        "accept": "application/json"
                                    }
                                }, finalise);
                            }
                        })
                    } else {
                        logger.warn("GDPR Unexpected use of claims.context.user.userKey ", instanceObj);
                        callback({error: "Unexpected use of claims.context.user.userKey. Claim sub"});
                    }
                } else {
                    logger.warn("missing oauthClientId, userKey or userAccountId", instanceObj);
                    callback({error: "missing oauthClientId, userKey or userAccountId"});
                }
            }
        });
    } catch (error) {
        callback && callback({error: "Failed to delete the resource: " + error});
    }
}

function doUpdate(logger, dbConnector, user, contentID, archivePlatformID, name, buffer, metrics, config, callback) {
    var filename = name + ".bmpr";
    doUpdateToPlatform(logger, dbConnector, user, contentID, archivePlatformID, filename, buffer, metrics, config, callback);
}

function uploadBinaryExtWithRetry(url, headers, filename, buffer, logger, config, callback) {
    logger = logger.getLogger({action: "uploadBinaryExtWithRetry", url: url});
    var retry = 0;
    var MAX_RETRY = 3;
    var TIMEOUT = 300;

    var upload = function(retry) {
        retry++;
        uploadBinaryExt(url, headers, filename, buffer, logger, config,function (p_obj) {
            if (p_obj.error) {
                logger.updateParams({code: p_obj.code});
                if (p_obj.code === 500 && retry < MAX_RETRY) {
                    logger.warn("upload binary failed for archive " + filename + " " + retry + "/" + MAX_RETRY);
                    setTimeout(function() {
                        upload(retry);
                    }, TIMEOUT * retry);
                } else {
                    logger.error("upload binary failed for archive " + filename + " " + retry + "/" + MAX_RETRY);
                    callback(p_obj);
                }
            } else {
                logger.info("upload binary succeeded for archive " + filename + " " + retry + "/" + MAX_RETRY);
                callback({id: p_obj.id});
            }
        });
    };

    upload(retry);
}

function doUpdateToPlatform(logger, dbConnector, user, contentID, archivePlatformID, filename, buffer, metrics, config, callback) {
    // TODO: API_V2 it could be deprecated in the future
    var path = '/rest/api/content/' + contentID + '/child/attachment/' + archivePlatformID + '/data';
    var platformToken = user.PLATFORM_TOKEN;
    logger = logger.getLogger({action: "updateToPlatform",  module: "confluence"});

    try {
        // JWT signature will be checked right after
        var claims = jwt.decode(platformToken, '', true);
        var iss = claims.iss;
        var userKey, userAccountId;

        if (claims.sub) {
            // GDPR: userKey deprecated, use urn:atlassian:connect:useraccountid instead
            userAccountId = claims.sub;
        } else if (claims.context && claims.context.user && claims.context.user.userKey) {
            userKey = claims.context.user.userKey;
        }

        dbConnector.getConnectorData("confluence", iss, function(instanceObj) {
            var scopes = "WRITE ACT_AS_USER";
            if (instanceObj.error || !instanceObj.sharedSecret) {
                var ret = instanceObj.error ? instanceObj : {error: "The instance seems to non be authenticated. Please try to reinstall the plugin"};
                callback && callback(ret);
            } else {
                try {
                    jwt.decode(platformToken, instanceObj.sharedSecret, false);
                } catch (error) {
                    callback({error: "JWT signature check failed"});
                    return;
                }

                if (instanceObj.oauthClientId && (userKey || userAccountId)) {
                    if (userAccountId) {
                        dbConnector.pauseSession(function(obj) {
                            if (obj.error) {
                                callback && callback(obj);
                            } else {
                                var resumeSession = obj.resumeSession;
                                var finalise = function (retObj) {
                                    resumeSession("confluence-#1", function (obj) {
                                        if (obj.error) {
                                            callback && callback(obj);
                                        } else {
                                            callback && callback(retObj);
                                        }
                                    });
                                };

                                var timer = metrics.trackTimeInterval('confluence-getAccessToken');
                                getAccessToken(instanceObj, userKey, userAccountId, scopes, false, function (obj) {
                                    timer.stop();
                                    var url = instanceObj.baseUrl + path + "?privacyMode=true";
                                    var headers;
                                    if (obj.error) {
                                        finalise(obj);
                                    } else {
                                        headers = {
                                            "X-Atlassian-Token": "no-check",
                                            "Authorization": "Bearer " + obj.accessToken,
                                            "accept": "application/json"
                                        };

                                        uploadBinaryExtWithRetry(url, headers, filename, buffer, logger, config, finalise);
                                    }
                                });
                            }
                        });
                    } else {
                        logger.warn("GDPR Unexpected use of claims.context.user.userKey. Claim sub " + claims.sub, {instanceObj});
                        callback({error: "Unexpected use of claims.context.user.userKey. Claim sub " + claims.sub});
                    }
                } else {
                    logger.error("ERROR: BWCC still using JWT authentication updating attachment" + iss);
                }
            }
        });
    } catch (e) {
        callback && callback({error:{message: "Failed to save the resource: " + e.message}});
    }
}


function doSaveToPlatform(logger, dbConnector, authToken, contentID, queryObject, filename, buffer, config, callback)
{
    // TODO: API_V2 it could be deprecated in the future
    var path = '/rest/api/content/' + contentID + '/child/attachment';
    logger = logger.getLogger({action: "doSaveToPlatform", module: "confluence"});

    try {
        // JWT signature is checked right after
        var claims = jwt.decode(authToken, '', true);
        var iss = claims.iss;
        var userKey, userAccountId;
        if (claims.sub) {
            // GDPR: userKey deprecated, use urn:atlassian:connect:useraccountid instead
            userAccountId = claims.sub;
        } else if (claims.context && claims.context.user && claims.context.user.userKey) {
            userKey = claims.context.user.userKey;
        }

        dbConnector.getConnectorData("confluence", iss, function(instanceObj) {
            var scopes = "WRITE ACT_AS_USER";
            if (instanceObj.error || !instanceObj.sharedSecret) {
                var ret = instanceObj.error ? instanceObj : {error: "The instance seems to non be authenticated. Please try to reinstall the plugin"};
                callback && callback(ret);
            } else {
                try {
                    jwt.decode(authToken, instanceObj.sharedSecret, false);
                } catch (error) {
                    callback({error: "JWT signature check failed"});
                    return;
                }

                if (instanceObj.oauthClientId && (userKey || userAccountId)) {
                    if (userAccountId) {
                        dbConnector.pauseSession(function(obj) {
                            if (obj.error) {
                                callback && callback(obj);
                            } else {
                                var resumeSession = obj.resumeSession;
                                var finalise = function(retObj) {
                                    resumeSession("confluence-#2", function(obj) {
                                        if (obj.error) {
                                            callback && callback(obj);
                                        } else {
                                            callback && callback(retObj);
                                        }
                                    });
                                };

                                getAccessToken(instanceObj, userKey, userAccountId, scopes, false, function (obj) {
                                    var url = instanceObj.baseUrl + path + "?privacyMode=true";
                                    var headers;
                                    if (obj.error) {
                                        finalise(obj);
                                    } else {
                                        if (queryObject && queryObject.status) {
                                            url = url + "&status=" + queryObject.status;
                                        }

                                        headers = {
                                            "X-Atlassian-Token": "no-check",
                                            "Authorization": "Bearer " + obj.accessToken,
                                            "accept": "application/json"
                                        };
                                        uploadBinaryExtWithRetry(url, headers, filename, buffer, logger, config, finalise);
                                    }
                                });
                            }
                        })
                    } else {
                        logger.warn("GDPR Unexpected use of claims.context.user.userKey ", {instanceObj});
                        callback({error: "Unexpected use of claims.context.user.userKey. Claim sub " + claims.sub});
                    }
                } else {
                    logger.error("ERROR: BWCC still using JWT authentication saving attachment" + iss);
                }
            }
        });
    } catch (e) {
        callback && callback({error:{message: "Failed to save the resource: " + e.message}});
    }
}

function doGetAttachmentMetadata(logger, dbConnector, contentID, attachmentID, authToken, config, callback) {
    doGetAttachmentListExtWithOAuth(logger, dbConnector, contentID, authToken, [".*"], config, function(obj) {
        var i, foundIt = null, list;
        if (obj.error) {
            callback && callback(obj);
        } else if (obj.data[".*"]){
            list = obj.data[".*"];
            for (i=0; i<list.length; i++) {
              if (list[i].id === attachmentID) {
                  foundIt = list[i];
              }
            }

            if (foundIt) {
                callback && callback(foundIt);
            } else {
                callback && callback({error: "file not found " + attachmentID, code: 404});
            }
        }
    });
}

function doGetAttachmentListExtWithOAuth(logger, dbConnector, contentID, authToken, extname, config, callback)
{
    logger = logger.getLogger({
        action: "doGetAttachmentListExtWithOAuth",
        module: "confluence"
    });
    try {
        // JWT signature will be checked right after
        let path = '/rest/api/content/' + contentID + '/child/attachment';
        const LIMIT = 150;
        let attachments = {}

        const claims = jwt.decode(authToken, '', true);
        dbConnector.getConnectorData(connector_id, claims.iss, function(obj) {
            if (obj.error || !obj.sharedSecret) {
                let ret = obj.error ? obj : {error: "The instance seems to non be authenticated. Please try to reinstall the plugin"};
                callback && callback(ret);
            } else {
                try {
                    jwt.decode(authToken, obj.sharedSecret, false);
                } catch (error) {
                    logger.error("JWT signature check failed: " + error, error);
                    callback({error: "JWT signature check failed"});
                    return;
                }

                if (extname && extname.length > 0) {
                    for (let i = 0; i < extname.length; i++) {
                        attachments[extname[i]] = [];
                    }
                } else {
                    extname = [".*"];
                    attachments[".*"] = [];
                }

                let pagination = function(attachments, start, limit, totalLength, callback) {
                    getAccessTokenFromJWT(logger, dbConnector, authToken, "READ ACT_AS_USER", function (obj) {
                        let accessObject, queryString;
                        if (obj.error) {
                            callback && callback(obj)
                        } else {
                            accessObject = obj;
                            queryString = "?expand=version&start=" + start + "&limit=" + LIMIT;
                            let url = accessObject.instanceObj.baseUrl + path + queryString; // {expand: "version", start: start, limit: LIMIT}

                            httpreq.get(url, {
                                headers: {
                                    'Authorization': 'Bearer ' + accessObject.accessToken
                                }
                            }, function (err, res) {
                                _checkDeprecationHeaders(res, logger, url, config.appName);
                                if (err) {
                                    callback && callback({error: "Failed to list the resources: " + err});
                                } else {
                                    if (res.statusCode === 200) {
                                        let resp = JSON.parse(res.body),
                                            i, l, attachment;

                                        for (i = 0; i < resp.size; i++) {
                                            attachment = resp.results[i];
                                            for (l = 0; l < extname.length; l++) {
                                                if (extname[l] === ".*" || pathUtil.extname(attachment.title) === extname[l]) {
                                                    let name = pathUtil.basename(attachment.title, pathUtil.extname(attachment.title));
                                                    attachments[extname[l]].push({id: attachment.id, filename: name});
                                                    totalLength++
                                                }
                                            }
                                        }

                                        if (resp.size < limit) {
                                            callback && callback({
                                                id: contentID,
                                                data: attachments,
                                                totalLength: totalLength
                                            });
                                        } else {
                                            pagination(attachments, start + limit, limit, totalLength, callback);
                                        }
                                    } else if (res.statusCode === 404) {
                                        callback && callback({error: "Failed to list the resources: unavailable or unauthorized (404)"});
                                    } else {
                                        callback && callback({error: "Failed to list the resources: " + res.statusCode});
                                    }
                                }
                            });
                        }
                    });
                }
                pagination(attachments, 0, LIMIT, 0, callback);
            }
        });
    } catch (e)
    {
        callback && callback({error:{message: "Failed to list the resources: " + e.message}});
    }
}

function convertIdsToTypes(url, accessToken, contentIds, cb) {
    httpreq.post(url, {
        headers: {
            'Authorization': 'Bearer ' + accessToken,
            'Accept': 'application/json',
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            contentIds
        })
    }, function (err, res) {
        if (err) {
            cb && cb({error: "Failed to get content ids: " + err});
        } else {
            cb && cb(res);
        }
    });
}

function getPathBasedOnId(conversion, id, endpoint) {
    let type;
    try {
        type = conversion.results[id]
        if (!(typeof type === 'string' || type instanceof String)) {
            type = undefined;
        }
    } catch (e) {

    }
    if (type?.includes('page')) {
        return `/api/v2/pages/${id}/${endpoint}`
    }
    if (type?.includes('blog')) {
        return `/api/v2/blogposts/${id}/${endpoint}`
    }
    return '';
}
function doGetAttachmentListWithOAuth(logger, dbConnector, contentID, authToken, extname, config, callback)
{
    getAccessTokenFromJWT(logger, dbConnector, authToken, "READ ACT_AS_USER", function (obj) {
        if (obj.error) {
            callback && callback(obj)
        } else {
            let accessObject = obj;
            callSaneFunctionFromLegacy(confluenceAdapter.convertIdsToTypes(accessObject.instanceObj, [contentID], accessObject.accessToken), (obj) => {
                if (obj.error) {
                    callback(obj);
                } else {
                    let path = getPathBasedOnId(obj, contentID, 'attachments');
                    let attachments = [];
                    logger = logger.getLogger({
                        action: "doGetAttachmentListWithOAuth",
                        module: "confluence"
                    });

                    let pagination = function(attachments, next, limit, callback) {
                        let url = next ? accessObject.instanceObj.baseUrl + next.replace("wiki/", "") : accessObject.instanceObj.baseUrl + path + "?limit=" + limit;

                        httpreq.get(url, {
                            headers: {
                                'Authorization': 'Bearer ' + accessObject.accessToken,
                                'Accept': 'application/json'
                            }
                        }, function (err, res) {
                            _checkDeprecationHeaders(res, logger, url, config.appName);
                            if (err) {
                                callback && callback({error: "Failed to list the attachments: " + err});
                            } else {
                                if (res.statusCode === 200) {
                                    let resp, attachment;
                                    try {
                                        resp = JSON.parse(res.body);
                                    } catch (error) {
                                        logger.error(`Failed to parse response body: ${res.body}: ${error.message}`);
                                        // Handle the error or return from the function
                                        callback && callback({error: `Failed to parse response body: ${res.body}: ${error.message}`});
                                        return;
                                    }
                                    let listAttachments = resp?.results instanceof Array ? resp.results : null;
                                    if (listAttachments) {
                                        for (let i = 0; i < listAttachments?.length; i++) {
                                            attachment = listAttachments[i];
                                            if (!extname || pathUtil.extname(attachment.title) === extname) {
                                                attachments.push(attachment);
                                            }
                                        }

                                        if (listAttachments.length<limit) {
                                            callback && callback({attachment: attachments});
                                        } else {
                                            if (resp?._links?.next)
                                            {
                                                pagination(attachments, resp?._links?.next, limit, callback);
                                            } else {
                                                callback && callback({error: "Failed to list the attachments: next links not present"});
                                            }
                                        }
                                    } else {
                                        callback && callback({error: "Failed to list the attachments: resp results is not an array or is not present"});
                                    }
                                } else if (res.statusCode === 401) {
                                    callback && callback({error: "Failed to list the attachments: Unauthorized (401)"});
                                } else {
                                    callback && callback({error: "Failed to list the attachments: " + res.statusCode});
                                }
                            }
                        });
                    }

                    pagination(attachments, '', 150, callback);
                }
            });
        }
    });
}

function doGetAttachmentDownloadLinkExt(authObj, attachmentID, queryObject, logger, config, callback)
{
    callSaneFunctionFromLegacy((async () => {
        const result = await confluenceAdapter.getAttachment(
            authObj.instanceObj,
            attachmentID,
            queryObject,
            authObj.accessToken,
        );
        const path = result.downloadLink.split("?")[0];
        const downloadURL = authObj.instanceObj.baseUrl + path;
        return { url: downloadURL };
    })(), callback)
}

ConfluenceConnector.prototype.setPlatformArchiveName = function (platformArchiveID, authToken, platformArchiveName, callback)
{
    //do nothing
    callback({skipPlatformInfoUpdate: true});
};

/**
 * @param {JSDocTypes.Logger} logger
 * @param {JSDocTypes.SessionData} sessionData
 * @param {string} platformArchiveID
 * @param {string} platformSiteID
 * @param {Record<string, unknown>} platformInfo
 * @param {string} platformToken
 * @returns {Promise<{success: true}>}
 */
ConfluenceConnector.prototype.asyncAuthorizedForArchive = async function(logger, sessionData, platformArchiveID, platformSiteID, platformInfo, platformToken) {
    return await callWithLegacyCallback((cb) =>
        this.authorizedForArchive(
            logger,
            sessionData,
            platformArchiveID,
            platformSiteID,
            platformInfo,
            platformToken,
            cb
        )
    );
};

/**
 * 
 * @param {JSDocTypes.Logger} logger 
 * @param {JSDocTypes.SessionData} sessionData 
 * @param {string} platformArchiveID 
 * @param {string} platformSiteID 
 * @param {Record<string, unknown>} platformInfo 
 * @param {string} platformToken 
 * @param {JSDocTypes.BASLegacyCallback<{success: true}>} callback 
 */
ConfluenceConnector.prototype.authorizedForArchive = function(logger, sessionData, platformArchiveID, platformSiteID, platformInfo, platformToken, callback) {
    var requesterClaims, archivedClaims, ret;
    logger = logger.getLogger({action: "authorizedForArchive", module: "confluence", platformArchiveID: platformArchiveID, platformSiteID: platformSiteID});
    var dbConnector = sessionData.dbConnector;

    dbConnector.getConnectorData("confluence", platformInfo.iss, function (obj) {
        var sharedSecret;

        if (obj.error) {
            callback && callback(obj);
        } else {
            sharedSecret = obj.sharedSecret;
            try {
                requesterClaims = jwt.decode(platformToken, sharedSecret, false);
            } catch (e) {
                logger.info("permalink is embedded in another platform");
                requesterClaims = {};
            }

            try {
                // shared secret could have changed in the meantime
                archivedClaims = jwt.decode(platformInfo.jwt, '', true);
            } catch (e) {
                logger.error("token stored in platform info is not valid " + platformInfo.jwt);
                archivedClaims = {};
            }

            if (requesterClaims && requesterClaims.iss && archivedClaims && archivedClaims.iss
                && requesterClaims.iss === archivedClaims.iss) {
                ret = {success: true};
            } else {
                ret = {error: "permalink is not fully authorized for this platform"};
            }

            callback && callback(ret);
        }
    });
};

ConfluenceConnector.prototype.makePermalinkID = function () {
    return shortUUID.generate();
};

/**
 * 
 * @param {string} baseDir 
 * @param {string} permalinkID 
 * @param {string} format 
 * @param {JSDocTypes.DataResidencyName} dataResidency 
 * @returns {string}
 */
ConfluenceConnector.prototype.getSnapshotUrl = function (baseDir, permalinkID, format, dataResidency) {
    //do nothing
    return '';
};

/**
 * 
 * @param {PermalinkData} permalinkData 
 * @returns {PermalinkExtra}
 */
ConfluenceConnector.prototype.getPermalinkExtraFromPermalinkData = function (permalinkData) {
    const {permalinkInfo, permalinkID, permalinkKind} = permalinkData;
    const kindForPath = "confluence";
    const format = permalinkInfo?.image?.format ? permalinkInfo.image.format : "png";
    const image = bmprUtilsMod.composeImagePermalinkUrl(this.config.shareUrls[this.config.defaultDataResidencyName], permalinkID, kindForPath, format); // we do not specify the kind so we will use the default "/p/" path (i.e. no proxy to S3)
    return {
        image: (permalinkKind === Consts.PermalinkKind.image || permalinkKind === Consts.PermalinkKind.image_unfurling) ? image : undefined,
        edit: permalinkInfo.edit ? permalinkInfo.edit : undefined,
        comment: permalinkInfo.comment ? permalinkInfo.comment : undefined,
        view: permalinkInfo.view ? permalinkInfo.view : undefined,
    };
};

ConfluenceConnector.prototype.storePermalinkImage = async function ({dataStream, mimeType, permalinkID, permalinkInfo}) {
    const format = permalinkInfo?.image?.format ? permalinkInfo.image.format : "png";
    const dataResidency = permalinkInfo?.dataResidency ?? this.config.defaultDataResidencyName;
    return await this.permalinkImageStorageAdapter.uploadPermalinkImage({
        dataStream,
        mimeType,
        permalinkID,
        platformKind: 'confluence',
        format,
        dataResidency,
    });
};

/**
 * 
 * @param {object} param0 
 * @param {JSDocTypes.Logger} param0.logger
 * @param {JSDocTypes.PermalinkData} param0.permalinkData
 * @param {object} [param0.attachment]
 * @param {import('stream').Readable} param0.attachment.file
 * @param {string} param0.attachment.mimeType
 * @param {string} [param0.suffix]
 * @returns {Promise<unknown>}
 */
ConfluenceConnector.prototype.generatePermalinkImage = async function ({logger, permalinkData, attachment, suffix}) {
    const {platformKind, platformSiteID, platformArchiveID, resourceID, branchID, platformInfo, permalinkInfo} = permalinkData;
    let ret = {};
    try {
        if (this.w2iAdapter) {
            await this.w2iAdapter.invoke({
                baseUrl: this.baseUrl,
                branchID: branchID,
                resourceID: resourceID,
                platformArchiveID: platformArchiveID,
                platformSiteID: platformSiteID,
                platformArchiveName: platformArchiveID,
                // using jwt stored in platformInfo
                platformToken: platformInfo.jwt,
                platformInfo: platformInfo,
                platformKind: platformKind,
                permalinkInfo,
                permalinkID: [permalinkData.permalinkID, suffix].join(''),
                authorizationHeaderString: await this.serverUtils.getBASAuthorizationHeaderString(logger),
                thumbnailSize: 392,
                dataResidency: this.config.defaultDataResidencyName,
            });
        }
        else {
            // attachment format is "png" by design
            if (attachment && attachment.file && attachment.mimeType) {
                ret = await this.storePermalinkImage({
                    dataStream: attachment.file,
                    mimeType: attachment.mimeType,
                    permalinkID: permalinkData.permalinkID,
                });
            }
        }
    }
    catch (error) {
        // error action handled by the caller
        throw error;
    }

    return ret;
};

ConfluenceConnector.prototype.generateSnapshot = async function (/*{logger, platformKind, platformSiteID, platformArchiveID, resourceID, branchID, platformInfo}*/) {
    //TODO: not implemented
    throw new Error("Not implemented");
};

ConfluenceConnector.prototype.deletePermalinkImages = async function ({permalinkIDs, format = "png", dataResidency = this.config.defaultDataResidencyName}) {
    return await this.permalinkImageStorageAdapter.deletePermalinkImages({
        permalinkIDs,
        platformKind: 'confluence',
        format,
        dataResidency,
    });
};

ConfluenceConnector.prototype.deletePermalinkThumbnailImages = async function ({permalinkIDs, format = "png", dataResidency = this.config.defaultDataResidencyName}) {
    return await this.permalinkImageStorageAdapter.deletePermalinkThumbnailImages({
        permalinkIDs,
        platformKind: 'confluence',
        format,
        dataResidency,
    });
};

ConfluenceConnector.prototype.getPermalink = function ({sessionData, dbConnector, permalinkData, releaseSessionIfNotReleased, req, res}, cb) {
    callSaneFunctionFromLegacy(this.asyncGetPermalink({sessionData, dbConnector, permalinkData, releaseSessionIfNotReleased, req, res}), cb);
};

ConfluenceConnector.prototype.getPermalinkFromPlatform = function ({sessionData, permalinkData, req: p_req, res: p_res}, callback)
{
    let logger = p_req.bas.logger.getLogger({action: "permalink-confluence", module: "confluence"});
    var dbConnector = sessionData.dbConnector;
    var platformInfo = permalinkData.platformInfo;
    let config = this.config;

    logger.info("permalink-confluence: " + permalinkData.permalinkID);

    //first version, no memory required but a very ugly link containing the jwt token and other stuff
    if (platformInfo  && platformInfo.contentID && platformInfo.jwt) {
        // get attachmentId from resource and branch id
        doGetAttachmentListWithOAuth(logger, dbConnector, platformInfo.contentID, platformInfo.jwt, ".png", config, function(obj) {
            var i, attachment = null, uniqueFileName;
            if (obj.error) {
                callback({error: "permalink: doGetAttachmentList failed " + obj.error});
            } else {
                for (i = 0; i<obj.attachment.length; i++) {
                    attachment = obj.attachment[i];

                    uniqueFileName = "balsamiq_" + permalinkData.resourceID + "_" + permalinkData.branchID + ".png";
                    if (attachment.title === uniqueFileName) {
                        p_req.query.attachmentID = attachment.id;
                        p_req.query.platformToken = platformInfo.jwt;
                        logger.info("permalink: attachmentID " + p_req.query.attachmentID);
                        doPipe(logger, dbConnector, p_req, p_res, sessionData, false, config, callback);
                        return;
                    }
                }

                // no PNG found
                callback({
                    error: "permalink: no attachment found for resource ID " + permalinkData.resourceID + " branch ID " + permalinkData.branchID,
                    code: 404
                });
            }
        });
    } else {
        callback({error: "permalink: missing parameter"});
    }
};

ConfluenceConnector.prototype.asyncGetPermalinkThumbnail = async function (permalinkData, logger) {
    const {permalinkID, platformKind, permalinkInfo} = permalinkData;
    const format = permalinkInfo?.image?.format ? permalinkInfo.image.format : "png";
    const dataResidency = permalinkInfo?.dataResidency ?? this.config.defaultDataResidencyName;
    try {
        return await this.permalinkImageStorageAdapter.getPermalinkImageBase64({permalinkID, platformKind, format, dataResidency});
    } catch (err) {
        // permalink image could be not ready yet
        await this.generatePermalinkImage({logger, permalinkData});
        return await this.permalinkImageStorageAdapter.getPermalinkImageBase64({permalinkID, platformKind, format, dataResidency});
    }
}

ConfluenceConnector.prototype.asyncGetPermalink = async function ({sessionData, dbConnector, permalinkData, releaseSessionIfNotReleased, req, res}) {
    const {permalinkID, platformKind, permalinkInfo} = permalinkData;
    const format = permalinkInfo?.image?.format ? permalinkInfo.image.format : "png";
    const dataResidency = permalinkInfo?.dataResidency ?? this.config.defaultDataResidencyName;
    const _pipeStream = async () => {
        const headers = await this.permalinkImageStorageAdapter.getPermalinkHeadersWithMetadata({platformKind, permalinkID, format, dataResidency});
        const stream = await this.permalinkImageStorageAdapter.getPermalinkImageStream({platformKind, permalinkID, format, dataResidency});
        await releaseSessionIfNotReleased();
        return await pipeStreamToResponse(stream, res, headers);
    }

    try {
        return await _pipeStream();
    } catch (err) {
        try {
            // Old permalinks images might be stored as attachments.
            await callWithLegacyCallback(cb => this.getPermalinkFromPlatform({
                sessionData,
                dbConnector,
                permalinkData,
                releaseSessionIfNotReleased,
                req,
                res
            }, cb));
        }
        catch (error) {
            try {
                await this.generatePermalinkImage({logger: req.bas.logger, permalinkData});
                return await _pipeStream();
            } catch (error) {
                // error action handled by the caller
                throw error;
            }
        }
    }
    return {};
};

/**
 * @param {JSDocTypes.BASRequest} req
 * @param {JSDocTypes.Response} res
 * @returns {void}
 */
ConfluenceConnector.prototype.endPoint = function (req, res) {
    // serve iframe endpoints and validate the JWT
    const endpoint = req.params.endpoint;
    const logger = req.bas.logger.getLogger({action: 'confluence-endpoint', module: "confluence", endpoint: endpoint});

    const repo = this.config.proxyConfig.find(cfg => cfg.prefix === '/bw-atlassian/');
    if (!repo) {
        logger.error("Proxy config not found for prefix /bw-atlassian/");
        res.status(500).json({error: "Proxy not found"});
        return;
    }

    /** @type URL */
    let url;
    try {
        const path = pathUtil.join(repo.path, "confluence/", ************************(endpoint))
        url = new URL(path, repo.host);
    } catch (error) {
        logger.error("Error constructing URL", error, {repo, endpoint});
        res.status(400).json({error: 'Invalid URL' + error});
        return;
    }

    verifyAtlassianJWT(logger, req, this.sessionManager, function (obj) {
        if (isBASLegacyErrorObject(obj)) {
            logger.error(obj.error, null, {action: "JWT validation for Atlassian Confluence endpoint"});
            res.status(401).json({code: 401, error: "Invalid or missing JWT token"});
            return;
        }
        function handleError(/** @type {Error} */ e) {
            logger.error("Unexpected error serving the request: " + e.message, e);
            res.status(500).json({code: 500, error: "Unable to serve the request"});
        }
        try {
            superagent
                .get(url.href)
                .on('response', (incomingResponse) => res.set(incomingResponse.headers))
                .pipe(res)
                .on('error', function(e) { handleError(e) });
        } catch (e) {
            handleError(e);
        }
    });
};

ConfluenceConnector.prototype.directAPICall = function (method, p_req, p_res) {
    var instanceInfoObj = p_req.body;
    var contentID, contentTitle;
    var jwtToken;
    var attachmentID;
    var claims;
    var iss;
    // var platformSiteID
    var partOfPlatformSiteID;
    var platformArchiveID;
    var downloadLink, branchID, resourceID, expectedDownloadLink, permalinkID, platformKind;
    //var platformArchiveName;
    let logger = p_req.bas.logger.getLogger({action: "confluence/" + p_req.params.api, module: "confluence", method: method});
    // in case of error this will be set to true and we will show the fallback placeholder, e.g. the smiley
    var pipeFallbackPlaceHolder = false;
    let config = this.config;

    logger.info("direct api call " + p_req.params.api);

    this.sessionManager.createSession(logger, p_req.params.api, function(obj) {
        var sessionData, dbConnector, imgData;
        let finalizeError = function (errorMessage, errorForLogs, errorCode) {
            let errorMessageForLog = errorMessage + ": " + errorForLogs;
            logger.error(errorMessageForLog);
            let sessionManager = sessionData.sessionManager;
            sessionManager && sessionManager.releaseSession(sessionData);

            p_res.status(errorCode || 404);
            p_res.setHeader('Content-Type', 'text/json');
            p_res.setHeader('Content-Length', Buffer.byteLength(errorMessage));
            p_res.write(errorMessage);
            p_res.end();
        }

        if (obj.error) {
            var msg = {error: obj.error};
            returnJson(JSON.stringify(msg), p_res, obj);
        } else {
            sessionData = obj;
            dbConnector = obj.dbConnector;
            // if (method === "GET" && p_req.params.api === "jwtDecode") {
            //     jwtToken = p_req.query.platformToken;
            //     claims = jwt.decode(jwtToken, '', true);
            //     logger.info("decoding jwt: ", claims);
            //     returnJson(JSON.stringify(claims), p_res, sessionData);
            // } else if (method === "GET" && p_req.params.api === "getAccessTokenFromJWT") {
            //     jwtToken = p_req.query.platformToken;
            //     getAccessTokenFromJWT(logger, dbConnector, jwtToken, "READ", function(obj) {
            //         logger.info("getting access token from jwt: ", obj);
            //         returnJson(JSON.stringify(obj), p_res, sessionData);
            //     }.bind(this));
            // } else
            if (method === "GET" && p_req.params.api === "placeholder") {
                if (p_req.query.permalinkID) {
                    permalinkID = p_req.query.permalinkID;
                    dbConnector.getPermalinkData(permalinkID, function (obj) {
                        if (obj.error) {
                            logger.error("error retrieving permalink for placeholder: using the smiley one as fallback " + obj.error, null, {permalinkID});
                            p_req.query.downloadLink = null;
                            // show the smiley placeholder
                            doPipe(logger, dbConnector, p_req, p_res, sessionData, true, config);
                        } else {
                            platformKind = obj.platformKind || "confluence";

                            if (platformKind === "confluence") {
                                this.getPermalink({
                                    sessionData: sessionData,
                                    permalinkData: obj,
                                    req: p_req,
                                    res: p_res
                                }, function (obj) {
                                    if (obj.error) {
                                        logger.error("failed to load the permalink: using the smiley one", null, obj);
                                        p_req.query.downloadLink = null;
                                        // show the smiley placeholder
                                        doPipe(logger, dbConnector, p_req, p_res, sessionData, true, config);
                                    }
                                }.bind(this));
                            } else {
                                sessionData.sessionManager.releaseSession(sessionData, function(sessionObj) {
                                    if (sessionObj.error) {
                                        // we log error but we try to complete the call
                                        logger.error("sessionManager.releaseSession failed: " + sessionObj.error);
                                    }
                                    // redirect to the external platform connector
                                    p_res.redirect('/p/' + permalinkID);
                                });
                            }
                        }
                    }.bind(this));
                    // ConfluenceConnector.permalink already calling doPipe
                    return;
                } else if (p_req.query.view === "grid") {
                    logger.info("placeholder for grid view: using the smiley one");
                    p_req.query.downloadLink = null;
                    // show the smiley placeholder
                    pipeFallbackPlaceHolder = true;
                } else if (p_req.query.downloadLink && p_req.query.jwt) {
                    // we always try to deduce the download link in order to cover the clone page workflow
                    // if (p_req.query.downloadLink === "draft") {
                    // deduce the image link from referrer url and resourceID
                    // p_req.headers. https://balsamiq-staging.atlassian.net/wiki/spaces/TEST/pages/create?fromPageId=54853633
                    // downloadLink = '/download/attachments/' + contentID + '/balsamiq_' + p_req.query.initialResourceID + '_' + p_req.query.branchID + '.png';
                    // p_req.headers.referer: https://balsamiq-staging.atlassian.net/wiki/spaces/TEST/pages/create?fromPageId=54853633
                    // p_req.headers.referer: https://balsamiq-staging.atlassian.net/wiki/spaces/TEST/pages/create?draftId=55312385&draftShareId=&useDraft=true
                    // p_req.headers.referer: https://balsamiq-staging.atlassian.net/wiki/spaces/TEST/pages/edit/55246874?draftId=55246887&draftShareId=3cda641b-a841-4c0e-bab2-358e3c158b9f&
                    logger.info("placeholder: deduce the image link from referrer and resourceID " + p_req.headers.referer);
                    resourceID = p_req.query.initialResourceID;
                    branchID = p_req.query.initialBranchID || "Master";
                    platformArchiveID = p_req.query.platformArchiveID;
                    jwtToken = p_req.query.jwt;

                    if (!platformArchiveID || !jwtToken) {
                        logger.info("placeholder: missing platformArchiveID and jwt parameters using the smiley one");
                        p_req.query.downloadLink = null;
                        // show the smiley placeholder
                        pipeFallbackPlaceHolder = true;
                    } else {
                        // JWT signature will be verified in the doPipe
                        claims = jwt.decode(jwtToken, '', true);
                        iss = claims.iss;
                        partOfPlatformSiteID = iss + "_";

                        dbConnector.getPlatformInfoFromPlatformArchiveID(partOfPlatformSiteID, platformArchiveID, function(obj) {
                            var platformInfo;
                            if (obj.error) {
                                logger.info("placeholder: could not find the platform info, using the smiley one");
                                p_req.query.downloadLink = null;
                                // show the smiley placeholder
                                pipeFallbackPlaceHolder = true;
                            } else {
                                if (obj.PLATFORM_INFO) {
                                    platformInfo = JSON.parse(obj.PLATFORM_INFO);
                                    contentID = platformInfo.contentID;
                                }

                                if (contentID) {
                                    expectedDownloadLink = '/download/attachments/' + contentID + '/balsamiq_' + resourceID + '_' + branchID + '.png';
                                } else {
                                    logger.info("placeholder: not able to infer contentID from platform info, using the smiley one");
                                    expectedDownloadLink = null;
                                    p_req.query.downloadLink = null;
                                    // show the smiley placeholder
                                    pipeFallbackPlaceHolder = true;
                                }

                                if (expectedDownloadLink !== null && expectedDownloadLink !== p_req.query.downloadLink) {
                                    logger.info("placeholder: page has being copied, force the deduced downloadLink");
                                    p_req.query.downloadLink = expectedDownloadLink;
                                }
                            }
                            doPipe(logger, dbConnector, p_req, p_res, sessionData, pipeFallbackPlaceHolder, config);
                        });
                    }
                    // }
                } else {
                    // grid
                    logger.info("placeholder: using the smiley one");
                    p_req.query.downloadLink = null;
                    // show the smiley placeholder
                    pipeFallbackPlaceHolder = true;                }

                if (!partOfPlatformSiteID) {
                    // if we are not inferring contentID from platformInfo
                    doPipe(logger, dbConnector, p_req, p_res, sessionData, pipeFallbackPlaceHolder, config);
                }
            } else if (method === "GET" && p_req.params.api === "render") {
                let platformToken = p_req.headers.authorization ? p_req.headers.authorization.split(" ")[1] : "";
                if (platformToken) {
                    callSaneFunctionFromLegacy(decodeJWT({connectorId: "confluence", dbConnector, platformToken}), function (obj) {
                        if (obj.error) {
                            // set the code to unauthorized
                            p_res.status(401);
                            returnJson(JSON.stringify({error: "Not authorized"}), p_res, sessionData);
                        } else {
                            // let fallbackPlaceHolderUrl = "https://bas20.balsamiq.com/bw-atlassian/confluence/img/mockups_ico_128.png";
                            imgData = {
                                width: p_req.query.width,
                                alignment: p_req.query.alignment
                            };
                            if (p_req.query.permalinkID) {
                                permalinkID = p_req.query.permalinkID;
                                imgData.src = config.shareUrls[config.defaultDataResidencyName] + "/p/" + permalinkID;
                                logger.info("rendering ", imgData);
                                returnRenderConfluenceImage(imgData, p_res, sessionData);
                            } else if (p_req.query.downloadLink && p_req.query.downloadLink != "draft") {
                                imgData.src = p_req.query.xdm_e + p_req.query.cp + p_req.query.downloadLink;
                                logger.info("rendering ", imgData);
                                returnRenderConfluenceImage(imgData, p_res, sessionData);
                            } else {
                                contentID = p_req.query.contentid || p_req.query.pageid;
                                branchID = p_req.query.initialBranchID || "Master";
                                downloadLink = '/download/attachments/' + contentID + '/balsamiq_' + p_req.query.initialResourceID + '_' + branchID + '.png';
                                imgData.src = p_req.query.xdm_e + p_req.query.cp + downloadLink;
                                logger.info("rendering ", imgData);
                                returnRenderConfluenceImage(imgData, p_res, sessionData);
                            }
                        }
                    });
                } else {
                    // set the code to unauthorized
                    p_res.status(401);
                    returnJson(JSON.stringify({error: "Not authorized"}), p_res, sessionData);
                }
            } else if (method === "GET" && p_req.params.api === "fetch") {
                doPipe(logger, dbConnector, p_req, p_res, sessionData, false, config);
            } else if (method === "POST" && p_req.params.api === "installed") {
                let finalizeInstallation = function (instanceInfoObj) {
                    var saveAndReturn = function (logger) {
                        dbConnector.saveConnectorData(connector_id, instanceInfoObj.clientKey, instanceInfoObj, function (obj) {
                            if (obj.error) {
                                returnJson(JSON.stringify(obj), p_res, sessionData);
                            } else {
                                dbConnector.getConnectorData(connector_id, instanceInfoObj.clientKey, function (obj) {
                                    if (obj.error || !obj.clientKey) {
                                        var ret;
                                        ret = obj.error ? obj : {error: "Installation failed. Unable to save the instance data"};
                                        logger.error("subscription failed " + ret.error);
                                        returnJson(JSON.stringify(ret), p_res, sessionData);
                                    } else {
                                        // [GDPR] not necessary to share with third party processor
                                        // logger.info("successfully installed subscription for clientKey " + obj.clientKey);
                                        returnJson(JSON.stringify(obj), p_res, sessionData);
                                    }
                                });
                            }
                        });
                    };

                    // check if the clientKey is already installed
                    dbConnector.getConnectorData(connector_id, instanceInfoObj.clientKey, function (obj) {
                        if (obj.error) {
                            returnJson(JSON.stringify(obj), p_res, sessionData);
                        } else {
                            // if we are in the new lifecycle, the check on the token has already passed
                            // if we are in the old lifecycle we test only for the upgrade i.e. the app is already installed and it is signed with a jwt token
                            if (obj.clientKey && newInstallationCycle === false) {
                                logger.info("old lifecycle, already installed  " + obj.clientKey);
                                jwtToken = p_req.query.jwt || (p_req.headers.authorization && p_req.headers.authorization.split(" ")[1]);
                                try {
                                    // verify the signature
                                    jwt.decode(jwtToken, obj.sharedSecret, false);
                                    // request has been signed correctly, we save the new shared secret
                                    saveAndReturn(logger);
                                } catch (e) {
                                    // set the code to unauthorized
                                    p_res.status(401);
                                    returnJson(JSON.stringify({error: "Signature failed:" + e.message}), p_res, sessionData);
                                }
                            } else {
                                saveAndReturn(logger);
                            }
                        }
                    });
                }

                let jwtToken, newInstallationCycle;
                try {
                    // Get Authorization header from request: `Authorization: JWT ${jwt_token}`
                    jwtToken = p_req.query.jwt || p_req.headers.authorization.split(" ")[1];
                    newInstallationCycle = isAtlassianNewLifeCycleActive(jwtToken);
                } catch (e) {
                    // no jwtToken associated
                    newInstallationCycle = false;
                }

                logger.updateParams({newInstallationCycle});
                logger.info("installing request " + JSON.stringify(instanceInfoObj));

                if (newInstallationCycle) {
                    doVerifyCallFromAtlassian(p_req, instanceInfoObj, config, function (obj) {
                        if (obj.error) {
                            finalizeError("not authorized", obj.error);
                        } else {
                            finalizeInstallation(instanceInfoObj);
                        }
                    });
                } else {
                    finalizeInstallation(instanceInfoObj);
                }
            } else if (method === "POST" && p_req.params.api === "uninstalled") {
                // Get Authorization header from request: `Authorization: JWT ${jwt_token}`
                let jwtToken, newInstallationCycle;
                try {
                    // Get Authorization header from request: `Authorization: JWT ${jwt_token}`
                    jwtToken = p_req.query.jwt || p_req.headers.authorization.split(" ")[1];
                    newInstallationCycle = isAtlassianNewLifeCycleActive(jwtToken);
                } catch (e) {
                    // no jwtToken associated
                    newInstallationCycle = false;
                }

                logger.updateParams({newInstallationCycle});
                logger.info("uninstalling request " + JSON.stringify(instanceInfoObj));

                if (newInstallationCycle) {
                    doVerifyCallFromAtlassian(p_req, instanceInfoObj, config, function (obj) {
                        if (obj.error) {
                            finalizeError("not authorized", obj.error);
                        } else {
                            dbConnector.deleteConnectorData(connector_id, instanceInfoObj.clientKey, function (obj) {
                                if (obj.error) {
                                    returnJson(JSON.stringify(obj), p_res, sessionData);
                                } else {
                                    // [GDPR] not necessary to share with third party processor
                                    // logger.info("uninstalling subscription for clientKey " + instanceInfoObj.clientKey);
                                    returnJson(JSON.stringify({success: "ok"}), p_res, sessionData);
                                }
                            });
                        }
                    });
                } else {
                    dbConnector.deleteConnectorData(connector_id, instanceInfoObj.clientKey, function (obj) {
                        if (obj.error) {
                            returnJson(JSON.stringify(obj), p_res, sessionData);
                        } else {
                            // [GDPR] not necessary to share with third party processor
                            // logger.info("successfully uninstalled subscription for clientKey " + instanceInfoObj.clientKey);
                            returnJson(JSON.stringify({success: "ok"}), p_res, sessionData);
                        }
                    });
                }
            } else if (method === "POST" && p_req.params.api === "enabled") {
                // [GDPR] not necessary to share with third party processor
                // logger.info("successfully enabled for clientKey " + instanceInfoObj.clientKey);
                returnJson(JSON.stringify({}), p_res, sessionData);
            } else if (method === "POST" && p_req.params.api === "disabled") {
                // [GDPR] not necessary to share with third party processor
                // logger.info("successfully disabled for clientKey " + instanceInfoObj.clientKey);
                returnJson(JSON.stringify({}), p_res, sessionData);
            } else if (method === "GET" && p_req.params.api === "list") {
                // TODO: to remove, obsolete
                contentID = p_req.query.contentid;
                jwtToken = p_req.query.jwt;
                doGetAttachmentListExt(logger, dbConnector, contentID, jwtToken, [".bmpr"], config, function(obj) {
                    returnJson(JSON.stringify(obj), p_res, sessionData);
                });
            } else if (method === "GET" && p_req.params.api === "migrate") {
                contentID = p_req.query.contentid;
                contentTitle = p_req.query.contentTitle;
                jwtToken = p_req.query.jwt;
                doMigrationSteps(logger, dbConnector, contentID, contentTitle, jwtToken, config, function(obj) {
                    returnJson(JSON.stringify(obj), p_res, sessionData);
                });
            } else if (method === "GET" && p_req.params.api === "deleteAttachment") {
                // TODO: to remove, obsolete
                jwtToken = p_req.query.jwt || p_req.headers.authorization.split(" ")[1];
                attachmentID = p_req.query.attachmentID;
                doRemoveExt(logger, dbConnector, jwtToken, attachmentID, config, function(obj) {
                    returnJson(JSON.stringify(obj), p_res, sessionData);
                });
            } else {
                logger.error("unsupported API");
                returnJson(JSON.stringify({error: "Unsupported API"}), p_res, sessionData);
            }
        }
    }.bind(this));
};

let doVerifyCallFromAtlassian = function (p_req, instanceInfoObj, config, callback) {
    try {
        // Get Authorization header from request: `Authorization: JWT ${jwt_token}`
        let jwtToken = p_req.query.jwt || p_req.headers.authorization.split(" ")[1];
        let expectedAudience = config.baseUrl;
        let expectedIssuer = instanceInfoObj.clientKey;

        callSaneFunctionFromLegacy(confluenceAdapter.verifyJWTSignedFromAtlassian(jwtToken, expectedAudience, expectedIssuer), callback)
    } catch (e) {
        callback({error: "unexpected exception: " + e});
    }
}

function returnJson(data, res, sessionData) {
    var sessionManager = sessionData.sessionManager;
    if (sessionManager && sessionManager.releaseSession) {
        sessionManager.releaseSession(sessionData, function (/*obj*/) {
            res.setHeader('Content-Type', 'text/json');
            res.setHeader('Content-Length', Buffer.byteLength(data));
            res.write(data);
            res.end();
        });
    } else {
        res.setHeader('Content-Type', 'text/json');
        res.setHeader('Content-Length', Buffer.byteLength(data));
        res.write(data);
        res.end();
    }
}

function createJwtPayloadExt(req, instanceObj, userId, config) {
    var now = moment().utc();
    var jwtTokenValidityInMinutes = 5;
    var clientKey = instanceObj.clientKey;
    var iss = instanceObj.key || config.confluenceNamespace;

    var token =  {
        "iss": iss,
        "iat": now.unix(),
        "exp": now.add(jwtTokenValidityInMinutes, 'minutes').unix(),
        "qsh": jwt.createQueryStringHash(req),
        "aud": [ clientKey ]
    };

    if (userId) {
        token["sub"] = userId;
    }
    return token;
}

// TODO: unify Atlassian Utils
// function createJwtPayload(req, clientKey, userId) {
//     var now = moment().utc();
//     //TODO addon.config.jwt().validityInMinutes;
//     var jwtTokenValidityInMinutes = 5;
//
//     var token =  {
//         "iss": config.confluenceNamespace,
//         "iat": now.unix(),
//         "exp": now.add(jwtTokenValidityInMinutes, 'minutes').unix(),
//         "qsh": jwt.createQueryStringHash(req),
//         "aud": [ clientKey ]
//     };
//
//     if (userId) {
//         token["sub"] = userId;
//     }
//     return token;
// }

function uploadBinaryExt(p_url, headers, name, buffer, logger, config, callback)
{
    var body = "", message;
    var parsed = UrlModule.parse(p_url);
    var form = new FormData();

    form.append('minorEdit', 'true');
    form.append('comment', 'auto-generated by Balsamiq Wireframes. DO NOT REMOVE');
    form.append('file', buffer, { ContentType: 'application/octet-stream', filename: name} );
    form.submit({
        protocol: parsed.protocol,
        host: parsed.hostname,
        port: parsed.port,
        path: parsed.path,
        headers: headers
    }, function(err, res) {
        _checkDeprecationHeaders(res, logger, p_url, config.appName);
        if (err) {
            callback({error: "Failed to upload the resource: " + err});
        } else {
            if (res.statusCode == 200)
            {
                res.setEncoding('utf8');
                res.on('data', function (chunk) {
                    body += chunk;
                });
                res.on('end', function () {
                    try {
                        var obj = JSON.parse(body);
                        // confluence return obj.results
                        var id = obj.id || obj[0] && obj[0].id || obj.results && obj.results[0].id;
                        if (id) {
                            callback({id: id});
                        } else {
                            callback({error: "Bad json response"});
                        }
                    } catch (er) {
                        callback({error: "Bad json"});
                    }
                });
            } else
            {
                message = "Unable to upload the resource: " + res.statusCode + " " + res.statusMessage;
                callback({error: message, url: p_url, code: res.statusCode});
            }
        }
    });
}

function doPipe(logger, dbConnector, req, res, sessionData, pipeFallbackPlaceHolder, config, callback) {
    var platformToken = req.query.platformToken || req.query.jwt;
    var attachmentID = req.query.attachmentID;
    // var draftStatus = req.query.draftStatus;
    var queryObject = null;
    var path = req.query.path || req.query.downloadLink;
    logger = logger.getLogger({action: "pipe", module: "confluence"});
    var fallbackPlaceHolderUrl = "https://bas20.balsamiq.com/bw-atlassian/confluence/img/mockups_ico_128.png";
    var sendStatusError = function(code, message) {
        code = code || 400;
        logger.error("unexpected error: " + message);
        res.status(code).send(message);
    };

    var finaliseExt = function(obj) {
        var url = obj.url;

        sessionData.sessionManager.releaseSession(sessionData, function(sessionObj) {
            var options;
            if (sessionObj.error) {
                // we log error but we try to complete the call
                logger.error("sessionManager.releaseSession failed " + sessionObj.error);
            }

            if (obj.error) {
                sendStatusError(obj.code, obj.error);
            } else {
                options = {
                    url: url,
                    headers: {
                        "X-Atlassian-Token": "no-check",
                        "Authorization": "Bearer " + obj.accessToken
                    },
                    incomingResHandler: (incomingRes) => {
                        // remove the content-disposition in order to not force the download of the image
                        delete incomingRes.headers['content-disposition'];
                    }
                };

                // in case we show the smiley as a fallback, we do not need authentication
                if (pipeFallbackPlaceHolder) {
                    delete options.headers.Authorization;
                }

                logger.updateParams({accessToken: obj.accessToken, url: url})
                logger.info("finalize piping");
                pipeToNewRequest(req, res, options, logger, callback);
            }
        });
    };

    var doPipeFromAttachmentID = function(logger, dbConnector, platformToken, scope) {
        getAccessTokenFromJWT(logger, dbConnector, platformToken, scope, function(obj) {
            var authObject, claims, platformInfo;
            if (obj.error) {
                logger.info("getAccessTokenFromJWT failed " + obj.error);
                if (obj.migrationGDPR) {
                    // JWT signature has been already verified in the getAccessTokenFromJWT
                    claims = jwt.decode(platformToken, "", true);
                    logger.info("try to get access token using the JWT stored in the PLATFORM_INFO table " + JSON.stringify(claims));
                    dbConnector.getPlatformInfoFromPlatformArchiveID(claims.iss, attachmentID, function(obj) {
                        if (obj.error) {
                            finaliseExt({error: obj.error + ", attachmentID " + attachmentID + ", platformToken " + platformToken})
                        } else {
                            try {
                                platformInfo = JSON.parse(obj.PLATFORM_INFO);
                                doPipeFromAttachmentID(logger, dbConnector, platformInfo.jwt, "READ");
                            } catch (e) {
                                logger.error("unable to fetch attachment, attachmentID " + attachmentID + " " + obj.PLATFORM_INFO);
                                finaliseExt({error: "unable to fetch attachment "  + "attachmentID " + attachmentID + " platformToken " + platformToken});
                            }
                        }
                    });
                } else {
                    finaliseExt({error: obj.error + "attachmentID " + attachmentID + " platformToken " + platformToken})
                }
            } else {
                authObject = obj;
                logger.info("get attachment download link for " + attachmentID);
                doGetAttachmentDownloadLinkExt(authObject, attachmentID, queryObject, logger, config,function(obj) {
                    if (obj.error) {
                        logger.warn(obj.error + " " + obj.url);
                        finaliseExt({error: "Unable to get the authorised URI", code: 404});
                    } else {
                        authObject.url = obj.url;
                        logger.info("finalize fetch for " + authObject.url);
                        finaliseExt(authObject);
                    }
                });
            }
        });
    };

    if (pipeFallbackPlaceHolder === true) {
        finaliseExt({url: fallbackPlaceHolderUrl});
    } else if (attachmentID) {
        logger.updateParams({attachmentID});
        logger.info("Try to pipe by attachmentID " + attachmentID);
        doPipeFromAttachmentID(logger, dbConnector, platformToken, "READ ACT_AS_USER");
    } else if (path) {
        // path = /download/attachments/557058/user-avatar?version=1&modificationDate=1448891129902&api=v2
        // var str = path.split('?'), pathString;
        // pathString = str[0];
        // if (str.length == 2) {
            // queryObject = queryStringToJSON('?' + str[1]);
        // }

        // if (queryObject.status == "draft") {
            // on draft we need to impersonate the user in order to access the attachment
            logger.info("Try to pipe by path " + path);

            getAccessTokenFromJWT(logger, dbConnector, platformToken, "READ ACT_AS_USER", function(obj) {
                var authObject;
                if (obj.error) {
                    // FIX: security report: https://tracker.bugcrowd.com/balsamiq/submissions/44fa5adb-9fd6-4c52-b736-6fc9d8590fee
                    logger.error(obj.error + " path " + path + " platformToken " + platformToken);
                    finaliseExt({error: "unauthorized"})
                } else {
                    authObject = obj;
                    if (authObject.instanceObj && authObject.instanceObj.baseUrl) {
                        authObject.url = authObject.instanceObj.baseUrl + path;
                        finaliseExt(authObject);
                    } else {
                        // FIX: security report: https://tracker.bugcrowd.com/balsamiq/submissions/44fa5adb-9fd6-4c52-b736-6fc9d8590fee
                        logger.error("cannot access instance baseUrl for selected path " + path);
                        logger.error("doPipe: malformed authObject " + JSON.stringify(authObject) );
                        finaliseExt({error: "unauthorized"});
                    }
                }
            });
        // } else {
        //     doGetAuthURLExt(dbConnector, platformToken, 'GET', pathString, queryObject, function(obj) {
        //         if (obj.error) {
        //             finalise({error: "Unable to get the authorised URI", code: 404});
        //         } else {
        //             finalise({url: obj.url});
        //         }
        //     });
        // }
    } else {
        logger.updateParams({query: req.query});
        logger.error("Pipe error unexpected parameters");
        finaliseExt({error: "Wrong parameters", code: 500});
    }
}

function doAuthFetchExt(logger, dbConnector, authToken, platformArchiveID, queryObject, isBinary, config, callback)
{
    var archiveID = generateArchiveID();

    getAccessTokenFromJWT(logger, dbConnector, authToken, "READ ACT_AS_USER", function(authObj) {
        if (authObj.error) {
            callback && callback(authObj)
        } else {
            doGetAttachmentDownloadLinkExt(authObj, platformArchiveID, queryObject, logger, config,function(obj) {
                var url;
                if (obj.error) {
                    callback && callback(obj);
                } else {
                    url = obj.url;
                    callSaneFunctionFromLegacy((async () => {
                        const buffer = await confluenceAdapter.download(url, isBinary, authObj.accessToken);
                        return {id: archiveID, buffer};
                    })(), callback, logger);
                }
            });
        }
    });
}

function doGetAuthURLExt(dbConnector, authToken, method, path, queryObject, config, callback)
{
    var url, urlNoToken;
    var claims;
    var queryString;

    if (authToken) {
        // JWT signature will be verified right after
        claims = jwt.decode(authToken, '', true);
        queryString = jwt.canonicalizeQueryString(queryObject);
        dbConnector.getConnectorData(connector_id, claims.iss, function(instanceObj) {
            if (instanceObj.error || !instanceObj.sharedSecret)
            {
                var ret = instanceObj.error ? instanceObj : {error: "The instance seems to non be authenticated. Please try to reinstall the plugin"};
                callback && callback(ret);
            } else
            {
                var sharedSecret = instanceObj.sharedSecret;
                var userKey;

                try {
                    // verify the signature
                    jwt.decode(authToken, sharedSecret, false);


                    if (claims.context && claims.context.user && claims.context.user.userKey) {
                        userKey = claims.context.user.userKey;
                    }

                    var jwtPayload = createJwtPayloadExt({
                        'method': method,
                        'path'  : path,
                        'query' : queryObject
                    }, instanceObj, userKey, config);

                    var jwtToken = jwt.encode(jwtPayload, sharedSecret, 'HS256');

                    urlNoToken =  instanceObj.baseUrl + path + (queryString ? '?' + queryString: '');
                    // [deprecated JWT on URL parameter] OK checked all caller
                    url = instanceObj.baseUrl + path + (queryString ? "?" + queryString + "&": '?') + "jwt=" + jwtToken;

                    callback && callback({url: url, urlNoToken: urlNoToken, jwtToken: jwtToken});
                } catch (e) {
                    callback && callback({error: "Signature failed:" + e.message});
                }
            }
        });
    } else {
        callback && callback({error: "Wrong parameter: platformToken is missing" });
    }
}

// function doGetContentMetadata(dbConnector, authToken, contentID, config, logger, callback) {
    // var url = "/rest/api/content/" + contentID;
    // doGetAuthURLExt(dbConnector, authToken, 'GET', url, {expand: "metadata"}, config ,function (obj) {
    //     if (obj.error) {
    //         callback(obj);
    //     } else {
    //         var url = obj.urlNoToken;
    //         var jwtToken = obj.jwtToken;
    //         httpreq.get(url, {
    //             headers: {
    //                 'Authorization': 'JWT ' + jwtToken
    //             }
    //         }, function (err, res) {
    //             _checkDeprecationHeaders(res, logger, url, config.appName);
    //             var jsonObject;
    //             if (err) {
    //                 callback && callback({error: "Failed to get the requested content: " + err});
    //             } else {
    //                 if (res.statusCode === 200) {
    //                     try {
    //                         jsonObject = JSON.parse(res.body);
    //                     } catch (e) {
    //                         callback && callback({error: "Failed to get content: malformed response"});
    //                     }
    //                     callback && callback(jsonObject);
    //                 } else if (res.statusCode === 401) {
    //                     callback && callback({error: "Failed to get attachment descriptor: Unauthorized (401)"});
    //                 } else {
    //                     callback && callback({error: "Failed to get attachment descriptor: " + res.statusCode});
    //                 }
    //             }
    //         });
    //     }
    // });
// }

// function doGetContent(dbConnector, authToken, contentID, callback) {
//     var url = "/rest/api/content/" + contentID;
//     doGetAuthURLExt(dbConnector, authToken, 'GET', url, {expand: "body.storage,version"}, function (obj) {
//         if (obj.error) {
//             callback(obj);
//         } else {
//             var url = obj.urlNoToken;
//             var jwtToken = obj.jwtToken;
//             httpreq.get(url, {
//                 headers: {
//                     'Authorization': 'JWT ' + jwtToken
//                 }
//             }, function (err, res) {
//                 var jsonObject;
//                 if (err) {
//                     callback && callback({error: "Failed to get the requested content: " + err});
//                 } else {
//                     if (res.statusCode === 200) {
//                         try {
//                             jsonObject = JSON.parse(res.body);
//                         } catch (e) {
//                             callback && callback({error: "Failed to get content: malformed response"});
//                         }
//                         callback && callback(jsonObject);
//                     } else if (res.statusCode === 401) {
//                         callback && callback({error: "Failed to get attachment descriptor: Unauthorized (401)"});
//                     } else {
//                         callback && callback({error: "Failed to get attachment descriptor: " + res.statusCode});
//                     }
//                 }
//             });
//         }
//     });
// }

// function doSaveContent(dbConnector, authToken, contentID, jsonObject, callback)
// {
//     var path = "/rest/api/content/" + contentID;
//     doGetAuthURLExt(dbConnector, authToken, 'PUT', path, null, function(obj) {
//         if (obj.error) {
//             callback(obj);
//         } else {
//             var url = obj.urlNoToken;
//             var jwtToken = obj.jwtToken;
//             httpreq.put(url, {
//                 json: jsonObject,
//                 headers: {
//                     'Authorization': 'JWT ' + jwtToken
//                 }
//             }, function (err, res) {
//                 var resp;
//                 if (err) {
//                     callback && callback({error: "Failed to get the requested content: " + err});
//                 } else {
//                     if (res.statusCode === 200)
//                     {
//                         resp = JSON.parse(res.body);
//                         callback && callback(resp);
//                     } else if (res.statusCode === 401)
//                     {
//                         callback && callback({error: "Failed to get attachment descriptor: Unauthorized (401)"});
//                     } else {
//                         callback && callback({error: "Failed to get attachment descriptor: " + res.statusCode});
//                     }
//                 }
//             });
//         }
//     });
// }

function doUpdateAttachmentTitle(dbConnector, authToken, contentID, attachmentId, attachmentObj, title, config, logger, callback)
{
    var jsonObject = {
        id: attachmentId,
        type: "attachment",
        title: title,
        version: {
            minorEdit: true,
            number: attachmentObj.version.number
        }
    };

    // TODO: API_V2 it could be deprecated in the future
    var url = "/rest/api/content/" + contentID + "/child/attachment/" + attachmentId;
    doUpdateContent(dbConnector, authToken, url, jsonObject, config, logger, callback);
}

function doUpdateContent(dbConnector, authToken, path, jsonObject, config, logger, callback)
{
    doGetAuthURLExt(dbConnector, authToken, 'PUT', path, null, config, function(obj) {
        if (obj.error) {
            callback(obj);
        } else {
            var url = obj.urlNoToken;
            var jwtToken = obj.jwtToken;
            httpreq.put(url, {
                headers: {
                    'Authorization': 'JWT ' + jwtToken
                },
                json: jsonObject
            }, function (err, res) {
                _checkDeprecationHeaders(res, logger, url, config.appName);
                var resp;
                if (err) {
                    callback && callback({error: "Failed requested API:" + url + " " + err});
                } else {
                    if (res.statusCode === 200)
                    {
                        resp = JSON.parse(res.body);
                        callback && callback(resp);
                    } else if (res.statusCode === 401)
                    {
                        callback && callback({error: "Failed to access requested API:" + url + " Unauthorized (401)"});
                    } else {
                        callback && callback({error: "Failed to access requested API:" + url + " " + res.statusCode});
                    }
                }
            });
        }
    });
}

function generateArchiveID()
{
    return connector_id + "_" + uuid.v1().replace(/-/g, '_');
}

//function getPlatformSiteID(contentID, token) {
//    var claims = jwt.decode(token, '', true);
//    var iss = claims.iss;
//    return iss + "_" + contentID;
//}

// function doMigrationMacroSteps(logger, dbConnector, sessionManager, contentID, platformArchiveID, platformSiteID, authToken, callback) {
//     var sessionData = dbConnector.sessionData;
//     logger = logger.getLogger({action: "migrationSteps", module: "confluence"});
//     logger.info("migration steps for macro start");
//
//     //if (et) {
//     //    var text = '<p><ac:structured-macro ac:macro-id="340331da-ce05-48e7-b8fb-e20a7f5a9ab7" ac:name="mockup" ac:schema-version="1"><ac:parameter ac:name="Version">1</ac:parameter><ac:parameter ac:name="Name">White</ac:parameter><ac:parameter ac:name="" /></ac:structured-macro></p>' +
//     //        '<table>' +
//     //        '<tbody>' +
//     //        '<tr>' +
//     //        '<th>Funzioner&egrave;?</th>' +
//     //        '<th>Bho?</th></tr>' +
//     //        '<tr>' +
//     //        '<td>&nbsp;</td>' +
//     //        '<td>&nbsp;</td></tr></tbody></table>';
//     //    parseMockupsMacro(text, null, platformArchiveID, platformSiteID, function(obj) {
//     //        console.log(JSON.stringify(obj));
//     //        callback && callback(obj);
//     //    });
//     //    return;
//     //}
//
//    // TODO: do we need a WRITE lock?
//     dbConnector.getBASArchiveID(platformSiteID, platformArchiveID, function(obj) {
//         var archiveID;
//         var platformArchiveName;
//
//         if (obj.error) {
//             callback && callback(obj);
//         }
//         else {
//             archiveID = obj.BAS_ARCHIVE_ID;
//             platformArchiveName = obj.PLATFORM_ARCHIVE_NAME;
//
//             sessionManager.openBarLocked(sessionData, archiveID, "WRITE", function (obj) {
//                 if (obj.error) {
//                     callback && callback(obj);
//                 }
//                 else {
//                     var bar = obj.bar;
//                     // if branchID == null we return the TOC of all branches
//                     // TODO: why bar.getTOC is not working?
//                     bar.dump(null, function (obj) {
//                         var dump;
//                         sessionManager.unlockConnection(sessionData, function (/*objUnlock*/) {
//                             // TODO: handle objUnlock?
//                             // we handle the eventual error from dump after releasing the lock
//                             if (obj.error) {
//                                 callback && callback(obj);
//                             }
//                             else {
//                                 dump = obj.dump;
//                                 doGetContent(dbConnector, authToken, contentID, function(obj) {
//                                     var content;
//                                     if (obj.error) {
//                                         callback && callback(obj);
//                                     } else {
//                                         content = {
//                                             id: obj.id,
//                                             type: obj.type,
//                                             title: obj.title,
//                                             space: obj.space,
//                                             body: obj.body,
//                                             version: {
//                                                 number: obj.version.number
//                                             }
//                                         };
//
//                                         parseMockupsMacro(obj.body.storage.value, dump, platformArchiveID, platformArchiveName, function(obj) {
//                                            if (obj.error) {
//                                                callback && callback(obj);
//                                            } else {
//                                                if (content.body.storage.value != obj.converted) {
//                                                    content.body.storage.value = obj.converted;
//                                                    content.version.number++;
//                                                    content.version.minorEdit = true;
//                                                    doSaveContent(dbConnector, authToken, contentID, content, function() {
//                                                        logger.info("migration completed, page updated to version " + content.version.number);
//                                                        callback && callback({success: "ok"});
//                                                    })
//                                                } else {
//                                                    // nothing change
//                                                    // TODO: is this an error?
//                                                    callback && callback({warning: "No macro has been converted"});
//                                                }
//                                            }
//                                         });
//                                     }
//                                 });
//                             }
//                         });
//                     }.bind(this));
//                 }
//             }.bind(this));
//         }
//     });
// }

// TODO: unify in BAS Utils
function migrationWatchdog(contentData, callback)
{
    var i, key,
        keys = Object.keys(contentData),
        completed = 0, error = null;
    for (i=0; i<keys.length; i++)
    {
        key = keys[i];
        if (contentData[key].error) {
            error = contentData[key].error;
            break;
        } else if (contentData[key].status == "onprogress") {
            break;
        } else {
            // success
            completed++;
        }
    }

    if (completed === keys.length)
    {
        callback({status: "completed"});
    } else if (error) {
        callback({error: error});
    } else {
        // retry later
        setTimeout(function() {
            migrationWatchdog(contentData, callback);
        }, 200);
    }
}

function doMigrationSteps(logger, dbConnector, contentID, contentTitle, authToken, config, callback)
{
    // JWT signature will be verified in the doGetAttachmentList
    var claims;
    var iss
    var confluenceServerURL;
    var convertedZIPAttachmentID;

    logger = logger.getLogger({action: "migrationSteps", module: "confluence"});

    try {
        claims = jwt.decode(authToken, '', true);
        iss = claims.iss;
    } catch (error) {
        logger.info("unable to migrate: " + error);
        callback("Unable to migrate, authentication error, probably JWT token in missing");
        return;
    }

    dbConnector.getConnectorData("confluence", iss, function(instanceObj) {
        if (instanceObj.error) {
            callback(obj);
        } else {
            confluenceServerURL = instanceObj.baseUrl; // include also the context (e.g. "https://balsamiq.atlassian.net/wiki")
            // get the list of attachments
            // for each attachment
            logger.info("migrating " + contentTitle + " " + confluenceServerURL);
            doGetAttachmentListWithOAuth(logger, dbConnector, contentID, authToken, null, config, function(obj) {
                let i,
                    /**@type JSZip */
                    backupAssets,
                    include,
                    backupZip = new JSZip(),
                    convertedZip = new JSZip(),
                    /**@type JSZip */
                    convertedAssets,
                    // NOTE(Snyk): Object.create(null) creates an object without a prototype so that it cannot be polluted by the user input
                    contentData = Object.create(null),
                    imagesMetadata = Object.create(null),
                    mockupsMetadata = Object.create(null),
                    mockupsHistory = Object.create(null),
                    history;


                if (obj.error)
                {
                    logger.info("unable to get attachment list " + obj.error + ' ' + confluenceServerURL);
                    callback(obj);
                } else
                {
                    backupAssets = backupZip.folder('assets');
                    convertedAssets = convertedZip.folder('assets');
                    for (i=0; i<obj.attachment.length; i++) {
                        (function(attachment) {
                            if (pathUtil.extname(attachment.title) === ".bmml") {
                                contentData[attachment.id] = {status: "onprogress"};
                                if (mockupsHistory[attachment.title] && mockupsHistory[attachment.title].length > 0)
                                {
                                    // TODO: versioning
                                    var h, gotcha = false, attachmentDate = new Date(attachment.created),
                                        history = mockupsHistory[attachment.title];

                                    for (h = 0; h<history.length; h++)
                                    {
                                        if (attachmentDate > new Date(history[h].created))
                                        {
                                            history[h].splice(h, 0, attachment);
                                            gotcha = true;
                                            break;
                                        }
                                    }

                                    if (gotcha == false)
                                    {
                                        history.push(attachment);
                                    }
                                } else
                                {
                                mockupsHistory[attachment.title] = [attachment];
                                }

                                include = true;
                            } else if (isImage(attachment.mediaType))
                            {
                                contentData[attachment.id] = {status: "onprogress"};
                                include = true;
                            } else {
                                include = null;
                            }

                            if (include) {
                                var isBinary = isImage(attachment.mediaType);
                                doAuthFetchExt(logger, dbConnector, authToken, attachment.id, {}, isBinary, config,function(obj) {
                                    if (obj.error) {
                                        logger.warn("unable to fetch attachment " + obj.error + ' ' + attachment._links.download + ' ' + confluenceServerURL);
                                        // [WORKAROUND] we do not abort the migration in case of error retrieving an image
                                        // contentData[attachment.id] = {status: "error", error: obj.error};
                                        contentData[attachment.id] = {status: "success", metadata: attachment, warning: obj.error};
                                    } else {
                                        // logger.info("successfully fetching " + attachment._links.download + " " + confluenceServerURL);
                                        contentData[attachment.id] = {status: "success", data: obj.buffer, metadata: attachment};
                                    }
                                    if (isBinary)
                                    {
                                        // image
                                        imagesMetadata[attachment.id] = attachment;
                                    } else
                                    {
                                        // mockup
                                        mockupsMetadata[attachment.id] = attachment;
                                        contentData[attachment.id].converted = obj.buffer;
                                    }
                                });
                            }
                        })(obj.attachment[i]);
                    }

                    migrationWatchdog(contentData, function(obj) {
                        var imageKeys, mockupKeys, i, l, h, strToFind, strToReplace, image, mockup, strConverted, title, convertedTitle;
                        if (obj.error)
                        {
                            logger.error("ERROR watchdog " + obj.error + ' ' + confluenceServerURL);
                            callback(obj);
                        } else
                        {
                            imageKeys = Object.keys(imagesMetadata);
                            mockupKeys = Object.keys(mockupsMetadata);

                            // for each images
                            for (i=0; i<imageKeys.length; i++)
                            {
                                image = imagesMetadata[imageKeys[i]];
                                for (l=0; l<mockupKeys.length; l++)
                                {
                                    mockup = mockupsMetadata[mockupKeys[l]];

                                    var parsed = UrlModule.parse(image._links.download);
                                    var query = parsed.search;
                                    var filename = pathUtil.parse(parsed.pathname).base;
                                    var dir = pathUtil.parse(parsed.pathname).dir;

                                    if (contentData[mockup.id].converted) {

                                        // image._links.download = '/download/attachments/884779/smileyface-128px.png?version=1&amp;modificationDate=1435658566535&amp;api=v2'
                                        strToFind = "<src>" + escape(confluenceServerURL + image._links.download) + "</src>";
                                        strToReplace = "<src>" + "./assets/" + escape(encodeURIComponent(image.title)) + "</src>";
                                        logger.info("try " + strToFind + ' ' + confluenceServerURL);
                                        strConverted = replaceAll(contentData[mockup.id].converted, strToFind, strToReplace);

                                        if (strConverted == contentData[mockup.id].converted) {
                                            // WORKAROUND 2.2 BUG: sometime the image.filename is not encoded as expected
                                            strToFind = "<src>" + escape(confluenceServerURL + dir + '/') + filename + escape(query) + "</src>";
                                            logger.info("try " + strToFind + ' ' + confluenceServerURL);
                                            strConverted = replaceAll(contentData[mockup.id].converted, strToFind, strToReplace);
                                        }

                                        if (strConverted == contentData[mockup.id].converted) {
                                            // WORKAROUND 2.2 BUG: sometime the image.filename is not encoded as expected
                                            strToFind = "<src>" + escape(confluenceServerURL + dir + '/') + encodeURIComponent(filename) + escape(query) + "</src>";
                                            logger.info("try " + strToFind + ' ' + confluenceServerURL);
                                            strConverted = replaceAll(contentData[mockup.id].converted, strToFind, strToReplace);
                                        }

                                        if (strConverted == contentData[mockup.id].converted) {
                                            // WORKAROUND 2.2 BUG: sometime the image.filename is not encoded as expected
                                            strToFind = "<src>" + escape(confluenceServerURL + dir + '/' + encodeURIComponent(filename) + query) + "</src>";
                                            logger.info("try " + strToFind + ' ' + confluenceServerURL);
                                            strConverted = replaceAll(contentData[mockup.id].converted, strToFind, strToReplace);
                                        }

                                        if (strConverted == contentData[mockup.id].converted) {
                                            // WORKAROUND Migration from M4C: last chance
                                            //  strToFind = "<src>.*/download/attachments/" + contentID + "/" + filename + ".*</src>";

                                            strToFind = '<src>.*/download/attachments/\\d+/' + escapeRegExp(encodeURIComponent(filename)) + '.*</src>';
                                            logger.info("try " + strToFind);
                                            strConverted = replaceAllWithoutEscaping(contentData[mockup.id].converted, strToFind, strToReplace);
                                        }

                                        if (strConverted == contentData[mockup.id].converted) {
                                            // WORKAROUND Migration from M4C: last chance
                                            strToFind = '<src>.*/download/attachments/\\d+/' + escapeRegExp(filename) + '.*</src>';
                                            logger.info("try " + strToFind);
                                            strConverted = replaceAllWithoutEscaping(contentData[mockup.id].converted, strToFind, strToReplace);
                                        }

                                        if (strConverted == contentData[mockup.id].converted) {
                                            // WORKAROUND Migration from M4C: last chance
                                            strToFind = '<src>.*/download/attachments/\\d+/' + escapeRegExp(encodeURI(encodeURIComponent(filename))) + '.*</src>';

                                            logger.info("try " + strToFind);
                                            strConverted = replaceAllWithoutEscaping(contentData[mockup.id].converted, strToFind, strToReplace);
                                        }

                                        if (strConverted != contentData[mockup.id].converted) {
                                            // the image was mentioned
                                            contentData[image.id].linked = true;
                                            contentData[mockup.id].converted = strConverted;
                                            logger.info("successfully image reference " + strToFind + ' ' + strToReplace + ' ' + confluenceServerURL);
                                        } else {
//                                        logger.info("not found image reference ", strToFind, strToReplace, confluenceServerURL);
                                        }
                                    } else {
                                        logger.warn("Content of mockup is null, skipping string replacement for " + mockup.id);
                                    }
                                }
                            }

                            // for each images
                            for (i=0; i<imageKeys.length; i++)
                            {
                                image = imagesMetadata[imageKeys[i]];
                                title = pathUtil.basename(image.title, pathUtil.extname(image.title)) + ".bmml";
                                if (mockupsHistory[title])
                                {
                                    // the image is a PNG export of a mockup
                                    contentData[image.id].exportedPNG = true;
                                }
                            }

                            // TODO: versioning
                            mockupKeys = Object.keys(mockupsHistory);
                            for (l=0; l<mockupKeys.length; l++)
                            {
                                history = mockupsHistory[mockupKeys[l]];
                                for (h=0; h<history.length; h++)
                                {
                                    mockup = history[h];
                                    if (h == 0)
                                    {
                                        title = mockup.title;
                                        convertedTitle = removePrefix(title, "mockup_");
                                        convertedZip.file(convertedTitle, contentData[mockup.id].converted);
                                    } else
                                    {
                                        title = mockup.title + "." + h;
                                    }
                                    backupZip.file(title, contentData[mockup.id].converted);
                                }
                            }

                            for (i=0; i<imageKeys.length; i++)
                            {
                                image = imagesMetadata[imageKeys[i]];
                                if (contentData[image.id].linked)
                                {
                                    backupAssets.file(image.title, contentData[image.id].data);
                                    convertedAssets.file(image.title, contentData[image.id].data);
                                }
                            }

                            // [WORKAROUND] Atlassian BUG: we do not delete/rename BMMLs and PNGs
                            //buffer = backupZip.generate({type:"nodebuffer"});
                            //doSaveToPlatform(logger, dbConnector, iss, contentID, contentTitle + "-Backup.zip", buffer, function(obj) {
                            //    if (obj.error)
                            //    {
                            //        callback(obj);
                            //    } else
                            //    {
                                    var unique = uuid.v1();
                                    convertedZip.generateInternalStream({type:"nodebuffer"}).accumulate().then(function (buffer) {
                                        doSaveToPlatform(logger, dbConnector, authToken, contentID, null, contentTitle + "-" + unique + "-Converted.zip", buffer, config, function(obj) {
                                            var attachment, keys, toDelete;
                                            if (obj.error)
                                            {
                                                logger.error("ERROR unable to upload " + obj.error + ' ' + contentTitle + "-Converted.zip" + ' ' + confluenceServerURL);
                                                callback(obj);
                                            } else
                                            {
                                                convertedZIPAttachmentID = obj.id;
                                                logger.info("Saved to platform converted zip file: " + convertedZIPAttachmentID);
                                                keys = Object.keys(contentData);
                                                for (l=0; l<keys.length; l++) {

                                                    attachment = contentData[keys[l]].metadata;
                                                    toDelete = false;
                                                    if (contentData[keys[l]].exportedPNG)
                                                    {
                                                        // At the moment we do not delete the PNG so they can be still be displayed with the new/old macro
                                                    } else
                                                    {
                                                        // linked image or bmml file
                                                        // [WORKAROUND] Atlassian BUG: we do not delete/rename BMMLs and PNGs
                                                        toDelete = false;
                                                    }

                                                    if (toDelete)
                                                    {
                                                        contentData[keys[l]] = {status: "onprogress"};
                                                        (function(attachment) {
                                                            var attachmentTitle = attachment.title + '_migrated';
                                                            //doRemove(dbConnector, iss, attachment.id, function(obj) {
                                                            doUpdateAttachmentTitle(dbConnector, authToken, contentID, attachment.id, attachment, attachmentTitle, config, logger,function(obj) {
                                                                if (obj.error) {
                                                                    contentData[attachment.id] = {status: "error", error: obj.error};
                                                                    logger.error("ERROR renaming " + attachment.title + " " + attachment.id);
                                                                    obj.result && logger.error("ERROR renaming " + JSON.stringify(obj.result));
                                                                } else {
                                                                    contentData[attachment.id] = {status: "success"};
                                                                    // logger.info("successfully renaming " + attachment.title + " " + attachment.id);
                                                                }
                                                            });
                                                        })(attachment);
                                                    }
                                                }

                                                migrationWatchdog(contentData, function(obj) {
                                                    // TODO: API_V2 it could be deprecated in the future
                                                    let path = `/rest/api/content/${contentID}/child/attachment/${convertedZIPAttachmentID}/download`;
                                                    if (obj.error) {
                                                        logger.error("Migration failed: " + obj.error);
                                                        callback(obj);
                                                    } else {
                                                        logger.info("Migration completed");
                                                        callback({
                                                            url: path,
                                                            convertedZIPAttachmentID: convertedZIPAttachmentID
                                                        });
                                                    }
                                                });
                                            }
                                        });
                                    });
                            //    }
                            //});
                        }
                    })
                }
            });
        }
    });
}

function decodePlatformSiteID (platformSiteID, userInfo) {
    const regex = /(.*)_(.*)$/;
    let m;

    if ((m = regex.exec(platformSiteID)) !== null) {
        if (m.length !== 3) {
            return {error: "malformed platformSiteID"};
        } else {
            // if userInfo is provided, we check the consistency of the platformSiteID
            if (userInfo?.platformInfo?.contentID && userInfo?.platformInfo?.contentID !== m[2]) {
                return { error: "inconsistent platformSiteID" };
            }

            // if userInfo is NOT provided, we only check if the platformSiteID is well formed
            return {
               iss: m[1],
               contentID: m[2]
            }
        }
    } else {
        return {error: "malformed platformSiteID"};
    }
}

function escapeRegExp(string) {
    return string.replace(/([.*+?^=!:${}()|\[\]\/\\])/g, "\\$1");
}

function replaceAll(string, find, replace) {
    return string.replace(new RegExp(escapeRegExp(find), 'gi'), replace);
}

function replaceAllWithoutEscaping(string, find, replace) {
    return string.replace(new RegExp(find, 'gi'), replace);
}

// function parseMockupsMacro(text, dump, platformArchiveID, platformArchiveName, callback) {
//     var xmlBodyText = '<?xml version="1.0"?>' + '<xml>' + text + '</xml>';
//
//     xmlBodyText = xmlBodyText
//         .split("ac:").join("ac--")
//         .split("ri:").join("ri--")
//         .split("&nbsp;").join("nnbbsspp")
//         .split("&quot;").join("qquuoott")
//         .split("&agrave;").join("aaggrraavvee");
//     try {
//         var xmlDoc = et.parse(xmlBodyText);
//
//         // xpath queries
//         var allMacros = xmlDoc.findall('.//ac--structured-macro[@ac--name="mockup"]');
//         var parameters, macros = [];
//
//         for (i=0; i<allMacros.length; i++) {
//             parameters = allMacros[i].findall('./ac--parameter[@ac--name="Name"]');
//             if (parameters.length) {
//                 macros.push(allMacros[i]);
//             }
//         }
//
//         var element = et.Element;
//         var name;
//         var platformArchiveIDNode;
//         var initialResourceIDNode;
//         var platformArchiveNameNode;
//         var initialResourceID;
//         var i;
//
//         for (i=0; i<macros.length; i++) {
//             // change the name attribute of the macro
//             macros[i].set('ac--name', 'mockup');
//
//             name = macros[i].findtext('ac--parameter[@ac--name="Name"]');
//
//             initialResourceID = bmprUtilsMod.getResourceIDfromName(name, dump) || "";
//
//             platformArchiveIDNode = element('ac--parameter');
//             platformArchiveIDNode.text = platformArchiveID;
//             platformArchiveIDNode.set('ac--name', 'platformArchiveID');
//
//             initialResourceIDNode = element('ac--parameter');
//             initialResourceIDNode.text = initialResourceID;
//             initialResourceIDNode.set('ac--name', 'initialResourceID');
//
//             platformArchiveNameNode = element('ac--parameter');
//             platformArchiveNameNode.text = platformArchiveName;
//             platformArchiveNameNode.set('ac--name', 'platformArchiveName');
//
//             var nameNode = macros[i].find('ac--parameter[@ac--name="Name"]');
//             macros[i].remove(nameNode);
//             macros[i].append(platformArchiveIDNode);
//             macros[i].append(initialResourceIDNode);
//             macros[i].append(platformArchiveNameNode);
//         }
//
//         var newBodyText = xmlDoc.write({'xml_declaration': false});
//
//         newBodyText = newBodyText
//             .split("ac--").join("ac:")
//             .split("ri--").join("ri:")
//             .split("nnbbsspp").join("&nbsp;")
//             .split("<xml>").join("")
//             .split("</xml>").join("")
//             .split("/>").join(" />")
//             .split("qquuoott").join("&quot;")
//             .split("aaggrraavvee").join("&agrave;");
//
//         callback({converted: newBodyText});
//     } catch (e) {
//         callback({error: e.message});
//     }
//
// }

//function parseMockupsMacroRegEx(text, dump, platformArchiveID, platformArchiveName, callback) {
//    var macro = [];
//    var mockupMacro = [];
//    var data = [];
//    var i, final;
//
//    text.replace(/<ac:structured-macro([\s\S]*?)<\/ac:structured-macro>/g, function(a, b) {
//        macro.push({orig: a, sub: b + "</ac:structured-macro>"}); return a;
//    });
//
//    for (i=0; i<macro.length; i++) {
//        (function(macro) {
//            macro.sub.replace(/ac:name="mockup"([\s\S]*?)<\/ac:structured-macro>/g, function(a, b) {
//                mockupMacro.push({orig: macro.orig, sub: b}); return a;
//            });
//        })(macro[i]);
//    }
//
//    for (i=0; i<mockupMacro.length; i++) {
//        (function(macro) {
//            macro.orig.replace(/([\s\S]*?)<ac:parameter ac:name="Name">([\s\S]*?)<\/ac:parameter>([\s\S]*?)<\/ac:structured-macro>/g, function(a, b ,c, d) {
//                var newString, initialResourceID;
//                var name = c;
//
//                initialResourceID = bmprUtilsMod.getResourceIDfromName(name, dump) || "";
//
//                newString = b + '<ac:parameter ac:name="initialResourceID">' + initialResourceID + '</ac:parameter>';
//                newString += '<ac:parameter ac:name="platformArchiveID">'+ platformArchiveID + '</ac:parameter>';
//                newString += '<ac:parameter ac:name="platformArchiveName">' + platformArchiveName + '</ac:parameter>';
//                newString += d + '</ac:structured-macro>';
//
//                data.push({orig: macro.orig, new: newString, name: name});
//                return a;
//            });
//        })(mockupMacro[i]);
//    }
//
//    final = text;
//    for (i=0; i<data.length; i++) {
//        final = replaceAll(final, data[i].orig, data[i].new);
//    }
//
//    callback({converted: final});
//}

ConfluenceConnector.prototype.aboutToSetArchiveAttributes = function (logger, sessionData, authToken, attributes, platformData, dbConnector, callback) {
    // do nothing
    callback({});
};

ConfluenceConnector.prototype.everyoneLeft = function(archiveID, callback) {
    //do nothing
    callback({});
};

ConfluenceConnector.prototype.getAuthTokenFromPlatform = function(logger, platformInfo, callback) {
    if (platformInfo && platformInfo.jwt) {
        callback && callback({platformToken: platformInfo.jwt});
    } else {
        logger = logger.getLogger({action: "getAuthTokenFromPlatform", module: "confluence", platformInfo: platformInfo});
        logger.error("JWT token not available for this entry");
        callback && callback({error: "JWT token not available for this entry"});
    }
};

ConfluenceConnector.prototype.logUserEvent = function(logger, dbConnector, ip, userInfo, userEvent, platformData, callback) {
    // nothing to do
    callback({});
};

function watchdog(contentData, callback)
{
    var i, key,
        keys = Object.keys(contentData),
        completed = 0, error = null;
    for (i=0; i<keys.length; i++)
    {
        key = keys[i];
        if (contentData[key].error) {
            error = contentData[key].error;
            break;
        } else if (contentData[key].status == "onprogress") {
            break;
        } else {
            // success
            completed++;
        }
    }

    if (completed === keys.length)
    {
        callback({status: "completed"});
    } else if (error) {
        callback({error: error});
    } else {
        // retry later
        setTimeout(function() {
            watchdog(contentData, callback);
        }, 200);
    }
}

ConfluenceConnector.prototype.getUsersInfoAndSettings = function(/*req, userData, callback*/) {
    throw new Error("Not implemented");
    // userData {
    //   "data": [
    //     {
    //       "projectId": "NGP-14",
    //       "platformSiteID: "8f896fa1-641f-3e55-9a60-5ff48c4696e5_10028",
    //       "userId": "557058:18e32678-32c8-4d4b-b838-55f0ee6f9597",
    //       "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiI1NTcwNTg6MThlMzI2NzgtMzJjOC00ZDRiLWI4MzgtNTVmMGVlNmY5NTk3IiwicXNoIjoiZTdkMGU3ODI3ZTAzYjQ1ZTk2MDkyNzM1NGM3OWM0YzZiYWE4MDYzZjY3ODE2MzEyYjViYzhiZjIwYTVmMjljNSIsImlzcyI6IjhmODk2ZmExLTY0MWYtM2U1NS05YTYwLTVmZjQ4YzQ2OTZlNSIsImNvbnRleHQiOnt9LCJleHAiOjE1NTI1NTkwNDYsImlhdCI6MTU1MjU1ODE0Nn0.wx1uICg_QyVNnUlM-K8sICsMy1ieXqr_t3nylNwTpQc"
    //     }
    //   ],
    //   "platformKind": "jira"
    // }
    // const config = this.config;
    // let logger = req.bas.logger.getLogger({
    //     action: "getUsersInfoAndSettings",
    //     module: "confluence"
    // });
    // var i, contentData, user, claims, iss;
    // var ret = {data: []};
    // var usersPerProject = {};
    // var sessionManager = this.sessionManager;
    //
    // this.serverUtils.checkServerAPICredentials(req)
    //     .then(allowed => {
    //         if (allowed) {
    //             // get platform info data if archive is loaded
    //             if (userData.data && userData.data.length) {
    //                 logger.info("getting users info and setting from Confluence platform ", {userData: userData.data});
    //                 contentData = {};
    //                 sessionManager.createSession(logger, "getUsersInfoAndSettings", function (obj) {
    //                     var sessionData, dbConnector;
    //
    //                     if (obj.error) {
    //                         callback && callback({error: "Cannot create db session " + obj.error});
    //                     } else {
    //                         sessionData = obj;
    //                         dbConnector = obj.dbConnector;
    //
    //                         // one data for each (project, user)
    //                         for (i = 0; i < userData.data.length; i++) {
    //                             user = userData.data[i];
    //
    //                             // JWT signature will be verified in the doGetContentMetadata function
    //                             claims = jwt.decode(user.token, '', true);
    //                             iss = claims.iss;
    //
    //                             (function (user, iss) {
    //                                 var projectId, platformSiteID, key;
    //                                 var accountUserId = user.userId;
    //                                 contentData[accountUserId] = {status: "onprogress"};
    //
    //                                 projectId = user.projectId;
    //                                 platformSiteID = user.platformSiteID;
    //                                 key = projectId + "_" + platformSiteID;
    //
    //                                 if (!usersPerProject[key]) {
    //                                     usersPerProject[key] = {
    //                                         projectId: projectId,
    //                                         platformSiteID: platformSiteID,
    //                                         platformArchiveName: projectId,
    //                                         users: []
    //                                     };
    //                                 }
    //
    //                                 dbConnector.getConnectorData("confluence", iss, function (instanceObj) {
    //                                     var scopes = "READ";
    //                                     var baseUrl, accessToken;
    //
    //                                     if (instanceObj.error || !instanceObj.sharedSecret) {
    //                                         contentData[accountUserId] = {
    //                                             status: "error",
    //                                             error: instanceObj.error ? instanceObj.error : "The instance seems to non be authenticated. Please try to reinstall the plugin"
    //                                         };
    //                                     } else {
    //                                         if (instanceObj.oauthClientId && accountUserId) {
    //                                             baseUrl = instanceObj.baseUrl;
    //                                             getAccessToken(instanceObj, null, accountUserId, scopes, true,function (obj) {
    //                                                 if (obj.error) {
    //                                                     contentData[accountUserId] = {status: "error", error: obj.error};
    //                                                 } else {
    //                                                     accessToken = obj.accessToken;
    //                                                     dbConnector.getPlatformInfoByIDs(projectId, platformSiteID, function(obj) {
    //                                                         var platformInfo;
    //                                                         if (obj.error) {
    //                                                             contentData[accountUserId] = {status: "error", error: obj.error};
    //                                                         } else {
    //                                                             platformInfo = JSON.parse(obj.PLATFORM_INFO);
    //
    //                                                             doGetContentMetadata(dbConnector, user.token, platformInfo.contentID, config, logger,function(obj) {
    //                                                                 if (obj.error) {
    //                                                                     contentData[accountUserId] = {status: "error", error: obj.error};
    //                                                                 } else {
    //                                                                     // logger.warn(obj);
    //                                                                     usersPerProject[key].containerPageUrl = obj._links.base + obj._links.webui;
    //                                                                     usersPerProject[key].containerId = platformInfo.contentID;
    //                                                                     usersPerProject[key].containerName = obj.title;
    //                                                                     usersPerProject[key].containerBaseUrl = obj._links.base;
    //
    //                                                                     getWatches(instanceObj.baseUrl, platformInfo.contentID, accessToken, logger, config,function(obj) {
    //                                                                         var eventUser;
    //                                                                         if (obj.error) {
    //                                                                             contentData[accountUserId] = {status: "error", error: obj.error};
    //                                                                         } else {
    //                                                                             if (obj.users) {
    //                                                                                 usersPerProject[key].users = usersPerProject[key].users.concat(obj.users);
    //                                                                             }
    //
    //                                                                             eventUser = usersPerProject[key].users.find(function(user) {return user.id === accountUserId});
    //
    //                                                                             if (eventUser) {
    //                                                                                 // nothing to do, the user is already a watcher
    //                                                                                 contentData[accountUserId] = {status: "success"};
    //                                                                             } else {
    //                                                                                 // dbConnector, config, baseUrl, userId, jwtToken, contentData, callback
    //                                                                                 getUserDetailsAsUser(baseUrl, accountUserId, accessToken, contentData, logger, config,function (obj) {
    //                                                                                     if (obj.error) {
    //                                                                                         logger.error("failed getUserDetails " + obj.error, null, obj);
    //                                                                                     } else {
    //                                                                                         usersPerProject[key].users.push(obj);
    //                                                                                     }
    //                                                                                 });
    //                                                                             }
    //                                                                         }
    //                                                                     });
    //                                                                 }
    //                                                             });
    //                                                         }
    //                                                     });
    //                                                 }
    //                                             });
    //                                         } else {
    //                                             contentData[accountUserId] = {
    //                                                 status: "error",
    //                                                 error: "invalid credentials, missing oauthClientId or accountUserId"
    //                                             };
    //                                         }
    //                                     }
    //                                 });
    //                             })(user, iss);
    //                         }
    //
    //                         watchdog(contentData, function () {
    //                             var keys, i;
    //                             sessionManager.releaseSession(sessionData);
    //
    //                             keys = Object.keys(usersPerProject);
    //
    //                             for (i=0; i<keys.length; i++) {
    //                                 ret.data.push(usersPerProject[keys[i]]);
    //                             }
    //
    //                             logger.info("returning users info and setting from Confluence platform ", ret);
    //                             callback && callback(ret);
    //                         });
    //                     }
    //                 });
    //             } else {
    //                 // nothing to do
    //                 callback && callback(ret);
    //             }
    //         } else {
    //             callback && callback({error: "invalid credentials"});
    //         }
    //     })
    //     .catch(err => {
    //         callback && callback({error: err.toString()});
    //     });
};

let getUserDetails = function (dbConnector, config, baseUrl, userId, jwtToken, oauthToken, contentData, logger, callback) {
    callSaneFunctionFromLegacy((async function() {
        try {
            const verificationResult = await confluenceAdapter.verifyJWTAndGetInstallationData(dbConnector, connector_id, jwtToken);
            if (!verificationResult.ok) {
                throw new Error("JWT verification failed: " + verificationResult.reason);
            }

            const { installationData, decodedAuthToken, userId } = verificationResult.value;

            let userDetails;
            const userDetailsAsAppResult = await confluenceAdapter.getUserDetailsAsApp(installationData, userId);
            if (!userDetailsAsAppResult.ok) {
                const userDetailsAsUserResult = await confluenceAdapter.getUserDetailsAsUser(installationData, userId, oauthToken);
                if (!userDetailsAsUserResult.ok) {
                    throw new Error("Failed to get user details: " + userDetailsAsUserResult.reason);
                }
                userDetails = userDetailsAsUserResult.value;
            } else {
                userDetails = userDetailsAsAppResult.value;
            }

            return userDetails;
        } catch (err) {
            logger.error(`getUserDetails: ${err.message}`, err);
            throw err;
        }
    })(), callback);
}

var getUserDetailsAsUser = function (baseUrl, userId, accessToken, contentData, logger, config, callback) {
    const options = {
        method: 'GET',
        url: baseUrl + '/rest/api/user?privacyMode=true&accountId=' + userId,
        auth: {bearer: accessToken},
        headers: {
            'Accept': 'application/json'
        }
    };

    superagent
        .get(options.url)
        .set('Authorization', 'Bearer ' + accessToken)
        .set('Accept', 'application/json')
        .end(function (error, res) {
            _checkDeprecationHeaders(res, logger, options.url, config.appName);
            if (error) {
                contentData[userId] = {status: "error", error: error};
            } else {
                if (res.statusCode === 200) {
                    const user = res.body;
                    const parsedUrl = UrlModule.parse(baseUrl);
                    const hostname = UrlModule.format({
                        protocol: parsedUrl.protocol,
                        host: parsedUrl.host
                    });

                    const avatarUrl = user.profilePicture && user.profilePicture.path ? hostname + user.profilePicture.path : anonAvatarURL;
                    // email and displayName are not always available for GDPR
                    if (user.accountId) {
                        callback({
                            "avatarUrl": avatarUrl,
                            "fullName": user.displayName,
                            "id": user.accountId,
                            "userName": user.accountId,
                            "email": user.email
                        });
                        contentData[userId] = {status: "success"};
                    } else {
                        contentData[userId] = {status: "error", error: "cannot retrieve user details"};
                        callback({error: "cannot retrieve user details", user});
                    }
                } else if (res.statusCode === 401) {
                    callback && callback({error: "Failed to get user info: Unauthorized (401)"});
                } else {
                    callback && callback({error: "Failed to get user info: " + res.statusCode});
                }
            }
        });
};

var getWatches = function(baseUrl, contentId, accessToken, logger, config, callback) {
    logger = logger.getLogger({action: "getWatches"});
    var url, options;
    var ret = {
        users: []
    };

    function handlePagination(nextUrl, ret) {
        if (nextUrl) {
            url = baseUrl + nextUrl + "&privacyMode=true";
        } else {
            // TODO: API_V2 it could be deprecated in the future
            url = baseUrl + '/rest/api/content/' + contentId + '/notification/child-created?privacyMode=true';
        }

        options = {
            method: 'GET',
            url: url,
            auth: {bearer: accessToken},
            headers: {
                'Accept': 'application/json'
            }
        };
        // logger.info('Requesting watches for : ' + contentId + " token " + accessToken);

        superagent
            .get(options.url)
            .set('Authorization', 'Bearer ' + accessToken)
            .set('Accept', 'application/json')
            .end(function (error, res) {
                _checkDeprecationHeaders(res, logger, options.url, config.appName);
                const parsedUrl = UrlModule.parse(baseUrl);
                const hostname = UrlModule.format({
                    protocol: parsedUrl.protocol,
                    host: parsedUrl.host
                });

                if (error) {
                    logger.error(error.toString(), error);
                    callback && callback(ret);
                } else {
                    // TODO: handle result pagination
                    const users = res.body;
                    if (users.results && users.results.length) {
                        const contentData = {};
                        for (let i = 0; i < users.results.length; i++) {
                            const watcher = users.results[i].watcher;
                            const userId = watcher.accountId;
                            contentData[userId] = {status: "onprogress"};

                            if (watcher.profilePicture && watcher.profilePicture.path &&
                                watcher.details && watcher.details.personal && watcher.details.personal.email) {
                                ret.users.push({
                                    "avatarUrl": hostname + watcher.profilePicture.path,
                                    "fullName": watcher.displayName,
                                    "id": userId,
                                    "userName": userId,
                                    "email": watcher.details.personal.email
                                });
                                contentData[userId] = {status: "success"};
                            } else {
                                // https://developer.atlassian.com/cloud/confluence/rest/#api-user-get
                                getUserDetailsAsUser(baseUrl, userId, accessToken, contentData, logger, config,function (obj) {
                                    if (obj.error) {
                                        logger.error("failed getUserDetails", null, obj);
                                    } else {
                                        ret.users.push(obj);
                                    }
                                });
                            }
                        }
                        watchdog(contentData, function () {
                            if (users._links && users._links.next) {
                                handlePagination(users._links.next, ret);
                            } else {
                                callback && callback(ret);
                            }
                        });
                    } else {
                        callback && callback(ret);
                    }
                }
        });
    }
    handlePagination(null, ret);
};

ConfluenceConnector.prototype.getProjectMembers = function(logger, dbConnector, ip, userInfo, userEvent, platformData, callback) {
    // This code sample uses the 'request' library:
    // https://www.npmjs.com/package/request
    var platformInfo = JSON.parse(platformData.PLATFORM_INFO);
    var authToken = platformInfo.jwt;
    var claims, contentId, iss, ret;
    const config = this.config;
    logger = logger.getLogger({action: 'getProjectMembers', module: 'confluence'});

    if (authToken) {
        // JWT signature will be verified right after
        claims = jwt.decode(authToken, '', true);
        contentId = platformInfo.contentID;
        iss = claims.iss;
        ret = {users: []};

        var userKey, userAccountId, accessToken;
        if (claims.sub) {
            // GDPR: userKey deprecated, use urn:atlassian:connect:useraccountid instead
            userAccountId = claims.sub;
        } else if (claims.context && claims.context.user && claims.context.user.userKey) {
            userKey = claims.context.user.userKey;
        }

        dbConnector.getConnectorData("confluence", iss, function (instanceObj) {
            var scopes = "READ";
            if (instanceObj.error || !instanceObj.sharedSecret) {
                ret = instanceObj.error ? instanceObj : {error: "The instance seems to non be authenticated. Please try to reinstall the plugin"};
                callback && callback(ret);
            } else {
                try {
                    jwt.decode(authToken, instanceObj.sharedSecret, false);
                } catch (error) {
                    logger.error("JWT signature failed: " + error, error);
                    callback && callback({error: "JWT signature failed"});
                    return;
                }

                if (instanceObj.oauthClientId && (userKey || userAccountId)) {
                    if (userAccountId) {
                        getAccessToken(instanceObj, userKey, userAccountId, scopes, true,function (obj) {
                            if (obj.error) {
                                callback && callback(obj);
                            } else {
                                accessToken = obj.accessToken;
                                getWatches(instanceObj.baseUrl, contentId, accessToken, logger, config, callback);
                            }
                        });
                    } else {
                        logger.warn("GDPR Unexpected use of claims.context.user.userKey ", instanceObj);
                        callback({error: "Unexpected use of claims.context.user.userKey. Claim sub " + claims.sub});
                    }
                } else {
                    callback && callback({error: {message: "missing credentials: "}});
                }
            }
        });
    } else {
        logger.error("missing JWT credentials", {platformData, userInfo});
        callback && callback({error: {message: "missing JWT credentials"}});
    }
};

ConfluenceConnector.prototype.projectHasBeenSavedOnPlatform = function(logger, dbConnector, platformSiteID, platformArchiveID, callback) {
    callback && callback({status: true});
};

ConfluenceConnector.prototype.projectExistsOnPlatform = function(logger, dbConnector, platformSiteID, platformArchiveID, callback) {
    let config = this.config;
    dbConnector.getBASArchiveID(platformSiteID, platformArchiveID, function (obj) {
        var platformInfo, authToken, contentID;
        if (obj.error) {
            callback(obj);
        } else {
            if (obj.PLATFORM_INFO) {
                platformInfo = JSON.parse(obj.PLATFORM_INFO);
                authToken = platformInfo.jwt;
                contentID = platformInfo.contentID;

                if (authToken && contentID) {
                    doGetAttachmentMetadata(logger, dbConnector, contentID, platformArchiveID, authToken, config, function(obj) {
                        if (obj.error) {
                            obj.platformSiteID = platformSiteID;
                            obj.platformArchiveID = platformArchiveID;
                            callback(obj);
                        } else {
                            callback({
                                status: true,
                                response: obj
                            });
                        }
                    });
                } else {
                    callback({error: "missing authToken or contentID"});
                }
            } else {
                callback({error: "archive not loaded"});
            }
        }
    });
};

export {
    ConfluenceConnector,
    doRunRedisExpirationKeyListener as ConfluenceRedisExpirationKeyListener,
    makeRedisKey as confluenceMakeRedisKey,
}
