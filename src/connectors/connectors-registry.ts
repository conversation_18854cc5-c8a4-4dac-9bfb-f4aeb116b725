import type { Logger } from '@balsamiq/logging';
import type { Config } from '../configLoader.ts';
import type { Metrics } from '../metrics.ts';
import type { RedisAdapter } from '../redisAdapter.ts';
import type { PermalinkImageFormat, S3PermalinksImageStorageAdapter } from '../s3adapter.ts';
import type { ServerUtils } from '../server_utils.js';
import type { SessionData, SessionManager } from '../session-manager.ts';
import type { Wireframe2imageAdapter } from '../wireframe2image-adapter.ts';
import * as cloudConnectorModule from './cloud.js';
import * as confluenceConnectorModule from './confluence.js';
import * as jiraConnectorModule from './jira.js';
import * as webDemoConnectorModule from './webdemo.ts';
import JIRAForgeConnector from './jira-forge.js';
import type { ConfluenceAdapter } from '../atlassian-adapter.ts';
import type { DBConnector, PermalinkExtra, User } from '../database.ts';
import type { PermalinkData } from '../model.ts';
import type { Clock } from '../clock.ts';
import type { Readable } from 'stream';
import type { BASLegacyCallback } from '../calling-style.ts';
import type { Request, Response } from 'express';
import type { BASRequest } from '../request-context.ts';
import type { DataResidencyName } from '../environment-variables-schemas.ts';

// Every connector implements this interface
export interface Connector {
    generateArchiveID(): string;

    save(
        logger: Logger,
        dbConnector: DBConnector,
        user: User | null,
        archiveID: string,
        newPlatformArchiveName: string | null,
        archiveRevision: number,
        buffer: Buffer,
        dump: BmprDump,
        options: { fromClose?: boolean; fromRestore?: boolean },
        callback: BASLegacyCallback<{}>
    ): void;

    getRole(
        logger: Logger,
        dbConnector: DBConnector,
        token: string,
        platformSiteID: string,
        platformArchiveID: string,
        userInfo: any,
        callback: (result: { role: number }) => void
    ): void;

    getSnapshotUrl(baseDir: string, permalinkID: string, format: string, dataResidency: DataResidencyName): string;

    generateSnapshot({
        logger,
        platformKind,
        platformSiteID,
        platformArchiveID,
        resourceID,
        branchID,
        platformInfo,
        permalinkInfo,
        dataResidency,
    }: {
        logger: Logger;
        platformKind: string;
        platformSiteID: string;
        platformArchiveID: string;
        resourceID: string;
        branchID: string;
        platformInfo: Record<string, unknown>;
        permalinkInfo: PermalinkData['permalinkInfo'];
        dataResidency: DataResidencyName;
    }): Promise<unknown>;

    storePermalinkImage({
        dataStream,
        mimeType,
        permalinkID,
        permalinkInfo,
    }: {
        dataStream: Readable;
        mimeType: string;
        permalinkID: string;
        permalinkInfo?: PermalinkData['permalinkInfo'];
    }): Promise<unknown>;
    getDataResidency?(logger: Logger, platformSiteID: string, platformArchiveID: string): Promise<DataResidencyName>;

    deletePermalinkThumbnailImages(params: {
        permalinkIDs: string[];
        format: PermalinkImageFormat;
        dataResidency: DataResidencyName;
    }): Promise<unknown>;
    deletePermalinkImages(params: {
        permalinkIDs: string[];
        format: PermalinkImageFormat;
        dataResidency: DataResidencyName;
    }): Promise<unknown>;
    asyncGetPermalinkThumbnail(permalinkData: PermalinkData, logger: Logger): Promise<string>;

    asyncAuthorizedForArchive?(
        logger: Logger,
        sessionData: SessionData,
        platformArchiveID: string,
        platformSiteID: string,
        platformInfo: Record<string, unknown>,
        platformToken: string
    ): Promise<{ success: true }>;

    authorizedForArchive?(
        logger: Logger,
        sessionData: SessionData,
        platformArchiveID: string,
        platformSiteID: string,
        platformInfo: Record<string, unknown>,
        platformToken: string,
        callback: BASLegacyCallback<{ success: true }>
    ): void;

    asyncGetPermalink?(params: {
        sessionData: SessionData;
        dbConnector: DBConnector;
        permalinkData: PermalinkData;
        releaseSessionIfNotReleased: () => Promise<void>;
        req: BASRequest;
        res: Response;
    }): Promise<{}>;
    getPermalink?(
        params: {
            sessionData: SessionData;
            dbConnector: DBConnector;
            permalinkData: PermalinkData;
            releaseSessionIfNotReleased: () => Promise<void>;
            req: BASRequest;
            res: Response;
        },
        cb: BASLegacyCallback<{}>
    ): void;

    getUniqueUserId?(user: User): string;
    getUserKeyFromPlatformToken(params: { platformToken: string; dbConnector: DBConnector; logger: Logger }): Promise<string | null>;
    getAuthTokenFromPlatform(logger: Logger, platformInfo: object, callback: BASLegacyCallback<{ platformToken: string | null }>): void;
    loadFromPlatform(
        logger: Logger,
        dbConnector: DBConnector,
        authToken: string | null,
        platformSiteID: string,
        platformArchiveID: string,
        options: null,
        platformInfo: Record<string, unknown>,
        callback: BASLegacyCallback<{ id: string; buffer: Buffer; platformInfo?: Record<string, unknown> }>
    ): void;
    getPermalinkExtraFromPermalinkData(permalinkData: PermalinkData): PermalinkExtra;
    makePermalinkID(params?: {
        platformKind: string;
        platformSiteID: string | null;
        platformArchiveID: string;
        resourceID: string;
        branchID: string;
    }): string;
    generatePermalinkImage(params: {
        logger: Logger;
        permalinkData: PermalinkData;
        attachment?: {
            file: Readable;
            mimeType: string;
        };
        suffix?: string;
    }): Promise<unknown>;
}

export function initializeConnectors(
    sessionManager: SessionManager,
    logger: Logger,
    metrics: Metrics,
    permalinkImageStorageAdapter: S3PermalinksImageStorageAdapter,
    config: Config,
    serverUtils: ServerUtils,
    w2iAdapter: Wireframe2imageAdapter,
    redisAdapter: RedisAdapter,
    confluenceAdapter: ConfluenceAdapter,
    clock: Clock
): {
    jira: jiraConnectorModule.JIRAConnector;
    jiraForge: JIRAForgeConnector;
    webDemo: webDemoConnectorModule.WebDemoConnector;
    confluence: confluenceConnectorModule.ConfluenceConnector;
    cloud: cloudConnectorModule.CloudConnector;
    getConnector: (kind: string | null) => Connector | null;
} {
    const jira = new jiraConnectorModule.JIRAConnector(
        sessionManager,
        logger,
        metrics,
        permalinkImageStorageAdapter,
        config,
        serverUtils,
        w2iAdapter,
        redisAdapter
    );

    const jiraForge = new JIRAForgeConnector(
        sessionManager,
        logger,
        metrics,
        permalinkImageStorageAdapter,
        config,
        serverUtils,
        w2iAdapter,
        redisAdapter
    );

    const confluence = new confluenceConnectorModule.ConfluenceConnector(
        sessionManager,
        logger,
        metrics,
        config,
        serverUtils,
        permalinkImageStorageAdapter,
        w2iAdapter,
        redisAdapter,
        confluenceAdapter
    );

    const webDemo = new webDemoConnectorModule.WebDemoConnector(
        sessionManager,
        logger,
        metrics,
        config,
        permalinkImageStorageAdapter,
        w2iAdapter,
        serverUtils,
        redisAdapter
    );

    const cloud = new cloudConnectorModule.CloudConnector(
        sessionManager,
        logger,
        metrics,
        permalinkImageStorageAdapter,
        config,
        serverUtils,
        w2iAdapter,
        redisAdapter,
        clock
    );

    const getConnector = function (kind: string | null): Connector | null {
        if (kind === 'jira') {
            return jira;
        } else if (kind === 'jira-forge') {
            return jiraForge;
        } else if (kind === 'wd') {
            return webDemo;
        } else if (kind === 'confluence') {
            return confluence;
        } else if (kind === 'cloud') {
            return cloud;
        }
        return null;
    };

    return {
        jira,
        jiraForge,
        webDemo,
        confluence,
        cloud,
        getConnector,
    };
}
