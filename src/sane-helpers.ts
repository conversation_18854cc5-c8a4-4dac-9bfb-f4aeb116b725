import type { Logger } from '@balsamiq/logging';
import { BASCallbackError, callWithLegacyCallback } from './calling-style.ts';
import constants from './constants.ts';
import type { DBConnector } from './database.ts';
import type { User } from './database.ts';
import type { Metrics } from './metrics.ts';
import type { SessionData } from './session-manager.ts';
import assert from 'assert';

// Build a reverse map from numbers to role names
const ROLE_VALUES_TO_NAME: Record<number, string> = {};
for (const key of Object.keys(constants)) {
    if (key.startsWith('ROLE_')) {
        const value = constants[key as keyof typeof constants];
        assert(typeof value === 'number', `Expected value to be a number for key: ${key}`); // Ensure the value is a number
        ROLE_VALUES_TO_NAME[value] = key;
    }
}

function roleValueToName(value: number): string {
    return ROLE_VALUES_TO_NAME[value] || `${value}`;
}

export type GetValidUserForRoleParams = {
    token: string;
    dbConnector: DBConnector;
    metrics: Metrics;
    logger: Logger;
    role: number;
    sessionData: SessionData;
};

export async function getValidUserForRole({
    token,
    dbConnector,
    metrics,
    logger,
    role,
<<<<<<< HEAD
    sessionData
}: GetValidUserForRoleParams ): Promise<User> {
=======
    sessionData,
}: {
    token: string;
    dbConnector: DBConnector;
    metrics: Metrics;
    logger: Logger;
    role?: number | null;
    sessionData: SessionData;
}): Promise<User> {
>>>>>>> master
    const timer = metrics.trackTimeInterval('checkValidUserForRole: getUser');
    let user: User;
    try {
        user = await callWithLegacyCallback((cb) => dbConnector.getUser(token, cb));
    } catch (err: any) {
        const sessionError = new BASCallbackError('Session Error');
        const resultObj = err.resultObj;
        sessionError.params = { error: resultObj.error, busy: resultObj.busy, ignore: resultObj.ignore };
        throw sessionError;
    } finally {
        timer.stop();
    }

    if (role === undefined || role === null) {
        logger.error(`Unexpected ERROR: Undefined role for ${sessionData.action} action`);
        // at the moment we just log in case the role is not defined
        // throw new Error("Not authorized");
    } else {
        const userRole = Number.parseInt(user.PERMISSIONS);
        if (userRole < role) {
            logger.securityWarningIgnore(
                `Not authorized: ${roleValueToName(userRole)} < ${roleValueToName(role)}`,
                'GET_VALID_USER_FOR_ROLE_NOT_AUTHORIZED'
            );
            throw new Error('Not authorized');
        }
    }

    // TODO: remove this side effect
    sessionData.token = token;
    sessionData.archiveID = user.ARCHIVE_ID;

    return user;
}
