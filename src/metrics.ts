import type { Logger } from '@balsamiq/logging';
import type { Config } from './configLoader.ts';
import type {
    CloudWatch,
    GetMetricStatisticsCommandInput,
    GetMetricStatisticsCommandOutput,
    GetMetricStatisticsInput,
    MetricDatum,
    PutMetricDataCommandInput,
    PutMetricDataCommandOutput,
    StandardUnit,
} from '@aws-sdk/client-cloudwatch';
import assert from 'assert';

export interface CloudWatchInterface {
    getMetricStatistics(args: GetMetricStatisticsCommandInput, cb: (err: any, data?: GetMetricStatisticsCommandOutput) => void): void;
    putMetricData(args: PutMetricDataCommandInput, cb: (err: any, data?: PutMetricDataCommandOutput) => void): void;
}

export interface TimeTracker {
    stop(): number;
}

interface DatabaseConnectionsResult {
    error?: Error | string;
    stack?: string;
    connections?: number;
}

export class Metrics {
    private logger: Logger;
    private namespace: string;
    private buildNumber: number;
    private dbCloudWatch: CloudWatchInterface;
    private cloudWatch: CloudWatchInterface;
    private _values: Map<string, MetricDatum>;

    constructor({
        logger,
        namespace = 'balsamiq/bas',
        buildNumber,
        dbCloudWatch,
        cloudWatch,
    }: {
        logger: Logger;
        namespace?: string;
        buildNumber: string;
        dbCloudWatch: CloudWatchInterface;
        cloudWatch: CloudWatchInterface;
    }) {
        this.logger = logger.getLogger({ module: 'metrics' });
        this.namespace = namespace;
        this.buildNumber = Number.parseInt(buildNumber);
        this.dbCloudWatch = dbCloudWatch;
        this.cloudWatch = cloudWatch;
        this._values = new Map<string, MetricDatum>();
    }

    trackTimeInterval(metricName: string): TimeTracker {
        const timer = startTimer();
        return {
            stop: (): number => {
                const elapsed = timer.stop();
                this.addValue(metricName, elapsed, 'Milliseconds');
                return elapsed;
            },
        };
    }

    addValue(metricName: string, value: number, measureUnit: string): void {
        this.addValueWithDimensions([], metricName, value, measureUnit);
    }

    addValueWithDimensions(dimensions: { Name: string; Value: string }[], metricName: string, value: number, measureUnit: string): void {
        const metricsKey = dimensions
            .concat([{ Name: 'name', Value: metricName }])
            .map((dim) => `${dim.Name}:${dim.Value}`)
            .join('-');

        const metricDatum: MetricDatum = this._values.get(metricsKey) ?? {
            MetricName: metricName,
            Unit: measureUnit as StandardUnit,
            Dimensions: dimensions,
            StatisticValues: {
                Maximum: -Number.MAX_VALUE,
                Minimum: Number.MAX_VALUE,
                SampleCount: 0,
                Sum: 0,
            },
        };

        const statisticValues = metricDatum.StatisticValues;
        assert(statisticValues, 'StatisticValues should be defined');

        statisticValues.Sum = (statisticValues.Sum ?? 0) + value;
        statisticValues.SampleCount = (statisticValues.SampleCount ?? 0) + 1;
        statisticValues.Maximum = Math.max(statisticValues.Maximum ?? -Number.MAX_VALUE, value);
        statisticValues.Minimum = Math.min(statisticValues.Minimum ?? Number.MAX_VALUE, value);

        this._values.set(metricsKey, metricDatum);
    }

    resetAll(): Map<string, MetricDatum> {
        const res = this._values;
        this._values = new Map<string, MetricDatum>();
        return res;
    }

    putMetricData(): void {
        const logger = this.logger.getLogger({ action: 'putMetricData' });
        if (this._values.size === 0) {
            return;
        }

        let metricData: MetricDatum[] = [
            {
                MetricName: 'Build number',
                Dimensions: [],
                Unit: 'None',
                StatisticValues: {
                    Maximum: this.buildNumber,
                    Minimum: this.buildNumber,
                    SampleCount: 1,
                    Sum: this.buildNumber,
                },
            },
        ];

        const allMetricsData = this.resetAll();
        allMetricsData.forEach((value) => {
            metricData.push(value);
        });

        logger.info(`Uploading ${metricData.length} metrics`);
        while (metricData.length > 0) {
            const metricDataChunk = metricData.slice(0, 20);
            this.cloudWatch.putMetricData({ Namespace: this.namespace, MetricData: metricDataChunk }, (err) => {
                if (err) {
                    logger.error(`putMetricData failed ${err}`);
                }
            });
            metricData = metricData.slice(20);
        }
    }
}

function startTimer(): TimeTracker {
    const startAt = process.hrtime();
    return {
        stop: (): number => {
            const diff = process.hrtime(startAt);
            return diff[0] * 1e3 + diff[1] * 1e-6;
        },
    };
}
