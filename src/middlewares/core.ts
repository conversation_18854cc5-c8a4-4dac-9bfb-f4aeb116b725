/** @prettier */
import { assertBASRequest, isBASRequest, type BASRequest } from '../request-context.ts';
import type { NextFunction, Response, Request } from 'express';

/**
 * This function is used to wrap async request handlers.
 * Aligned with master branch pattern.
 */
export function basRequestHandler(fn: (req: BASRequest, res: Response) => Promise<void>) {
    return (req: Request, res: Response, next: NextFunction) => {
        assertBASRequest(req);
        fn(req, res)
            .catch(next)
            .finally(() => {});
    };
}

/**
 * This function is used to wrap async middleware functions.
 * Aligned with master branch pattern.
 */
export function basMiddleware(fn: (req: BASRequest, res: Response, next: NextFunction) => Promise<void>) {
    return (req: Request, res: Response, next: NextFunction) => {
        assertBASRequest(req);
        fn(req, res, next).catch(next);
    };
}

/**
 * Global error handler aligned with master branch pattern.
 * Improved error handling with proper type checking and header validation.
 */
export function globalErrorHandler(err: unknown, req: Request, res: Response, next: NextFunction) {
    const availableLogger = isBASRequest(req) ? req.bas.logger : console;
    const error = err instanceof Error ? err : new Error(`Unexpected error ${err}`);
    availableLogger.error(error.message, error);

    if (res.headersSent) {
        availableLogger.error('Headers already sent, cannot send error response');
        return next(err); // Delegate to the default Express error handler (will close the connection and fail the request)
    }

    res.status(500).json({ error: 'Internal Server Error' });
}
