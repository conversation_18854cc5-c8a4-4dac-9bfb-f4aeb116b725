/** @prettier */
import { BASRequestContext, type BASRequest } from '../request-context.ts';
import type { NextFunction, Response, Request } from 'express';

/**
 * This function is used to wrap async request handlers.
 * Aligned with master branch pattern.
 */
export function basRequestHandler(fn: (req: BASRequest, res: Response, next: NextFunction) => Promise<void>) {
    return (req: Request, res: Response, next: NextFunction) => {
        BASRequestContext.assertBASRequestContext(req);
        fn(req, res, next).catch(next);
    };
}

/**
 * Middleware wrapper for BAS request context
 * Ensures request has BAS context before proceeding
 */
export function basMiddleware(fn: (req: BASRequest, res: Response, next: NextFunction) => Promise<void>) {
    return basRequestHandler(fn);
}

/**
 * Global error handler aligned with master branch pattern.
 * Improved error handling with proper type checking and header validation.
 */
export function globalErrorHandler(err: unknown, req: Request, res: Response, next: NextFunction) {
    const availableLogger = BASRequestContext.isBASRequest(req) ? req.bas.logger : console;
    const error = err instanceof Error ? err : new Error(`Unexpected error ${err}`);
    availableLogger.error(error.message, error);

    if (res.headersSent) {
        availableLogger.error('Headers already sent, cannot send error response');
        return next(err); // Delegate to the default Express error handler
    }

    res.status(500).json({ error: 'Internal Server Error' });
}
