/** @prettier */
import type { NextFunction, Response } from 'express';
import type { BASRequest } from '../../request-context.ts';
import type { SessionData } from '../../session-manager.ts';
import { basMiddleware } from '../core.ts';
import { callSaneFunctionFromLegacy } from '../../calling-style.ts';
import { getValidUserForRole } from '../../sane-helpers.js';

/**
 * Validates user credentials and creates session data
 */
async function validateUser(req: BASRequest, api: string): Promise<SessionData> {
    const sessionData = await req.bas.acquireSession({ token: req.query.token as string });

    try {
        const user = await getValidUserForRole({
            token: req.query.token as string,
            dbConnector: sessionData.dbConnector,
            metrics: req.bas.metrics,
            logger: req.bas.logger,
            role: req.bas.roleForAccessTable[api as keyof typeof req.bas.roleForAccessTable],
            sessionData,
        });

        // Convert User type to SessionData.user type
        sessionData.user = {
            USERINFO: user.USERINFO || undefined,
            PERMISSIONS: user.PERMISSIONS || undefined,
        };
        return sessionData;
    } catch (userObj: any) {
        const resp: any = {
            error: userObj.error,
            busy: userObj.busy,
            ignore: userObj.ignore,
            userAgent: req.headers['user-agent'],
        };
        if (req.body.platformArchiveID) {
            resp.error += ` [${req.body.platformArchiveID}]`;
        }
        throw resp;
    }
}

/**
 * Creates user validation middleware
 * Validates user credentials and attaches session data to request
 */
export function createUserValidationMiddleware(api: string) {
    return basMiddleware(async (req: BASRequest, res: Response, next: NextFunction) => {
        try {
            const sessionData = await validateUser(req, api);
            (req as any).sessionData = sessionData;
            next();
        } catch (error: any) {
            req.bas.logger.warn('User validation failed', { error, api });
            res.status(401).json({
                error: error.error || 'User validation failed',
                busy: error.busy,
                ignore: error.ignore,
                userAgent: error.userAgent,
                code: 401,
            });
        }
    });
}
