/** @prettier */
import type { NextFunction, Response } from 'express';
import type { BASRequest } from '../../request-context.ts';
import type { SessionData } from '../../session-manager.ts';
import { basMiddleware } from '../core.ts';
import { callSaneFunctionFromLegacy } from '../../calling-style.ts';

/**
 * Validates user credentials and creates session data
 */
async function validateUser(req: BASRequest, api: string): Promise<SessionData> {
    return new Promise((resolve, reject) => {
        req.bas.sessionManager.createSession(req.bas.logger, req.route.path, (sessObj: any) => {
            if (sessObj.error) {
                return reject(sessObj);
            }

            const sessionData = sessObj;
            const token = req.query.token as string;

            callSaneFunctionFromLegacy(
                req.bas.getValidUserForRole({
                    token,
                    dbConnector: sessionData.dbConnector,
                    metrics: req.bas.metrics,
                    logger: req.bas.logger,
                    role: req.bas.minRoleForAccess[api as keyof typeof req.bas.minRoleForAccess],
                    sessionData,
                }),
                (userObj: any) => {
                    if (userObj.error) {
                        const resp: any = {
                            error: userObj.error,
                            busy: userObj.busy,
                            ignore: userObj.ignore,
                            userAgent: req.headers['user-agent'],
                        };
                        if (req.body.platformArchiveID) {
                            resp.error += ` [${req.body.platformArchiveID}]`;
                        }
                        return reject(resp);
                    }

                    sessionData.user = userObj;
                    resolve(sessionData);
                }
            );
        });
    });
}

/**
 * Creates user validation middleware
 * Validates user credentials and attaches session data to request
 */
export function createUserValidationMiddleware(api: string) {
    return basMiddleware(async (req: BASRequest, res: Response, next: NextFunction) => {
        try {
            const sessionData = await validateUser(req, api);
            (req as any).sessionData = sessionData;
            next();
        } catch (error: any) {
            req.bas.logger.warn('User validation failed', { error, api });
            res.status(401).json({
                error: error.error || 'User validation failed',
                busy: error.busy,
                ignore: error.ignore,
                userAgent: error.userAgent,
                code: 401
            });
        }
    });
}
