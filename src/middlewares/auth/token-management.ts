/** @prettier */
import type { NextFunction, Response } from 'express';
import type { BASRequest } from '../../request-context.ts';
import { basMiddleware } from '../core.ts';

/**
 * Updates platform authentication token in the database
 */
async function updatePlatformToken(req: BASRequest): Promise<any> {
    const sessionData = (req as any).sessionData;
    if (!sessionData) {
        throw new Error('Session data not available');
    }

    const token = sessionData.user.TOKEN;
    const platformToken = req.body.platformToken ?? null;

    return new Promise((resolve, reject) => {
        sessionData.dbConnector.updatePlatformAuthToken(token, platformToken, req.bas.clock, (result: any) => {
            if (result.error) {
                reject(result);
            } else {
                resolve(result);
            }
        });
    });
}

/**
 * Creates token update middleware
 * Updates platform authentication tokens in the database
 */
export function createTokenUpdateMiddleware() {
    return basMiddleware(async (req: BASRequest, res: Response, next: NextFunction) => {
        try {
            const result = await updatePlatformToken(req);
            (req as any)._updatedTokenResult = result;
            next();
        } catch (error: any) {
            req.bas.logger.error('Token update failed', error);
            // Don't fail the request for token update errors, just log and continue
            (req as any)._updatedTokenResult = { error: error.message || 'Token update failed' };
            next();
        }
    });
}
