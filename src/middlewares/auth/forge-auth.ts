/** @prettier */
import type { NextFunction, Response } from 'express';
import type { BASRequest } from '../../request-context.ts';
import { basMiddleware } from '../core.ts';
import { authMiddleware } from '../../connectors/lib/validateForgeAuthHeader.js';
import { callSaneFunctionFromLegacy } from '../../calling-style.ts';

interface PlatformInfo {
    kind?: string;
    appId?: string;
    fitClaims?: any;
    [key: string]: any;
}

/**
 * Extracts platform information from request body or attached platform data
 */
function extractPlatformInfo(req: BASRequest): PlatformInfo | null {
    // Case A: body-based platform info
    if (req.body?.platformInfo && req.body?.kind) {
        try {
            const platformInfo = JSON.parse(req.body.platformInfo);
            return {
                ...platformInfo,
                kind: req.body.kind,
                appId: platformInfo.appId,
                source: 'body',
            };
        } catch {
            return null;
        }
    }

    // Case B: sessionData→DB fetch
    if ((req as any).platformData) {
        const pd = (req as any).platformData;
        try {
            const platformInfo = JSON.parse(pd.PLATFORM_INFO);
            return {
                ...platformInfo,
                kind: pd.PLATFORM_KIND,
                appId: platformInfo?.fitClaims?.app?.id,
                source: 'database',
            };
        } catch {
            return null;
        }
    }

    return null;
}

/**
 * Validates forge authentication and attaches claims to request
 */
async function validateForgeAuth(req: BASRequest, appId: string): Promise<any> {
    return new Promise((resolve, reject) => {
        callSaneFunctionFromLegacy(authMiddleware(req, appId, req.bas.logger), (claims: any) => {
            if (!claims || claims?.error) {
                reject(claims || { error: 'Authentication failed' });
            } else {
                resolve(claims);
            }
        });
    });
}

/**
 * Attaches forge claims to the request and updates platform info
 */
function attachForgeClaims(req: BASRequest, claims: any, platformInfo: PlatformInfo): void {
    // Attach platform token for downstream middleware
    req.body.platformToken = claims.accessTokens?.user;

    // Update platform info with claims
    platformInfo.fitClaims = claims;

    req.bas.logger.info('Forge auth: attaching fitClaims', {
        source: platformInfo.source,
        hasApiBaseUrl: !!claims.siteUrl,
        platformInfoKeys: Object.keys(platformInfo)
    });

    // Persist updated platformInfo back to the same place we got it
    if (platformInfo.source === 'body' && req.body?.platformInfo) {
        req.body.platformInfo = JSON.stringify(platformInfo);
        req.bas.logger.info('Forge auth: updated req.body.platformInfo');
    } else if (platformInfo.source === 'database' && (req as any).platformData) {
        (req as any).platformData.PLATFORM_INFO = JSON.stringify(platformInfo);
        req.bas.logger.info('Forge auth: updated req.platformData.PLATFORM_INFO');
    }
}

/**
 * Creates forge authentication middleware
 * Handles jira-forge authentication by validating tokens and attaching claims
 */
export function createForgeAuthMiddleware() {
    return basMiddleware(async (req: BASRequest, res: Response, next: NextFunction) => {
        const platformInfo = extractPlatformInfo(req);

        // Only process jira-forge requests with valid appId
        if (!platformInfo || platformInfo.kind !== 'jira-forge' || !platformInfo.appId) {
            return next();
        }

        try {
            const claims = await validateForgeAuth(req, platformInfo.appId);
            attachForgeClaims(req, claims, platformInfo);
            next();
        } catch (err: any) {
            req.bas.logger.error('Forge authentication failed', err);
            res.status(401).json({
                error: err?.message || 'Forge authentication failed',
                code: 401,
            });
        }
    });
}
