/** @prettier */
// Re-export core middleware functions from master branch pattern
export { basRequestHandler, basMiddleware, globalErrorHandler } from './core.ts';

// Export forge-specific middleware
export { createForgeAuthMiddleware } from './auth/forge-auth.ts';
export { createUserValidationMiddleware } from './auth/user-validation.ts';
export { createTokenUpdateMiddleware } from './auth/token-management.ts';
export { createPlatformDataMiddleware } from './platform/platform-data.ts';

// Export auth pipeline
export { createApiAuthPipeline } from './pipelines/auth-pipeline.ts';

// Export legacy functions for backward compatibility (to be removed in Phase 2)
export { makeHand<PERSON>, checkValidUser } from './legacy.ts';
