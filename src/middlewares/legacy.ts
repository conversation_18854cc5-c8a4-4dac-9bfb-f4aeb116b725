/** @prettier */
// Legacy middleware functions - to be removed in Phase 2
import { makeLegacyResultFromError } from '../calling-style.ts';
import { BASRequestContext, type BASRequest } from '../request-context.ts';
import type { SessionData, SessionManager } from '../session-manager.ts';
import type { NextFunction, Response, Request, Application } from 'express';
import * as express from 'express';
import cors from 'cors';
import hsts from 'hsts';
import bodyParser from 'body-parser';
import bodyParserErrorHandler from 'express-body-parser-error-handler';
import compression from 'compression';
import proxy from 'express-http-proxy';
import type { Config } from '../configLoader.ts';
import type { BuildNumberTracker } from '../track_build_number.ts';
import type { Logger } from '@balsamiq/logging';
import type { RedisAdapter } from '../redisAdapter.ts';
import type { ServerUtils } from '../server_utils.js';
import path from 'path';
import { type MinRoleForAccess } from '../role-for-access-table.ts';
import type { BlockList } from '../blocklist.ts';
import { callSaneFunctionFromLegacy } from '../calling-style.ts';
import type { GetValidUserForRoleParams } from '../sane-helpers.ts';
import type { User } from '../database.ts';
import type { Metrics } from '../metrics.ts';
import { authMiddleware } from '../connectors/lib/validateForgeAuthHeader.js';
import type { Clock } from '../clock.ts';
import con from '../constants.ts';
import { basRequestHandler } from './core.ts';

// Add missing type for AnyConnector
type AnyConnector = any;

// NOTE: Avoid using params and switch to req.bas (BASRequestContext).
export function makeHandler(actionName: string, actionFunction: any, params: BASRequestContext) {
    async function asyncRequestHandler(req: BASRequest, res: Response) {
        req.bas.addLoggerInfo({ action: actionName });
        function returnJSON(result: object) {
            if ('error' in result && result.error) {
                req.bas.logger.error(`${result.error}`, null, { return: true });
            }
            res.json(result);
        }

        const actionParams = {
            ...params,
            actionName,
            minRoleForAccess: req.bas.minRoleForAccess[actionName as keyof MinRoleForAccess],

            getSessionData: req.bas.acquireSession.bind(req.bas),
            releaseSessionIfNotReleased: req.bas.releaseSession.bind(req.bas),
            verifyAdminCredential: req.bas.verifyAdminCredential.bind(req.bas),

            req,
            res,
        };

        try {
            let result = await actionFunction(actionParams);
            if (result) {
                returnJSON(result);
            }
            // If result is undefined, the response is handled by the action
        } catch (e) {
            const err = e as Error;
            req.bas.logger.error(err.toString(), err);
            const obj = makeLegacyResultFromError(err);
            returnJSON(obj);
        } finally {
            await req.bas.releaseSession();
        }
    }

    return function requestHandler(req: BASRequest, res: Response) {
        asyncRequestHandler(req, res).catch((err) => {
            req.bas.logger.error(err.toString(), err);
            res.status(500).send(`Unexpected error while handling request`);
        });
    };
}

export function checkValidUser(api: string, req: BASRequest, res: Response, onSuccess: (sessionData: SessionData) => void) {
    req.bas.sessionManager.createSession(req.bas.logger, req.route.path, (sessObj: any) => {
        if (sessObj.error) {
            return req.bas.returnJSON(sessObj, res);
        }

        const sessionData = sessObj;
        const token = req.query.token as string;

        callSaneFunctionFromLegacy(
            req.bas.getValidUserForRole({
                token,
                dbConnector: sessionData.dbConnector,
                metrics: req.bas.metrics,
                logger: req.bas.logger,
                role: req.bas.minRoleForAccess[api as keyof typeof req.bas.minRoleForAccess],
                sessionData,
            }),
            (userObj: any) => {
                if (userObj.error) {
                    const resp: any = {
                        error: userObj.error,
                        busy: userObj.busy,
                        ignore: userObj.ignore,
                        userAgent: req.headers['user-agent'],
                    };
                    if (req.body.platformArchiveID) {
                        resp.error += ` [${req.body.platformArchiveID}]`;
                    }
                    return req.bas.returnJSON(resp, res, sessionData);
                }

                sessionData.user = userObj;
                onSuccess(sessionData);
            }
        );
    });
}

export function checkValidUserMiddleware(api: string) {
    return (req: BASRequest & { sessionData?: any }, res: Response, next: NextFunction) => {
        checkValidUser(api, req, res, (sessionData) => {
            req.sessionData = sessionData;
            next();
        });
    };
}

export function attachPlatformData() {
    return (req: BASRequest & { sessionData?: any }, res: Response, next: NextFunction) => {
        const sessionData = (req as any).sessionData;
        if (!sessionData) return next();

        const db = sessionData.dbConnector;
        const archiveID = sessionData.user.ARCHIVE_ID;
        db.getPlatformData(archiveID, (obj: any) => {
            if (obj.error) {
                return req.bas.returnJSON(obj, res, sessionData);
            }
            (req as any).platformData = obj;
            next();
        });
    };
}

// Legacy forge claims middleware - replaced by modular version
export function forgeClaimsMiddleware() {
    return (req: BASRequest, res: Response, next: NextFunction) => {
        let kind: string | undefined;
        let platformInfo: any;
        let appId: string | undefined;

        // Case A: body-based
        if (req.body?.platformInfo && req.body?.kind) {
            try {
                platformInfo = JSON.parse(req.body.platformInfo);
                kind = req.body.kind;
                appId = platformInfo.appId;
            } catch {
                return next();
            }
        }
        // Case B: sessionData→DB fetch
        else if ((req as any).platformData) {
            const pd = (req as any).platformData;
            kind = pd.PLATFORM_KIND;
            try {
                platformInfo = JSON.parse(pd.PLATFORM_INFO);
                appId = platformInfo?.fitClaims?.app?.id;
            } catch {
                return next();
            }
        } else {
            return next(); // no platformInfo at all
        }

        if (kind === 'jira-forge' && appId) {
            try {
                callSaneFunctionFromLegacy(authMiddleware(req, appId, req.bas.logger), function (claims: any) {
                    if (!claims || claims?.error) {
                        req.bas.returnJSON(claims, res);
                    } else {
                        req.body.platformToken = claims.accessTokens?.user;
                        platformInfo['fitClaims'] = claims;

                        // persist updated platformInfo back to the same place we got it
                        if (req.body?.platformInfo) {
                            req.body.platformInfo = JSON.stringify(platformInfo);
                        } else {
                            (req as any).platformData.PLATFORM_INFO = JSON.stringify(platformInfo);
                        }
                        next();
                    }
                });
            } catch (err: any) {
                req.bas.returnJSON({ error: err.message || 'Unexpected error' }, res);
            }
        } else {
            next();
        }
    };
}

export function updateUserTokenMiddleware() {
    return (req: BASRequest & { sessionData?: any }, res: Response, next: NextFunction) => {
        const sessionData = req.sessionData!;
        const token = req.sessionData.user.TOKEN;
        const platformToken = req.body.platformToken ?? null;

        sessionData.dbConnector.updatePlatformAuthToken(token, platformToken, req.bas.clock, (result: any) => {
            (req as any)._updatedTokenResult = result;
            next();
        });
    };
}

export function configureMiddlewares(
    app: Application,
    params: {
        config: Config;
        logger: Logger;
        redisAdapter: RedisAdapter;
        sessionManager: SessionManager;
        serverUtils: ServerUtils;
        buildNumberTracker: BuildNumberTracker;
        appDirName: string;
        minRoleForAccess: MinRoleForAccess;
        getConnector: (kind: string) => AnyConnector;
        blockList: BlockList;
        returnJSON: (data: object, res: Response, sessionData?: SessionData, disableLog?: boolean) => void;
        metrics: Metrics;
        sane_getValidUserForRole: (params: GetValidUserForRoleParams) => Promise<User>;
        clock: Clock;
    }
) {
    const { config, buildNumberTracker, appDirName } = params;

    app.set('x-powered-by', false);
    app.set('trust proxy', true); // https://expressjs.com/en/api.html - "Application Settings" section - Needed to have a meaningful "req.ip" value
    app.set('port', config.port);

    app.use(function (_req, res, next) {
        res.setHeader('Referrer-Policy', 'origin');
        next();
    });

    app.use(function (req, res, next) {
        BASRequestContext.augmentRequestObject(req, res, params);
        next();
    });

    app.use(function (req, res, next) {
        BASRequestContext.assertBASRequestContext(req);
        try {
            decodeURIComponent(req.path);
        } catch (e) {
            const err = e as Error;
            req.bas.logger.warn(err.message + req.path, { err });
            res.json({ code: 400, error: 'Bad Request' });
            return;
        }
        next();
    });

    app.use(
        cors({
            exposedHeaders: ['x-balsamiq-archive-revision'],
            maxAge: 86400,
        })
    );
    app.use(bodyParser.urlencoded({ limit: '100mb', extended: true }));
    app.use(bodyParser.json({ limit: '100mb' }));
    app.use(
        bodyParserErrorHandler({
            onError: (err, req) => {
                const message = err.toString();
                if (BASRequestContext.isBASRequest(req)) {
                    req.bas.logger.info(message);
                } else {
                    console.log(message);
                }
            },
        })
    );

    app.use(
        hsts({
            maxAge: 31536000, // Must be at least 1 year to be approved
            includeSubDomains: true, // Must be enabled to be approved
        })
    );

    app.use('/static', express.static(path.join(appDirName, 'static'), { maxAge: 31536000000 })); // one year in milliseconds

    app.use(compression());

    // Gather build number information from the request headers and collects metrics
    app.use(basRequestHandler(buildNumberTracker.asyncMiddleware.bind(buildNumberTracker)));

    app.use(function (req, res, next) {
        // Log headers coming from BWD requests
        BASRequestContext.assertBASRequestContext(req);
        // NOTE: in theory it would be nice to log each and every request.
        // In practise, as of today, we don't have enough capacity in LBC
        // to ingest such a volume of logs, so we limit the log to
        // BWD only, to build the statistics that we need.
        if (
            req.headers['user-agent'] &&
            req.headers['user-agent'].startsWith('Balsamiq Wireframes') &&
            req.path !== '/getRTCAuthToken' &&
            req.path !== '/getUsersList' &&
            req.path !== '/health' &&
            req.path !== '/getArchiveRevision' &&
            req.path !== '/broadcastMessage'
        ) {
            req.bas.logger.info(req.method + ' ' + req.path + '. Start request', {
                action: 'root',
                userAgent: { header: req.headers['user-agent'] },
            });
        }
        next();
    });

    // Setup bas proxying
    config.proxyConfig.forEach(function (route) {
        app.use(
            route.prefix,
            proxy(route.host, {
                https: true,
                proxyReqPathResolver: function (req) {
                    return route.path + (req.url.startsWith('/') ? req.url.substring(1) : req.url);
                },
            })
        );
    });
}
