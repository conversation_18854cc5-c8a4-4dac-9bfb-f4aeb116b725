import redis from 'redis';
import type { Logger } from '@balsamiq/logging';

const PLATFORM_DATA_EXPIRATION_IN_SEC = 3600 * 24;

class RedisAdapter {
    public readonly config: ConstructorParameters<typeof RedisAdapter>[0];
    private logger: Logger;

    private _client?: ReturnType<typeof redis.createClient>;
    private get client() {
        if (!this._client) {
            throw new Error('Redis client not initialized.');
        }
        return this._client;
    }

    constructor(config: { port: number; host: string; database?: number }, logger: Logger) {
        this.config = config;
        this.logger = logger.getLogger({ subsystem: 'redis' });
    }

    async init() {
        this._client = await redis
            .createClient({
                socket: {
                    host: this.config.host,
                    port: this.config.port,
                    reconnectStrategy: (retries: number) => Math.round(500 + Math.random() * 1000 * Math.min(5, retries)),
                    connectTimeout: 5000,
                },
                database: this.config.database,
            })
            .on('connect', () => {
                this.logger.info('Connected to Redis.');
            })
            .on('ready', () => {
                this.logger.info('Redis is ready.');
            })
            .on('end', () => {
                this.logger.info('Redis connection ended.');
            })
            .on('error', (err: Error) => {
                this.logger.error(`Redis error: ${err}`, err);
            })
            .on('reconnecting', () => {
                this.logger.info('Reconnecting to Redis...');
            })
            .connect();
    }

    duplicate(logger: Logger) {
        return new RedisAdapter(this.config, logger);
    }

    async getPlatformDataByArchiveID(basArchiveID: string) {
        const key = KEYS.platformDataByArchiveId(basArchiveID);
        const value = await this.client.get(key);
        return value ? JSON.parse(value) : null;
    }

    async setPlatformData(platformData: any) {
        const key = KEYS.platformDataByArchiveId(platformData.BAS_ARCHIVE_ID);
        await this.client.setEx(key, PLATFORM_DATA_EXPIRATION_IN_SEC, JSON.stringify(platformData));
    }

    async setBuildNumber(product: string, buildNumber: number) {
        const key = KEYS.buildNumber();
        await this.client.hSet(key, product, buildNumber);
    }

    async getBuildNumber(product: string) {
        const key = KEYS.buildNumber();
        const value = await this.client.hGet(key, product);
        return value ? parseInt(value) : null;
    }

    async enableKeyspaceEvents() {
        await this.client.sendCommand(['config', 'set', 'notify-keyspace-events', 'Ex']);
    }

    async subscribeToKeyspaceEvents(listener: (message: string, channel: string) => void) {
        await this.client.subscribe(`__keyevent@${this.config.database ?? 0}__:expired`, listener);
    }

    // Redis methods pass-through

    async eval(script: string, keys: string[], args: string[]) {
        await this.client.eval(script, { keys, arguments: args });
    }

    async del(key: string) {
        return await this.client.del(key);
    }

    async get(key: string) {
        return await this.client.get(key);
    }

    async setex(key: string, seconds: number, value: string) {
        return await this.client.setEx(key, seconds, value);
    }

    async lrange(key: string, start: number, end: number) {
        return await this.client.lRange(key, start, end);
    }

    async hmset(key: string, data: Record<string, string>) {
        return await this.client.hSet(key, data); // hset and hmset are same in node redis
    }

    async hdel(key: string, field: string) {
        return await this.client.hDel(key, field);
    }

    async hexists(key: string, field: string) {
        return await this.client.hExists(key, field);
    }

    async hvals(key: string) {
        return await this.client.hVals(key);
    }

    async sadd(key: string, value: string) {
        return await this.client.sAdd(key, value);
    }

    async expire(key: string, seconds: number) {
        return await this.client.expire(key, seconds);
    }

    async flushdb() {
        return await this.client.flushDb();
    }

    async quit() {
        return await this.client.quit();
    }

    async sendCommand(command: string[]) {
        return await this.client.sendCommand(command, {});
    }

    async subscribe(channels: string | Array<string>, listener: (message: string, channel: string) => void) {
        return await this.client.subscribe(channels, listener);
    }

    async unsubscribe(channels: string | Array<string>) {
        return await this.client.unsubscribe(channels);
    }
}

const KEYS = {
    platformDataByArchiveId: (basArchiveID: string) => `!pd!${basArchiveID}`,
    buildNumber: () => '!bn!',
} as const;

export { RedisAdapter };
