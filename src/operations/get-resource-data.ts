import { callWithLegacyCallback } from '../calling-style.ts';
import { getValidUserForRole } from '../sane-helpers.js';
import { getSessionIdFromBearerToken } from '../utils.ts';
import type { Response } from 'express';
import type { BASRequest } from '../request-context.ts';

export async function getResourceData(req: BASRequest, res: Response): Promise<void> {
    const branchID = req.query.branchID as string;
    const resourceID = req.query.resourceID as string;
    const basArchiveID = req.query.basArchiveID as string;
    let token = req.query.token as string;

    req.bas.addLoggerInfo({
        branchID,
        resourceID,
        basArchiveID,
    });

    if (!branchID || !resourceID) {
        res.status(400).json({ error: 'missing parameter in request' });
        return;
    }

    if (!token) {
        try {
            token = getSessionIdFromBearerToken(req);
        } catch (err) {
            res.status(401).json({ error: (err as Error).message });
            return;
        }
    }

    const sessionData = await req.bas.acquireSession({ token });
    const dbConnector = sessionData.dbConnector;

    const user = await getValidUserForRole({
        token,
        dbConnector,
        metrics: req.bas.metrics,
        logger: req.bas.logger,
        role: req.bas.roleForAccessTable.getResourceData,
        sessionData,
    });

    const archiveID = user.ARCHIVE_ID;
    if (basArchiveID && basArchiveID !== archiveID) {
        req.bas.addLoggerInfo({ archiveID });
        res.json({ error: "basArchiveID doesn't match session's archiveID" });
        return;
    }

    const archive = await callWithLegacyCallback<{ bar: BalsamiqArchive }>((cb) =>
        req.bas.sessionManager.openBarLocked(sessionData, archiveID, 'READ', cb)
    );

    try {
        const { sha1, data, archiveRevision } = await callWithLegacyCallback<{ sha1: string; data: unknown; archiveRevision: number }>(
            (cb) => archive.bar.getResourceDataWithOptions(resourceID, branchID, { computeSHA1: !!basArchiveID }, cb)
        );

        if (basArchiveID) {
            res.set({
                ETag: `"${sha1}"`,
                'X-Balsamiq-Archive-Revision': archiveRevision,
                'Cache-Control': 'no-cache, private, must-revalidate',
            });
            res.json({ data });
            return;
        }

        res.json({
            data,
            archiveRevision,
        });
    } catch (e) {
        res.json({ error: (e as Error).message });
    }
}
