import { callWithLegacyCallback } from '../calling-style.ts';
import { isPlatformData, type PlatformData } from '../model.ts';
import type { BASRequest } from '../request-context.ts';
import { getValidUserForRole } from '../sane-helpers.ts';
import { getPermalinksDataHelper } from '../utils.ts';
import type { Response } from 'express';

export async function getPermalinksData(req: BASRequest, res: Response): Promise<Response> {
    const token = req.query.token;

    if (!token || typeof token !== 'string') {
        return res.status(401).json({ error: 'wrong credentials' });
    }

    const sessionData = await req.bas.acquireSession({ token });
    const dbConnector = sessionData.dbConnector;

    const user = await getValidUserForRole({
        token,
        dbConnector,
        metrics: req.bas.metrics,
        logger: req.bas.logger,
        role: req.bas.roleForAccessTable.getPermalinksData,
        sessionData,
    });

    const archiveID = user.ARCHIVE_ID;
    req.bas.addLoggerInfo({ archiveID });
    res.setHeader('Cache-Control', 'no-store, no-cache');

    const platformData = await callWithLegacyCallback<PlatformData | {}>((cb) => dbConnector.getPlatformData(archiveID, cb));

    if (!isPlatformData(platformData)) {
        return res.status(400).json({ error: 'archive not found or invalid platform data' });
    }

    const platformSiteID = platformData.PLATFORM_SITE_ID ?? '';
    const platformArchiveID = platformData.PLATFORM_ARCHIVE_ID;
    const platformKind = platformData.PLATFORM_KIND;

    req.bas.addLoggerInfo({
        platformSiteID,
        platformArchiveID,
        platformKind,
    });

    if (!platformArchiveID || !platformKind) {
        return res.status(400).json({ error: 'platform data unavailable for archiveID ' + archiveID });
    }

    const connector = req.bas.getConnector(platformKind);
    if (!connector) {
        return res.status(400).json({ error: 'connector unavailable for platformKind ' + platformKind });
    }

    const permalinks = await getPermalinksDataHelper({
        siteId: platformSiteID,
        projectId: platformArchiveID,
        kind: platformKind,
        dbConnector,
        connector,
    });

    return res.status(200).json(permalinks);
}
