import { callWithLegacyCallback } from '../calling-style.ts';
import { getValidUserForRole } from '../sane-helpers.ts';
import type { Response } from 'express';
import type { BASRequest } from '../request-context.ts';
import type { GetUserListResult } from '../database.ts';
import { getKindFromArchiveID } from '../utils.ts';
import { z } from 'zod';

const getUsersListQuerySchema = z.object({
    token: z.string({
        required_error: 'Token is required',
        invalid_type_error: 'Token must be a string',
    }),
    option: z
        .string({
            required_error: 'Option is required',
            invalid_type_error: 'Option must be a string',
        })
        .optional(),
});

export async function getUsersList(req: BASRequest, res: Response): Promise<void> {
    const queryParamsParseResult = getUsersListQuerySchema.safeParse(req.query);

    if (!queryParamsParseResult.success) {
        res.status(400).json({ error: 'Invalid parameters' });
        return;
    }

    const { token, option } = queryParamsParseResult.data;
    req.bas.addLoggerInfo({ action: 'getUsersList', sessionToken: token });
    res.setHeader('Cache-Control', 'no-cache, no-store');
    let archiveID;
    let optionsFromEditor = null;

    // -- Sanity checks ----------------------------

    const sessionData = await req.bas.acquireSession({ token });
    const dbConnector = sessionData.dbConnector;

    const user = await getValidUserForRole({
        token,
        dbConnector,
        metrics: req.bas.metrics,
        logger: req.bas.logger,
        role: req.bas.roleForAccessTable.getUsersList,
        sessionData,
    });

    archiveID = user.ARCHIVE_ID;
    const userInfo = user.USERINFO ? JSON.parse(user.USERINFO) : null;
    if (userInfo && userInfo.basOptions) {
        optionsFromEditor = userInfo.basOptions;
    }
    req.bas.addLoggerInfo({ archiveID });

    // -- Query RTC server ----------------------------
    const kind = getKindFromArchiveID(archiveID, req.bas.config.archiveIDPrefix);
    let uuids = await req.bas.rtcAdapter.herenow(archiveID, req.bas.logger);

    if (uuids.length === 0) {
        res.json({ users: [] });
        return;
    }

    // -- Build response ----------------------------
    const { users, basSessionsCount, minTimestamp, maxTimestamp } = await callWithLegacyCallback<GetUserListResult>((cb) =>
        dbConnector.getUsersList(uuids, option, cb)
    );

    let now = req.bas.clock.now();
    if (users && users.length) {
        req.bas.metrics.addValue('rtc-users-count-' + kind, users.length, 'Count');
        req.bas.addLoggerInfo({
            sessionsCount: uuids.length,
            usersCount: users.length,
            basSessionsCount: basSessionsCount,
            minTimestamp: minTimestamp,
            maxTimestamp: maxTimestamp,
            minTimestampInSecs: (now - minTimestamp) / 1000,
            maxTimestampInSecs: (now - maxTimestamp) / 1000,
            uuids: uuids,
        });

        // RTC log BW Web editor in background is very noisy, we disable the log for single user session
        if (users.length > 1 && basSessionsCount > 1) {
            req.bas.logger.info('Users Online ' + users.length + ', RTC sessions ' + uuids.length + ', BAS sessions ' + basSessionsCount);
        }
    }
    res.json({ users });
}
