import { callWithLegacyCallback } from '../calling-style.ts';
import Consts from '@balsamiq/bmpr/lib/BalsamiqArchiveConstants.js';
import { makePermalinkObject } from '../utils.ts';
import type { BASRequest } from '../request-context.ts';
import type { Response } from 'express';
import { getValidUserForRole } from '../sane-helpers.ts';
import { isPermalinkData, isPlatformData, type PermalinkData, type PlatformData } from '../model.ts';

export async function getPermalinkData(req: BASRequest, res: Response): Promise<Response> {
    const token = req.query.token;
    const resourceID = req.query.resourceID && typeof req.query.resourceID === 'string' ? req.query.resourceID : null;
    const branchID = req.query.branchID && typeof req.query.branchID === 'string' ? req.query.branchID : null;
    let permalinkID = req.query.permalinkID && typeof req.query.permalinkID === 'string' ? req.query.permalinkID : null;
    const permalinkKind =
        req.query.permalinkKind && typeof req.query.permalinkKind === 'string' ? req.query.permalinkKind : Consts.PermalinkKind.image;

    if (!token || typeof token !== 'string') {
        return res.status(401).json({ error: 'wrong credentials' });
    }

    if ((!resourceID || !branchID) && !permalinkID) {
        return res.status(400).json({ error: 'invalid parameters' });
    }

    req.bas.addLoggerInfo({ resourceID, branchID, permalinkID, permalinkKind });
    res.setHeader('Cache-Control', 'no-store, no-cache');

    const sessionData = await req.bas.acquireSession({ token });
    const dbConnector = sessionData.dbConnector;

    const user = await getValidUserForRole({
        token,
        dbConnector,
        metrics: req.bas.metrics,
        logger: req.bas.logger,
        role: req.bas.roleForAccessTable.getPermalinkData,
        sessionData,
    });

    const archiveID = user.ARCHIVE_ID;
    req.bas.addLoggerInfo({ archiveID });

    const platformData = await callWithLegacyCallback<PlatformData | {}>((cb) => dbConnector.getPlatformData(archiveID, cb));
    if (!isPlatformData(platformData)) {
        return res.status(400).json({ error: 'archive not found or invalid platform data' });
    }

    const platformSiteID = platformData.PLATFORM_SITE_ID ?? '';
    const platformArchiveID = platformData.PLATFORM_ARCHIVE_ID;
    const platformKind = platformData.PLATFORM_KIND;

    req.bas.addLoggerInfo({
        platformSiteID,
        platformArchiveID,
        platformKind,
    });

    if (!platformArchiveID || !platformKind) {
        return res.status(400).json({ error: 'platform data unavailable for archiveID ' + archiveID });
    }

    const connector = req.bas.getConnector(platformKind);
    if (!connector) {
        return res.status(400).json({ error: 'connector unavailable for platformKind ' + platformKind });
    }

    let permalinkData: PermalinkData | {} = {};
    if (resourceID && branchID) {
        permalinkData = await dbConnector.getPermalinkDataFromResourceID({
            platformKind,
            platformSiteID,
            platformArchiveID,
            resourceID,
            branchID,
            permalinkKind,
        });
    } else if (permalinkID) {
        permalinkData = await dbConnector.getPermalinkDataFromPermalinkID({ platformKind, platformSiteID, platformArchiveID, permalinkID });
    }

    if (!isPermalinkData(permalinkData)) {
        return res.status(200).json({ message: 'permalink does not exist' });
    }

    return res.status(200).json(makePermalinkObject(permalinkData, connector));
}
