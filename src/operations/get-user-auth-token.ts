import { getValidUserForRole } from '../sane-helpers.js';
import type { Response } from 'express';
import type { BASRequest } from '../request-context.ts';
import type { Config } from '../configLoader.ts';
import type { JwtKmsService } from '@balsamiq/saas-utils';

const JWT_EXPIRATION_IN_MSECS = 15 * 60 * 1000; // 15 minutes

function getPlatformKindFromArchiveID(config: Config, archiveID: string): string {
    let str = archiveID.replace(config.archiveIDPrefix, '');
    let tmp = str.split('_');
    return tmp[0];
}

async function getTokenFromKMS(jwtKmsAdapter: JwtKmsService, key_arn: string, uniqueUserId: string): Promise<string> {
    // call AWS KMS service
    // Create a JWT token using a KMS key identified by a key_arn
    return jwtKmsAdapter.createSignedJWT(
        key_arn,
        { userId: uniqueUserId, iss: 'bas' },
        { expires: new Date(Date.now() + JWT_EXPIRATION_IN_MSECS) }
    );
}

export async function getUserAuthToken(req: BASRequest, res: Response): Promise<void> {
    let token = req.query.token as string;

    const sessionData = await req.bas.acquireSession({ token });
    const dbConnector = sessionData.dbConnector;

    const user = await getValidUserForRole({
        token,
        dbConnector,
        metrics: req.bas.metrics,
        logger: req.bas.logger,
        role: req.bas.roleForAccessTable.getUserAuthToken,
        sessionData,
    });

    const archiveID = user.ARCHIVE_ID;
    const userId = user.USERNAME;
    const platformKind = getPlatformKindFromArchiveID(req.bas.config, archiveID);
    const connector = req.bas.getConnector(platformKind);
    if (!connector) {
        res.json({ error: 'connector unavailable for platformKind ' + platformKind });
        return;
    }
    if (!connector.getUniqueUserId) {
        res.json({ error: 'getUniqueUserId not implemented for platformKind ' + platformKind });
        return;
    }

    const uniqueUserId = connector.getUniqueUserId(user);

    req.bas.addLoggerInfo({ archiveID, platformKind, userId });

    // call KMS
    const userAuthToken = await getTokenFromKMS(req.bas.jwtKmsAdapter, req.bas.config.kmsService.key_arn, uniqueUserId);

    req.bas.logger.info(`Get User Auth Token ${userAuthToken}`);

    res.json({
        userAuthToken: userAuthToken,
        services: {
            i2w: {
                url: req.bas.config.i2wService.url,
            },
        },
    });
}
