import busboy from 'busboy';
import fs from 'fs';
import { callWithLegacyCallback } from '../calling-style.ts';
import Consts from '@balsamiq/bmpr/lib/BalsamiqArchiveConstants.js';
import { makePermalinkObject } from '../utils.ts';
import type { BASRequest } from '../request-context.ts';
import type { Response } from 'express';
import { getValidUserForRole } from '../sane-helpers.ts';
import { assertPermalinkInfo, isPermalinkData, isPlatformData, type PermalinkInfo, type PlatformData } from '../model.ts';
import { Readable } from 'stream';
import path from 'path';

const __dirname = new URL('.', import.meta.url).pathname;
const tryLaterHtmlPage = fs.readFileSync(path.join(__dirname, '../permalink_try_later.html'));

export async function createOrUpdateImageLink(req: BASRequest, res: Response): Promise<Response> {
    const token = req.query.token;

    const { attachment, params } = await parseFormData(req);
    const resourceID = params?.resourceID;
    const branchID = params?.branchID;

    let permalinkInfo: PermalinkInfo = {};
    try {
        permalinkInfo = params?.permalinkInfo ? JSON.parse(params.permalinkInfo) : {};
        assertPermalinkInfo(permalinkInfo);
    } catch (err) {
        permalinkInfo = {};
    }

    if (!token || typeof token !== 'string' || !resourceID || !branchID) {
        return res.status(400).json({ error: 'wrong credentials or invalid parameters', token, resourceID, branchID });
    }

    if (attachment !== null && attachment.mimeType !== 'image/png') {
        return res.status(400).json({ error: `unsupported image mimeType: ${attachment.mimeType}` });
    }

    req.bas.addLoggerInfo({ resourceID, branchID });

    const sessionData = await req.bas.acquireSession({ token });
    const dbConnector = sessionData.dbConnector;

    const user = await getValidUserForRole({
        token,
        dbConnector,
        metrics: req.bas.metrics,
        logger: req.bas.logger,
        role: req.bas.roleForAccessTable.createOrUpdateImageLink,
        sessionData,
    });
    const internalUserID = user.INTERNAL_ID;
    const username = user.USERNAME;
    const archiveID = user.ARCHIVE_ID;

    req.bas.addLoggerInfo({ archiveID });

    const platformData = await callWithLegacyCallback<PlatformData | {}>((cb) => dbConnector.getPlatformData(archiveID, cb));

    if (!isPlatformData(platformData)) {
        return res.status(400).json({ error: 'archive not found or invalid platform data' });
    }

    const platformSiteID = platformData.PLATFORM_SITE_ID ?? '';
    const platformArchiveID = platformData.PLATFORM_ARCHIVE_ID;
    const platformKind = platformData.PLATFORM_KIND;

    let platformInfo;
    try {
        platformInfo = platformData.PLATFORM_INFO ? JSON.parse(platformData.PLATFORM_INFO) : {};
    } catch (e) {
        platformInfo = {};
    }

    req.bas.addLoggerInfo({
        platformArchiveID,
        platformSiteID,
        platformKind,
    });

    if (!platformArchiveID || !platformKind) {
        return res.status(400).json({ error: 'platform data unavailable for archiveID ' + archiveID });
    }

    const connector = req.bas.getConnector(platformKind);
    if (!connector?.makePermalinkID) {
        return res.status(400).json({ error: 'platform does not support permalink' });
    }

    // no need to add to the message platformArchiveID, platformSiteID and platformKind because already included with addLoggerInfo
    req.bas.logger.info('start createOrUpdateImageLink');

    // Prepare a new permalinkID to use in case of insert or use the one passed in the request
    let tempPermalinkID = permalinkInfo.permalinkUUID
        ? permalinkInfo.permalinkUUID
        : connector.makePermalinkID({ platformKind, platformSiteID, platformArchiveID, resourceID, branchID });

    // As of now only Cloud supports data residency, so we are saving ourselves the trouble of calling the database or the connector unnecessarily:
    if (connector.getDataResidency) {
        let existingPermalink = await dbConnector.getPermalinkDataFromResourceID({
            platformKind,
            platformSiteID,
            platformArchiveID,
            resourceID,
            branchID,
        });

        if (isPermalinkData(existingPermalink)) {
            // Data residency of existing permalinks should never change:
            permalinkInfo.dataResidency = existingPermalink.permalinkInfo.dataResidency;
        } else {
            try {
                // Ask the data residency to the authority which is the connector:
                permalinkInfo.dataResidency = await connector.getDataResidency(req.bas.logger, platformSiteID, platformArchiveID);
            } catch (err) {
                const error = err as Error;
                req.bas.logger.error(`Unexpected error in connector.getDataResidency: ${error.message}`, error);
                // Note: This is a comment that underlines the fact that if anything goes wrong with the call to the connector above then we fall back to leaving the data residency not explicit set by not doing anything here.
            }
        }
    }

    const [permalinkData, created] = await dbConnector.insertOrUpdatePermalink({
        permalinkID: tempPermalinkID,
        platformKind,
        platformSiteID,
        platformArchiveID,
        resourceID,
        branchID,
        dirty: false,
        permalinkKind: Consts.PermalinkKind.image,
        permalinkInfo,
        platformInfo,
        timestamp: req.bas.clock.now(),
    });

    await req.bas.releaseSession();

    req.bas.addLoggerInfo({
        permalinkID: permalinkData.permalinkID,
    });

    // TODO: maybe in the future the attachment will be always null and this code will be removed
    if (attachment !== null) {
        // attachment format is "png" by design
        await connector.storePermalinkImage({
            dataStream: attachment.file,
            mimeType: attachment.mimeType,
            permalinkID: permalinkData.permalinkID,
        });

        // TODO: lazy image generation for comparison, in the future we'll remove it
        connector.generatePermalinkImage({ logger: req.bas.logger, permalinkData, suffix: '_w2i' }).catch((err) => {
            req.bas.logger.error(`Unexpected error in connector.generatePermalinkImage (attachment): ${err.message}`, err);
        });
    } else {
        // upload a dummy image
        if (created === 1) {
            await connector.storePermalinkImage({
                dataStream: Readable.from(tryLaterHtmlPage),
                mimeType: 'text/html',
                permalinkID: permalinkData.permalinkID,
                permalinkInfo: permalinkData.permalinkInfo,
            });
        }

        //lazy image generation
        connector.generatePermalinkImage({ logger: req.bas.logger, permalinkData }).catch((err) => {
            req.bas.logger.error(`Unexpected error in connector.generatePermalinkImage (no attachment): ${err.message}`, err);
        });
    }

    req.bas.logger.info(`Permalink ${permalinkData.permalinkID} ${created === 1 ? 'Inserted' : 'Updated'}`);

    req.bas.broadcastRTCMessage(archiveID, {
        author: internalUserID,
        username,
        operation: req.bas.actionName,
        resourceID,
        branchID,

        platformKind: platformKind,
        platformArchiveID: platformArchiveID,
        platformSiteID: platformSiteID,
        permalinkUUID: permalinkData.permalinkID,
    });

    return res.status(200).json(makePermalinkObject(permalinkData, connector));
}

function parseFormData(req: BASRequest): Promise<{
    params: Record<string, string> | null;
    attachment: { fieldname: string; file: Readable; filename: string; encoding: string; mimeType: string } | null;
}> {
    if (!req.is('multipart/form-data')) {
        return Promise.resolve({ params: null, attachment: null }); // No formdata to parse
    }

    const fieldLimits = {
        // Block some kind of malicious requests
        fileSize: 20 * 1024 * 1024,
        fields: 10,
        parts: 10,
        files: 1,
    };
    const bb = busboy({ headers: req.headers, limits: fieldLimits });
    return new Promise((resolve, reject) => {
        let params: Record<string, string> = {};
        let attachment: { fieldname: string; file: Readable; filename: string; encoding: string; mimeType: string } | null = null;
        bb.on('field', function (fieldname, val) {
            params[fieldname] = val;
        });

        bb.on('file', function (fieldname, file, info) {
            const { filename, encoding, mimeType } = info;
            attachment = { fieldname, file, filename, encoding, mimeType };
            resolve({ params, attachment });
        });

        bb.on('error', reject);
        bb.on('finish', () => resolve({ params, attachment }));

        req.pipe(bb).on('error', reject);
    });
}
