import type { BASRequest } from '../request-context.ts';
import type { Response } from 'express';
import Consts from '@balsamiq/bmpr/lib/BalsamiqArchiveConstants.js';
import { makePermalinkObject } from '../utils.ts';
import { callWithLegacyCallback, isBASCallbackError } from '../calling-style.ts';
import {
    assertPermalinkInfo,
    isPermalinkData,
    isPlatformData,
    parsePlatformInfo,
    type PermalinkData,
    type PermalinkInfo,
    type PlatformData,
} from '../model.ts';
import { getValidUserForRole } from '../sane-helpers.ts';

export async function setPermalink(req: BASRequest, res: Response): Promise<void> {
    if (req.query.token) {
        await setPermalinkAsUser(req, res);
    } else {
        // If no token assume it's an admin request
        await setPermalinkAsBASAdmin(req, res);
    }
}

const allowedPermalinkKinds = new Set<string>([Consts.PermalinkKind.image, Consts.PermalinkKind.public_share]);
function parseRequestBody(req: BASRequest): {
    token: string | null;
    resourceID: string;
    branchID: string;
    permalinkKind: string;
    check: boolean;
    permalinkInfo: PermalinkInfo;
    permalinkID: string | null;
} {
    const token = typeof req.query.token === 'string' ? req.query.token : null;
    const body = req.body;
    let permalinkInfo: PermalinkInfo;
    try {
        permalinkInfo = JSON.parse(body.permalinkInfo);
        assertPermalinkInfo(permalinkInfo);
    } catch (e) {
        permalinkInfo = {};
    }
    const permalinkID: string | null = permalinkInfo.permalinkUUID ?? null;
    return {
        token,
        resourceID: body.resourceID,
        branchID: body.branchID,
        permalinkKind: body.permalinkKind || Consts.PermalinkKind.image,
        check: body.check,
        permalinkInfo,
        permalinkID,
    };
}

async function setPermalinkAsUser(req: BASRequest, res: Response) {
    const { token, resourceID, branchID, permalinkKind, check, permalinkInfo, permalinkID } = parseRequestBody(req);
    req.bas.addLoggerInfo({ resourceID, branchID, permalinkKind });

    // empty resourceID and branchID allowed only for public_share kind
    if (
        !token ||
        !allowedPermalinkKinds.has(permalinkKind) ||
        (permalinkKind === Consts.PermalinkKind.image && (!resourceID || !branchID))
    ) {
        res.status(400).json({ error: 'wrong parameters' });
        return;
    }

    const sessionData = await req.bas.acquireSession({ token });
    const dbConnector = sessionData.dbConnector;

    const user = await getValidUserForRole({
        token,
        dbConnector,
        metrics: req.bas.metrics,
        logger: req.bas.logger,
        role: req.bas.roleForAccessTable.setPermalinkData,
        sessionData,
    });

    const archiveID = user.ARCHIVE_ID;
    const internalUserID = user.INTERNAL_ID;
    const username = user.USERNAME;

    req.bas.addLoggerInfo({ archiveID });

    const platformData = await callWithLegacyCallback<PlatformData | {}>((cb) => dbConnector.getPlatformData(archiveID, cb));
    if (!isPlatformData(platformData)) {
        res.status(400).json({ error: 'archive not found or invalid platform data' });
        return;
    }

    const platformSiteID = platformData.PLATFORM_SITE_ID || '';
    const platformArchiveID = platformData.PLATFORM_ARCHIVE_ID;
    const platformKind = platformData.PLATFORM_KIND;
    const platformInfo = parsePlatformInfo(platformData.PLATFORM_INFO);

    req.bas.addLoggerInfo({
        platformArchiveID,
        platformSiteID,
        platformKind,
    });

    if (!platformArchiveID || !platformKind) {
        return res.status(400).json({ error: 'platform data unavailable for archiveID ' + archiveID });
    }

    const connector = req.bas.getConnector(platformKind);
    if (!connector) {
        return res.status(400).json({ error: 'connector unavailable for platformKind ' + platformKind });
    }

    if (check) {
        const permalink = await dbConnector.getPermalinkDataFromResourceID({
            platformKind,
            platformSiteID,
            platformArchiveID,
            resourceID,
            branchID,
            permalinkKind,
        });

        if (isPermalinkData(permalink)) {
            return res.status(200).json(makePermalinkObject(permalink, connector));
        } else {
            return res.status(200).json({ message: 'permalink not existing' });
        }
    }

    let newPermalinkID;
    if (!permalinkID) {
        let existingPermalink = await dbConnector.getPermalinkDataFromResourceID({
            platformKind,
            platformSiteID,
            platformArchiveID,
            resourceID,
            branchID,
            permalinkKind,
        }); // Check if permalink for specified resource already exists

        if (isPermalinkData(existingPermalink)) {
            // Permalink exists
            req.bas.logger.info('Using existing permalink with code ' + existingPermalink.permalinkID);
            return res.status(200).json(makePermalinkObject(existingPermalink, connector));
        }

        // generate uuid for new permalink
        newPermalinkID = connector.makePermalinkID({ platformKind, platformSiteID, platformArchiveID, resourceID, branchID });
    } else {
        newPermalinkID = permalinkID;
    }

    req.bas.logger.info('Setting a permalink with code ' + newPermalinkID);

    const permalinkData: PermalinkData = {
        permalinkID: newPermalinkID,
        platformKind,
        platformSiteID,
        platformArchiveID,
        resourceID,
        branchID,
        dirty: false,
        permalinkKind,
        permalinkInfo,
        platformInfo,
        timestamp: req.bas.clock.now(),
    };
    try {
        await callWithLegacyCallback<{}>((cb) => dbConnector.savePermalinkData(permalinkData, cb));
    } catch (err) {
        if (isBASCallbackError(err) && err.resultObj?.busy) {
            req.bas.logger.info(`Duplicated permalinkID ${newPermalinkID}`);
        } else {
            throw err;
        }
    }
    req.bas.logger.info('Permalink created');

    let permalinkResponse = makePermalinkObject(permalinkData, connector);

    if (permalinkKind === Consts.PermalinkKind.public_share) {
        // nothing to do, for the moment we do not send RTC message
    } else {
        // "image"
        req.bas.broadcastRTCMessage(archiveID, {
            author: internalUserID,
            username,
            operation: req.bas.actionName,
            branchID,
            resourceID,

            platformKind: platformKind,
            platformArchiveID: platformArchiveID,
            platformSiteID: platformSiteID,
            permalinkUUID: newPermalinkID,
        });
    }
    res.status(200).json(permalinkResponse);
}

async function setPermalinkAsBASAdmin(req: BASRequest, res: Response) {
    if (!(await req.bas.verifyAdminCredentials())) {
        return res.status(403).json({ error: 'wrong credentials' });
    }
    const { resourceID, branchID, permalinkKind, permalinkInfo } = parseRequestBody(req);

    // Trust the platforminfo that's coming from the request
    const body = req.body;
    const platformSiteID = body.platformSiteID;
    const platformArchiveID = body.platformArchiveID;
    const platformKind = body.platformKind;

    let platformInfo;
    try {
        platformInfo = JSON.parse(body.platformInfo);
    } catch (e) {
        platformInfo = {};
    }

    req.bas.addLoggerInfo({
        platformArchiveID,
        platformSiteID,
        platformKind,
        platformInfo,
    });

    if (!platformArchiveID || !platformSiteID || !platformKind) {
        return res.status(400).json({ error: 'wrong parameters' });
    }

    const connector = req.bas.getConnector(platformKind);
    if (!connector) {
        return res.status(400).json({ error: 'connector unavailable for platformKind ' + platformKind });
    }

    const permalinkID = connector.makePermalinkID({ platformKind, platformSiteID, platformArchiveID, resourceID, branchID });
    const newPermalinkData = {
        permalinkID,
        platformKind,
        platformSiteID,
        platformArchiveID,
        resourceID,
        branchID,
        dirty: false,
        permalinkKind,
        permalinkInfo,
        platformInfo,
        timestamp: req.bas.clock.now(),
    };

    await req.bas.withSession(async (sessionData) => {
        const dbConnector = sessionData.dbConnector;
        await callWithLegacyCallback((cb) => dbConnector.savePermalinkData(newPermalinkData, cb));
    });

    req.bas.logger.info('Permalink created');

    return res.status(200).json(makePermalinkObject(newPermalinkData, connector));
}
