import assert from 'assert';
import type { DataResidencyName } from '../environment-variables-schemas.ts';
import type { BASRequest } from '../request-context.ts';
import { pipeStreamToResponse } from '../utils.ts';
import type { Response } from 'express';

const bucketDir = 'slack';
// This function is here only for local development because on AWS we have the Cloudfront lambda@edge
export async function getSnapshot(req: BASRequest, res: Response): Promise<void> {
    const UUID = req.params.id;

    if (!UUID) {
        res.status(400).json({ error: 'invalid parameters' });
        return;
    }

    req.bas.addLoggerInfo({ UUID, bucketDir });
    res.setHeader('Cache-Control', 'no-cache, no-store');

    req.bas.logger.info('Serving snapshot image');

    try {
        await getSnapshotHelper({
            req,
            res,
            UUID,
            bucketDir,
            dataResidency: req.bas.config.defaultDataResidencyName,
        });
        return; //we're done
    } catch (err) {
        const error = err as Error;
        req.bas.logger.error(`Unexpected error: ${error.message}`, error);
    }

    if (res.headersSent) {
        // Failed while streaming the data. Cannot fix this anymore.
        res.end();
    } else {
        res.status(404).send('Not found');
    }
}

async function getSnapshotHelper({
    UUID,
    bucketDir,
    dataResidency,
    res,
    req,
}: {
    UUID: string;
    bucketDir: string;
    dataResidency: DataResidencyName;
    res: Response;
    req: BASRequest;
}): Promise<void> {
    const _pipeStream = async () => {
        const stream = await req.bas.permalinkImageStorageAdapter.getSnapshotImageStream({ bucketDir, UUID, dataResidency });
        assert(stream !== undefined, 'Failed to get snapshot image stream');
        return await pipeStreamToResponse(stream, res, [['Content-Type', 'image/png']]);
    };
    try {
        await _pipeStream();
    } catch (err) {
        // error action handled by the caller
        throw err;
    }
}
