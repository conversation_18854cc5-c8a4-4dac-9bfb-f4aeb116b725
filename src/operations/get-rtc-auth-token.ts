import type { BASRequest } from '../request-context.ts';
import { getValidUserForRole as sane_getValidUserForRole } from '../sane-helpers.ts';
import con from '../constants.ts';
import type { User } from '../database.ts';
import { getSessionIdFromBearerToken } from '../utils.ts';
import type { Response } from 'express';

export async function getRtcAuthToken(req: BASRequest, res: Response) {
    let token;
    try {
        token = getSessionIdFromBearerToken(req);
    } catch (err) {
        return { error: 'Error fetching the session token' };
    }

    const rateLimiterGroup = req.bas.rateLimiterConfiguration.getRateLimitersGroup(
        ['getRtcAuthToken'],
        { sessionToken: token },
        req.bas.logger
    );
    const rateLimiterStatus = await rateLimiterGroup.checkExceeded();
    if (rateLimiterStatus.hasExceeded) {
        res.status(429).json({
            error: rateLimiterStatus.publicInfo?.publicErrorMessage,
        });
        return;
    }

    const response = await req.bas.sessionManager.withSession(req.bas.logger, '/' + con.API_GET_RTC_AUTH_TOKEN, async (dbSession) => {
        let user: User;
        try {
            user = await sane_getValidUserForRole({
                token,
                dbConnector: dbSession.dbConnector,
                metrics: req.bas.metrics,
                logger: req.bas.logger,
                role: con.ROLE_VIEWER,
                sessionData: dbSession,
            });
        } catch (error) {
            const err = error as Error & { params?: { busy?: boolean } };
            if (err.params && err.params.busy) {
                return {
                    body: { error: 'Not authenticated' },
                    statusCode: 403,
                };
            } else {
                throw err;
            }
        }

        // Reducing daily almost 500K lines in the logs
        // It is also the only API where we log not anonymized PII (email, full name)
        // req.bas.logger.info('getRtcAuthToken', { action: 'getRtcAuthToken', ...req.bas.getUserSessionInfo(userSession) });

        const {
            jwtCallbackInfo: { basBaseURL, secret },
        } = req.bas.config.getRtcConfig();
        const { heartbeatIntervalInSec } = await req.bas.rtcAdapter.getFrontendConfig(req.bas.logger);
        const jwtExtraFields = {
            sessionExpirationCallback: {
                url: basBaseURL + con.API_RTC_USERS_LEFT_ARCHIVE,
                secret,
            },
            heartbeatDurationInSec: heartbeatIntervalInSec,
        };

        // Reducing daily almost 500K lines in the logs
        // req.bas.logger.info('Calling new Lambda-RTC buildRTCTokenInfo');
        const tokenInfo = await req.bas.rtcAdapter.buildRTCTokenInfo(user.ARCHIVE_ID, req.bas.logger, jwtExtraFields);
        return {
            body: tokenInfo,
            statusCode: 200,
        };
    });
    req.bas.sendStatusCodeJsonResponse(response.statusCode, response.body);
}
