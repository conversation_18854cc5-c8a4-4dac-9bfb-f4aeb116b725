import { callWithLegacyCallback, isBASLegacyErrorObject, type BASLegacyCallback } from '../calling-style.ts';
import { updatePermalinkImages } from '../permalinks.js';
import con from '../constants.ts';
import type { BASRequest } from '../request-context.ts';
import type { Response } from 'express';
import type { DBConnector, User } from '../database.ts';
import { isPlatformData, type PlatformData } from '../model.ts';

export async function usersLeftArchive(req: BASRequest, res: Response): Promise<void> {
    if (!(await req.bas.verifyAdminCredentials())) {
        res.status(403).json({ error: 'wrong credentials' });
        return;
    }

    const rateLimiterGroup = req.bas.rateLimiterConfiguration.getRateLimitersGroup(
        ['usersLeftArchive'],
        { application: 'bas' },
        req.bas.logger
    );
    const rateLimiterStatus = await rateLimiterGroup.checkExceeded();
    if (rateLimiterStatus.hasExceeded) {
        res.status(429).json({
            error: rateLimiterStatus.publicInfo?.publicErrorMessage ?? 'Too many requests',
        });
        return;
    }

    const jsonObj = req.body;
    const channels = jsonObj.channels;
    if (!Array.isArray(channels) || channels.length === 0) {
        res.status(400).json({ error: 'channels must be a non-empty array' });
        return;
    }

    const sessionData = await req.bas.acquireSession();

    res.status(200).json({
        resp: await Promise.all(channels.map((channel) => _updatePermalinkImage(req, sessionData.dbConnector, channel))),
    });
}

async function _updatePermalinkImage(req: BASRequest, dbConnector: DBConnector, channel: { id: string; userIds: string[] }) {
    const archiveID = channel.id;
    let userIds = channel.userIds;

    req.bas.addLoggerInfo({ channelID: archiveID });

    let platformData: PlatformData | {};
    try {
        platformData = await callWithLegacyCallback<{} | PlatformData>((cb) => dbConnector.getPlatformData(archiveID, cb));
    } catch (error) {
        return { error: 'no platformArchiveID found for channelID: ' + archiveID };
    }

    if (!isPlatformData(platformData)) {
        return { error: 'no platformArchiveID found for channelID: ' + archiveID };
    }

    let usersForArchive;
    try {
        usersForArchive = await callWithLegacyCallback<{ users: User[] }>((callback) =>
            dbConnector.getUsersForArchive(archiveID, callback)
        );
    } catch (err) {
        const error = err as Error;
        return { error: error.message };
    }

    const atLeastOneUserIsEditor =
        userIds.findIndex((userId) => {
            return (
                usersForArchive.users.findIndex((userForArchive) => {
                    return userId === userForArchive.INTERNAL_ID && Number.parseInt(userForArchive.PERMISSIONS) >= con.ROLE_EDITOR;
                }) !== -1
            );
        }) !== -1;

    let resp;
    if (atLeastOneUserIsEditor) {
        req.bas.addLoggerInfo({ platformData });
        // req.bas.logger.info("updatePermalinkImages");
        resp = await updatePermalinkImages({
            broadcastRTCMessage: req.bas.broadcastRTCMessage,
            dbConnector,
            archiveID,
            userName: 'none',
            getConnector: req.bas.getConnector,
            platformArchiveID: platformData.BAS_ARCHIVE_ID,
            platformKind: platformData.PLATFORM_KIND,
            platformSiteID: platformData.PLATFORM_SITE_ID,
            timestamp: req.bas.clock.now(),
            logger: req.bas.logger,
        });
    } else {
        resp = { msg: 'no editor found' };
    }

    return resp;
}
