import { callWithLegacyCallback, isBASCallbackError } from '../calling-style.ts';
import type { BASRequest } from '../request-context.ts';
import type { Response } from 'express';

export async function getPublicShare(req: BASRequest, res: Response): Promise<void> {
    // https://share.balsamiq.com/ps/52s8pKS9KdtDCyWUJBkB3Q?f=N4IgUiBcCMA0IDkpxAYWfAMhkAhHAsjgFo4DSUA2gLoC%2BQA%3D

    const staticFileRepositoryUrl = req.bas.staticFileRepository.legacyGetStaticFileRepository();
    if (staticFileRepositoryUrl === null) {
        req.bas.logger.error('Unexpected error: BAS misconfiguration: missing config.proxyConfig for public share');
        res.status(500).send('Unexpected error: wrong configuration');
        return;
    }

    const permalinkID = req.params.id;
    req.bas.addLoggerInfo({ permalinkID: permalinkID });

    try {
        // if permalinkId is not specified the API has been already failed before
        await req.bas.withSession(async (sessionData) => {
            await callWithLegacyCallback((cb) => sessionData.dbConnector.getPermalinkData(permalinkID, cb));
        });
    } catch (err) {
        // not a valid permalink ID
        if (isBASCallbackError(err) && err.resultObj?.notFound) {
            req.bas.logger.info('Public Share link not found');
            const url = staticFileRepositoryUrl + 'share/404.html';
            res.setHeader('Cache-Control', 'no-cache, no-store');
            try {
                const response = await req.bas.staticFileRepository.fetch(url);
                res.status(404).send(response.text);
            } catch (error) {
                req.bas.logger.error('Error fetching 404 page', error as Error);
                res.status(500).send('Unexpected error: unable to fetch 404 page');
            }
            return;
        } else {
            req.bas.logger.error('Public Share link not found: Unexpected error ', err as Error);
            throw err;
        }
    }

    const url = staticFileRepositoryUrl + 'share/public_share.html';
    req.bas.logger.info('Getting Public Share link ' + url);
    res.setHeader('Cache-Control', 'no-cache, no-store');
    try {
        const response = await req.bas.staticFileRepository.fetch(url);
        res.send(response.text);
    } catch (err) {
        req.bas.logger.error('Error fetching public share page', err as Error);
        res.status(500).send('Unexpected error: unable to fetch public share page');
    }
}
