import { callWithLegacyCallback, isBASCallbackError } from '../calling-style.ts';
import Consts from '@balsamiq/bmpr/lib/BalsamiqArchiveConstants.js';
import type { BASRequest } from '../request-context.ts';
import type { Response } from 'express';
import { getValidUserForRole } from '../sane-helpers.ts';
import { isPermalinkData, isPlatformData, type PermalinkData, type PlatformData } from '../model.ts';

async function deletePermalinkData(req: BASRequest, res: Response): Promise<Response> {
    const jsonObj = req.body;
    const token = req.query.token;

    let resourceID = jsonObj.resourceID;
    let branchID = jsonObj.branchID;
    let permalinkID = jsonObj.permalinkID;
    const permalinkKind = jsonObj.permalinkKind || Consts.PermalinkKind.image;

    if (!token || typeof token !== 'string') {
        return res.status(401).json({ error: 'wrong credentials' });
    }

    if ((!resourceID || !branchID) && !permalinkID) {
        return res.status(400).json({ error: 'invalid parameters' });
    }

    req.bas.addLoggerInfo({ resourceID, branchID, permalinkID, permalinkKind });

    const sessionData = await req.bas.acquireSession({ token });
    const dbConnector = sessionData.dbConnector;

    const user = await getValidUserForRole({
        token,
        dbConnector,
        metrics: req.bas.metrics,
        logger: req.bas.logger,
        role: req.bas.roleForAccessTable.deletePermalinkData,
        sessionData,
    });
    const archiveID = user.ARCHIVE_ID;
    const internalUserID = user.INTERNAL_ID;
    const username = user.USERNAME;

    req.bas.addLoggerInfo({ archiveID });

    let platformData = await callWithLegacyCallback<PlatformData | {}>((cb) => dbConnector.getPlatformData(archiveID, cb));
    if (!isPlatformData(platformData)) {
        return res.status(400).json({ error: 'archive not found or invalid platform data' });
    }

    const platformSiteID = platformData.PLATFORM_SITE_ID ?? '';
    const platformArchiveID = platformData.PLATFORM_ARCHIVE_ID;
    const platformKind = platformData.PLATFORM_KIND;

    req.bas.addLoggerInfo({
        platformArchiveID,
        platformSiteID,
        platformKind,
    });

    if (!platformArchiveID || !platformKind) {
        return res.status(400).json({ error: 'platform data unavailable for archiveID ' + archiveID });
    }

    const connector = req.bas.getConnector(platformKind);
    if (!connector) {
        return res.status(400).json({ error: 'connector unavailable for platformKind ' + platformKind });
    }

    let permalinkData: PermalinkData | {};
    if (resourceID && branchID) {
        permalinkData = await dbConnector.getPermalinkDataFromResourceID({
            platformKind,
            platformSiteID,
            platformArchiveID,
            resourceID,
            branchID,
            permalinkKind,
        });
    } else {
        permalinkData = await dbConnector.getPermalinkDataFromPermalinkID({ platformKind, platformSiteID, platformArchiveID, permalinkID });
    }

    if (!isPermalinkData(permalinkData)) {
        return res.status(200).json({ warning: 'no permalink found for the requested resource' });
    }

    // fill in missing resourceID and branchID info in case we got the permalink from permalinkID
    resourceID = resourceID || permalinkData.resourceID;
    branchID = branchID || permalinkData.branchID;

    if (
        permalinkData.permalinkKind === Consts.PermalinkKind.image ||
        permalinkData.permalinkKind === Consts.PermalinkKind.image_unfurling
    ) {
        await connector.deletePermalinkImages({
            permalinkIDs: [permalinkData.permalinkID],
            format: permalinkData.permalinkInfo?.image?.format || 'png',
            dataResidency: permalinkData.permalinkInfo?.dataResidency || req.bas.config.defaultDataResidencyName,
        });
        await connector.deletePermalinkThumbnailImages({
            permalinkIDs: [permalinkData.permalinkID],
            format: permalinkData.permalinkInfo?.image?.format || 'png',
            dataResidency: permalinkData.permalinkInfo?.dataResidency || req.bas.config.defaultDataResidencyName,
        });
    }

    try {
        await dbConnector.deletePermalinkData({
            platformKind,
            platformSiteID,
            platformArchiveID,
            resourceID,
            branchID,
            permalinkKind: permalinkData.permalinkKind,
        });

        req.bas.broadcastRTCMessage(archiveID, {
            author: internalUserID,
            username,
            operation: req.bas.actionName,
            resourceID,
            branchID,

            platformKind: platformKind,
            platformArchiveID: platformArchiveID,
            platformSiteID: platformSiteID,
            permalinkUUID: permalinkData.permalinkID,
        });
    } catch (err) {
        if (isBASCallbackError(err) && err.resultObj?.notFound) {
            return res.status(200).json({ warning: 'no permalink found for the requested resource' });
        }
        throw err;
    }
    return res.status(200).json({ message: 'permalink deleted' });
}

export { deletePermalinkData };
