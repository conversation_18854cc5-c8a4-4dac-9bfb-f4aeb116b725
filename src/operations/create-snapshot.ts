import shortUUID from 'short-uuid';
import { base64ToStream } from '../utils.ts';
import { DATA_RESIDENCIES } from '../environment-variables-schemas.ts';
import { getResourceNameAndIDs } from '../utils.ts';
import type { BASRequest } from '../request-context.ts';
import type { Response } from 'express';

export async function createSnapshot(req: BASRequest, res: Response): Promise<void> {
    if (!(await req.bas.verifyAdminCredentials())) {
        res.status(403).json({ error: 'wrong credentials' });
        return;
    }

    const jsonObj = req.body;
    let resourceID = jsonObj.resourceID;
    let branchID = jsonObj.branchID;
    const platformSiteID = jsonObj.platformSiteID;
    const platformArchiveID = jsonObj.platformArchiveID;
    const platformKind = jsonObj.platformKind;
    const userID = jsonObj.platformInfo.userID;
    const bucketDir = jsonObj.platformInfo.bucketDir;
    const format = jsonObj?.platformInfo?.image?.format ? jsonObj.platformInfo.image.format : 'png';
    const quality = jsonObj?.platformInfo?.image?.quality ? jsonObj.platformInfo.image.quality : 1;
    const transparentBackground = jsonObj?.platformInfo?.image?.transparentBackground
        ? jsonObj.platformInfo.image.transparentBackground
        : false;
    const dataResidency = jsonObj?.platformInfo?.dataResidency
        ? jsonObj.platformInfo.dataResidency
        : req.bas.config.defaultDataResidencyName;

    req.bas.addLoggerInfo({
        platformArchiveID,
        platformSiteID,
        platformKind,
        platformInfo: jsonObj.platformInfo,
    });

    if (!platformArchiveID || !platformSiteID || !platformKind || !userID || !bucketDir) {
        res.status(400).json({ error: 'wrong parameters' });
        return;
    }

    if (!DATA_RESIDENCIES.includes(dataResidency)) {
        res.status(400).json({ error: 'Invalid data residency' });
        return;
    }

    const connector = req.bas.getConnector(platformKind);
    if (!connector) {
        res.status(400).json({ error: 'connector unavailable for platformKind ' + platformKind });
        return;
    }

    const sessionData = await req.bas.acquireSession();
    const dbConnector = sessionData.dbConnector;

    req.bas.logger.info(`Getting getResourceNameAndIDs for ${branchID}-${resourceID}`);

    let resourceInfo;
    try {
        ({ resourceInfo } = await getResourceNameAndIDs({
            config: req.bas.config,
            sessionManager: req.bas.sessionManager,
            dbConnector,
            connector,
            sessionData,
            platformKind,
            platformSiteID,
            platformArchiveID,
            platformInfo: jsonObj.platformInfo,
            resourceID,
            branchID,
            logger: req.bas.logger,
        }));
    } catch (err) {
        resourceInfo = null;
    } finally {
        if (!(resourceInfo && resourceInfo.resourceID && resourceInfo.branchID)) {
            res.status(404).json({ error: 'No resource found', notFound: true });
            return;
        }
        resourceID = resourceInfo.resourceID;
        branchID = resourceInfo.branchID;

        req.bas.addLoggerInfo({
            resourceID,
            branchID,
        });
    }

    const UUID = shortUUID.generate();
    req.bas.logger.info('Creating snapshot...');
    try {
        await connector.generateSnapshot({
            logger: req.bas.logger,
            platformKind,
            platformSiteID,
            platformArchiveID,
            resourceID,
            branchID,
            platformInfo: {
                userID: userID,
                bucketKey: `snapshots/${bucketDir}/${UUID}.${format}`,
            },
            permalinkInfo: {
                image: {
                    format: format,
                    quality: quality,
                    transparentBackground: transparentBackground,
                },
            },
            dataResidency,
        });
    } catch (err) {
        const error = err as Error;
        req.bas.logger.error(`Unexpected error in connector.generateSnapshot: ${error.message}`, error);

        //fallback to thumbnail
        if (resourceInfo.thumbnail) {
            // thumbnail format is "png" by design
            req.bas.logger.info('fallback to thumbnail: calling storePermalinkImage');
            const { stream, length } = base64ToStream(resourceInfo.thumbnail);
            await req.bas.permalinkImageStorageAdapter.uploadSnapshotImage({
                dataStream: stream,
                mimeType: 'image/png',
                bucketDir: bucketDir,
                UUID: UUID,
                metadata: {
                    platformArchiveID: platformArchiveID,
                    platformSiteID: platformSiteID,
                    platformKind: platformKind,
                    userID: userID,
                    branchID: branchID,
                    resourceID: resourceID,
                },
                format: 'png',
                dataResidency,
            });
        }
    }

    req.bas.logger.info('snapshot created');

    res.status(200).json({
        projectName: resourceInfo.projectName,
        name: resourceInfo.name,
        image: connector.getSnapshotUrl(bucketDir, UUID, format, dataResidency),
    });
}
