import { BASApiClient } from '../test/utils/apiclient.ts';
import type { BASRequest } from '../request-context.ts';
import type { Response } from 'express';
import { WebDemoConnector } from '../connectors/webdemo.ts';
import assert from 'node:assert';

interface HealthResponse {
    now: number;
    build: string;
    status: string;
    dbConnection?: string;
    webdemo?: string;
    stat?: unknown;
    serverID?: string;
}

export async function health(req: BASRequest, res: Response): Promise<void> {
    const stat = req.query.stat;
    const now = req.bas.clock.now();
    const reset = req.query.reset;
    const testDbConnection = req.query.db === 'true';
    const testWebdemo = req.query.webdemo === 'true';
    const paramServerID = req.query.serverID as string | undefined;

    const resp: HealthResponse = {
        now,
        build: req.bas.config.buildNumber,
        status: 'ok',
    };

    req.bas.addLoggerInfo({ testDbConnection, testWebdemo, paramServerID });

    if (testDbConnection) {
        try {
            await req.bas.acquireSession();
        } catch (err) {
            req.bas.logger.error('Connection to database is unavailable', err as Error);
            res.status(503).send('Connection to database is unavailable');
            return;
        }
        await req.bas.releaseSession();
        resp.dbConnection = 'ok';
        res.json(resp);
        return;
    } else if (testWebdemo) {
        const authorized = await req.bas.verifyAdminCredentials();
        if (!authorized) {
            res.status(401).send('Authorization failed');
            return;
        }

        const rateLimiterGroup = req.bas.rateLimiterConfiguration.getRateLimitersGroup(
            ['healthCheck'],
            { application: 'bas' },
            req.bas.logger
        );
        const rateLimiterStatus = await rateLimiterGroup.checkExceeded();
        if (rateLimiterStatus.hasExceeded) {
            req.bas.sendStatusCodeJsonResponse(429, {
                error: rateLimiterStatus.publicInfo?.publicErrorMessage ?? 'Rate limit exceeded',
            });
            return;
        }
        rateLimiterGroup.incr();

        const basClient = new BASApiClient(req.app, {
            kind: 'wd',
            userName: 'health-check',
            getPlatformToken: () => {
                const wd = req.bas.getConnector('wd');
                assert(wd instanceof WebDemoConnector, 'Webdemo connector is not available');
                return wd.generalAccessPlatformToken;
            },
        });

        let result = await basClient.create();
        if (!result.ok || !result.body.platformArchiveID) {
            req.bas.logger.error(`Health check failed to create webdemo test archive: ${result.body.error}`);
            res.status(503).send('Health check failed to create webdemo test archive');
            return;
        }
        basClient.platformArchiveID = result.body.platformArchiveID;

        result = await basClient.open();
        if (!result.ok || !result.body.token) {
            req.bas.logger.error(`Health check failed to open webdemo test archive ${result.body.error}`);
            res.status(503).send('Health check failed to open webdemo test archive');
            return;
        }
        const token = result.body.token;

        result = await basClient.delete({ token });
        if (!result.ok || result.body.error) {
            req.bas.logger.error(`Health check failed to delete webdemo test archive ${result.body.error}`);
            res.status(503).send('Health check failed to delete webdemo test archive');
            return;
        }

        result = await basClient.close({ token });
        if (!result.ok) {
            req.bas.logger.error(`Health check failed to close webdemo test archive ${result.body.error}`);
            res.status(503).send('Health check failed to close webdemo test archive');
            return;
        }

        resp.webdemo = 'ok';
        res.json(resp);
        return;
    } else if (reset === 'edeef418-ddf6-4f1c-bfe5-e60237938adc') {
        req.bas.addLoggerInfo({ action: 'startup' });
        if (paramServerID === req.bas.serverID) {
            req.bas.logger.info('Going to restart the server ' + req.bas.serverID);
            resp.status = 'exiting';
            res.json(resp);
            await new Promise((resolve) => setTimeout(resolve, 500));
            process.exit();
        } else {
            resp.status = 'Ignoring the reset command ' + paramServerID + ' != ' + req.bas.serverID;
            req.bas.logger.info(resp.status);
        }
    }

    if (stat) {
        resp.stat = req.bas.sessionManager.getSessionStat();
        resp.serverID = req.bas.serverID;
    }

    res.json(resp);
}
