import path from 'path';
import { getThumbnailFromDump, pipeToNewRequest, createBARFromBuffer } from '../utils.ts';
import { callWithLegacyCallback, isBASCallbackError, isBASLegacyErrorObject, makeLegacyResultFromError } from '../calling-style.ts';
import type { BASRequest } from '../request-context.ts';
import type { Response } from 'express';
import { isPlatformData, type PermalinkData, type PlatformData } from '../model.ts';
import assert from 'assert';
import type { Logger } from '@balsamiq/logging';
import type { DBConnector } from '../database.ts';
import type { SessionData, SessionManager } from '../session-manager.ts';
import type { Connector } from '../connectors/connectors-registry.ts';

export async function getPermalink(req: BASRequest, res: Response): Promise<Response> {
    let permalinkID = req.params.id || req.query.ID;

    if (!permalinkID || typeof permalinkID !== 'string') {
        return res.status(400).json({ error: 'invalid parameters' });
    }

    if (permalinkID.endsWith('.png')) {
        permalinkID = path.basename(permalinkID, '.png');
    } else if (permalinkID.endsWith('.jpg')) {
        permalinkID = path.basename(permalinkID, '.jpg');
    }

    // in case of direct call we need to clean the ID
    if (permalinkID.includes('_thumbnail')) {
        permalinkID = permalinkID.replace('_thumbnail', '');
    }

    const query = req.query.query || req.query.q || 'none'; // "exists" || "metadata" || "thumbnail-only" || "none" if not set
    const platformToken = req.query.platformToken && typeof req.query.platformToken === 'string' ? req.query.platformToken : null;

    req.bas.addLoggerInfo({ query, permalinkID, platformToken });

    res.setHeader('Cache-Control', 'no-cache, no-store');

    const sessionData = await req.bas.acquireSession();
    const dbConnector = sessionData.dbConnector;
    let permalinkData: PermalinkData;
    try {
        permalinkData = await callWithLegacyCallback((cb) => dbConnector.getPermalinkData(permalinkID, cb));
    } catch (err) {
        if (isBASCallbackError(err) && err.resultObj?.notFound) {
            if (query === 'none') {
                // Request coming from the browser -> answer text
                return permalinks404Response(req, res);
            } else {
                // Request coming from some integration -> Answer json
                return res.status(404).json({ error: 'No permalink found', notFound: true });
            }
        }
        throw err;
    }

    const { platformKind, platformInfo, platformSiteID, platformArchiveID, resourceID, branchID, permalinkInfo, permalinkKind, timestamp } =
        permalinkData;

    const connector = req.bas.getConnector(platformKind);
    if (!connector) {
        return res.status(400).json({ error: 'connector unavailable for platformKind ' + platformKind });
    }

    req.bas.addLoggerInfo({ platformKind, platformSiteID, platformArchiveID, resourceID, branchID });

    if (query === 'exists') {
        req.bas.logger.info('Check permalink existance');
        return res.status(200).json({ permalinkID });
    } else if (query === 'none') {
        req.bas.logger.info('Serving permalink image');
        if (!connector || !connector.asyncGetPermalink) {
            return res.status(501).send('Not Implemented');
        }

        // [PUBLIC_SHARE] We allow to retrieve the thumbnail associated to the Public Share link
        // if (permalinkKind !== 'image') {
        //     // Do not serve images for permalinkKind !== 'image'
        //     return permalinks404Response();
        // }

        // Pass the request down to the connector
        try {
            await connector.asyncGetPermalink({
                sessionData,
                dbConnector,
                permalinkData,
                releaseSessionIfNotReleased: async () => await req.bas.releaseSession(),
                req,
                res,
            });
            return res;
        } catch (err) {
            if (isBASCallbackError(err) && err.resultObj?.info) {
                req.bas.logger.info(err.message);
            } else {
                const error = err as Error;
                req.bas.logger.error(`Unexpected error in connector.getPermalink: ${error.message}`, error);
            }

            if (res.headersSent) {
                // Failed while streaming the data. Cannot fix this anymore.
                return res.end();
            } else {
                return permalinks404Response(req, res);
            }
        }
    } else if (query === 'metadata') {
        if (req.query && req.query.agent) {
            req.bas.addLoggerInfo({ agent: req.query.agent });
        }
        req.bas.logger.info('Getting permalink metadata');

        let thumbnailInfo, archiveID;
        try {
            ({ thumbnailInfo, archiveID } = await getThumbnailAndArchiveID(
                {
                    permalinkData,
                    platformKind,
                    platformSiteID,
                    platformArchiveID,
                    platformInfo,
                    resourceID,
                    branchID,
                },
                connector,
                sessionData,
                dbConnector,
                req
            ));
        } catch (err) {
            const error = err as Error;
            return res.status(500).json({ error: error.message });
        }

        let authenticated = false;
        if (platformToken && connector && connector.asyncAuthorizedForArchive) {
            try {
                await connector.asyncAuthorizedForArchive(
                    req.bas.logger,
                    sessionData,
                    platformArchiveID,
                    platformSiteID,
                    platformInfo,
                    platformToken
                );
                authenticated = true;
            } catch (err) {
                authenticated = false;
            }
        }

        if (!authenticated) {
            return res.status(200).json({
                ...thumbnailInfo,
                permalinkData: {
                    permalinkInfo: {
                        ...permalinkInfo,
                        ...connector.getPermalinkExtraFromPermalinkData(permalinkData),
                    },
                    permalinkID,
                    permalinkKind: permalinkData.permalinkKind,
                    platformKind: permalinkData.platformKind,
                    timestamp,
                },
            });
        } else {
            return res.status(200).json({
                ...thumbnailInfo,
                permalinkData: {
                    ...permalinkData,
                    permalinkInfo: {
                        ...permalinkData.permalinkInfo,
                        ...connector.getPermalinkExtraFromPermalinkData(permalinkData),
                    },
                    rtcInfo: {
                        rtcChannel: archiveID,
                        newRtc: true,
                    },
                },
            });
        }
    } else if (query === 'thumbnail-only') {
        req.bas.logger.info('Getting permalink thumbnail');
        let image;
        try {
            image = await connector.asyncGetPermalinkThumbnail(permalinkData, req.bas.logger);
        } catch (err) {
            const error = err as Error;
            req.bas.logger.error('No thumbnail found on S3 ', error);
            return res.status(404).json({ error: 'Not Found' });
        }
        const format = permalinkInfo?.image?.format || 'png';
        return await base64ImageResponse(req, res, { data: image, format });
    } else {
        req.bas.logger.error('unexpected action: ' + query);
        return res.status(400).json({ error: 'unexpected action' });
    }
}

// HELPER FUNCTIONS
async function permalinks404Response(req: BASRequest, res: Response) {
    const staticFileRepository = req.bas.staticFileRepository.legacyGetStaticFileRepository();
    assert(staticFileRepository !== null, 'Static file repository is not available');

    const url = staticFileRepository + 'share/404_image_permalink.html';
    pipeToNewRequest(
        req,
        res,
        {
            url: url,
            incomingResHandler: (incomingRes) => {
                incomingRes.statusCode = 404;
            },
        },
        req.bas.logger,
        (result) => {
            if (isBASLegacyErrorObject(result)) {
                res.status(404).end();
            }
        }
    );
    return res;
}

async function base64ImageResponse(
    req: BASRequest,
    res: Response,
    { data, filename = null, format = 'png' }: { data: string; filename?: string | null; format: string }
) {
    await req.bas.releaseSession();

    const binaryData = Buffer.from(data, 'base64');
    res.setHeader('Content-Type', 'image/' + format);
    res.setHeader('Content-Length', Buffer.byteLength(binaryData));
    if (filename) {
        res.setHeader('content-disposition', `inline; filename=${filename}`);
    }
    res.write(binaryData);
    return res.end();
}

async function getThumbnailAndArchiveID(
    {
        permalinkData,
        platformKind,
        platformSiteID,
        platformArchiveID,
        platformInfo,
        resourceID,
        branchID,
    }: {
        permalinkData: PermalinkData;
        platformKind: string;
        platformSiteID: string;
        platformArchiveID: string;
        platformInfo: Record<string, unknown>;
        resourceID: string;
        branchID: string;
    },
    connector: Connector,
    sessionData: SessionData,
    dbConnector: DBConnector,
    req: BASRequest
) {
    const archiveInfo = await callWithLegacyCallback<PlatformData | {}>((cb) =>
        dbConnector.getBASArchiveIDWithExclusiveRowLock(platformSiteID, platformArchiveID, true, cb)
    );

    let ret;
    if (!isPlatformData(archiveInfo)) {
        // Retrieve the archive id from connector
        ret = await _getThumbnailFromConnector(
            { platformKind, platformSiteID, platformArchiveID, platformInfo, resourceID, branchID },
            connector,
            sessionData,
            dbConnector,
            req
        );
    } else {
        const archiveID = archiveInfo.BAS_ARCHIVE_ID;
        ret = await _getThumbnailFromBAS({ archiveID, platformSiteID, platformArchiveID, resourceID, branchID }, sessionData, req);
    }
    if (ret.thumbnailInfo.image) {
        // nothing to do
        req.bas.logger.info('Thumbnail saved in the BMPR');
    } else {
        req.bas.logger.info('Get thumbnail from S3');
        try {
            ret.thumbnailInfo.image = await connector.asyncGetPermalinkThumbnail(permalinkData, req.bas.logger);
        } catch (e) {
            // TODO: consider removing from error logging.
            req.bas.logger.error('No thumbnail found on S3: ' + e);
        }
    }

    return ret;
}

async function _getThumbnailFromConnector(
    {
        platformKind,
        platformSiteID,
        platformArchiveID,
        platformInfo,
        resourceID,
        branchID,
    }: {
        platformKind: string;
        platformSiteID: string;
        platformArchiveID: string;
        platformInfo: Record<string, unknown>;
        resourceID: string;
        branchID: string;
    },
    connector: Connector,
    sessionData: SessionData,
    dbConnector: DBConnector,
    req: BASRequest
) {
    const authInfo = await callWithLegacyCallback<{ platformToken: string | null }>((cb) =>
        connector.getAuthTokenFromPlatform(req.bas.logger, platformInfo, cb)
    );
    const platformToken = authInfo.platformToken;

    const archive = await callWithLegacyCallback<BalsamiqArchive>((cb) =>
        connector.loadFromPlatform(req.bas.logger, dbConnector, platformToken, platformSiteID, platformArchiveID, null, platformInfo, cb)
    );
    const archiveID = archive.id;
    const buffer = archive.buffer;

    platformInfo = archive.platformInfo || platformInfo; // update the platformInfo if changed by the loadFromPlatform
    const bar = await callWithLegacyCallback<BalsamiqArchive>((cb) =>
        createBARFromBuffer(
            archiveID,
            buffer,
            sessionData,
            platformInfo,
            platformKind,
            platformSiteID,
            platformArchiveID,
            null,
            dbConnector,
            req.bas.logger,
            req.bas.config,
            req.bas.sessionManager,
            cb
        )
    );

    let thumbnailInfo = await callWithLegacyCallback<ThumbnailInfo>((cb) => getThumbnailFromDump(resourceID, branchID, bar.dump, cb));

    if (!thumbnailInfo.image && bar.dump.Thumbnails.length > 0) {
        ({ resourceID, branchID } = bar.dump.Thumbnails[0].ATTRIBUTES);
        thumbnailInfo = await callWithLegacyCallback((cb) => getThumbnailFromDump(resourceID, branchID, bar.dump, cb));
    }

    return { thumbnailInfo, archiveID };
}

async function _getThumbnailFromBAS(
    {
        archiveID,
        platformSiteID,
        platformArchiveID,
        resourceID,
        branchID,
    }: {
        archiveID: string;
        platformSiteID: string;
        platformArchiveID: string;
        resourceID: string;
        branchID: string;
    },
    sessionData: SessionData,
    req: BASRequest
) {
    req.bas.logger.info(
        `permalink accessing an already opened basArchiveID:${archiveID} platformArchiveID:${platformArchiveID} platformSiteID: ${platformSiteID}`
    );

    try {
        const archive = await callWithLegacyCallback<BalsamiqArchive>((cb) =>
            req.bas.sessionManager.openBarLocked(sessionData, archiveID, 'READ', cb)
        );
        const bar = archive.bar;
        const { attributes } = await callWithLegacyCallback<BalsamiqArchiveAttributes>((cb) => bar.getArchiveAttributes(cb));
        let thumbnailInfo;
        try {
            thumbnailInfo = await callWithLegacyCallback((cb) => bar.getThumbnailFromResourceID(resourceID, branchID, cb));
        } catch (err) {
            // Try again by picking the first thumbnail of the archive
            const { thumbnails } = await callWithLegacyCallback<any>((cb) => bar.getThumbnails(cb));
            if (thumbnails.length === 0) {
                throw new Error(`No Thumbnails found in archive`);
            }
            ({ branchID, resourceID } = thumbnails[0].ATTRIBUTES);
            thumbnailInfo = await callWithLegacyCallback<ThumbnailInfo>((cb) => bar.getThumbnailFromResourceID(resourceID, branchID, cb));
        }
        return { thumbnailInfo: { ...thumbnailInfo, projectName: attributes.name }, archiveID };
    } finally {
        await callWithLegacyCallback((cb) => req.bas.sessionManager.unlockConnection(sessionData, cb));
    }
}
