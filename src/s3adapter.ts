import {
    type CompleteMultipartUploadCommandOutput,
    type DeleteObjectsCommandInput,
    type DeleteObjectsCommandOutput,
    type GetObjectCommandInput,
    type GetObjectCommandOutput,
    type HeadObjectCommandInput,
    type HeadObjectCommandOutput,
    type PutObjectCommandInput,
    S3,
} from '@aws-sdk/client-s3';
import { Upload } from '@aws-sdk/lib-storage';
import type { DataResidencyName } from './environment-variables-schemas.ts';
import type { Readable } from 'stream';

type StreamingBlobPayloadInputTypes = NonNullable<PutObjectCommandInput['Body']>; // Because `StreamingBlobPayloadInputTypes` isn't exported from `@aws-sdk/*`
type StreamingBlobPayloadOutputTypes = NonNullable<GetObjectCommandOutput['Body']>; // Because `StreamingBlobPayloadOutputTypes` isn't exported from `@aws-sdk/*`

export type {
    CompleteMultipartUploadCommandOutput,
    DeleteObjectsCommandInput,
    DeleteObjectsCommandOutput,
    GetObjectCommandInput,
    GetObjectCommandOutput,
    HeadObjectCommandInput,
    HeadObjectCommandOutput,
    PutObjectCommandInput,
    StreamingBlobPayloadInputTypes,
    StreamingBlobPayloadOutputTypes,
};

// TODO: Probabily it's wiser to introduce simpler and more "controlled" types for
//       parameters instead of S3's `*(Input|Output)` types, but that would require
//       a deeper analysis of the code base which we didn't want to do at this
//       point and instead focus on TS-ifying the existing code without
//       introducing any breaking changes.
export interface IS3Adapter {
    upload(
        region: string,
        params: PutObjectCommandInput,
        cb: (err: S3Error | null, data?: CompleteMultipartUploadCommandOutput) => void
    ): void;
    getObject(region: string, params: GetObjectCommandInput): Promise<GetObjectCommandOutput>;
    headObject(region: string, params: HeadObjectCommandInput, cb: (err: S3Error | null, data?: HeadObjectCommandOutput) => void): void;
    deleteObjects(
        region: string,
        params: DeleteObjectsCommandInput,
        cb: (err: S3Error | null, data?: DeleteObjectsCommandOutput) => void
    ): void;
}

export class S3Adapter implements IS3Adapter {
    upload(region: string, params: PutObjectCommandInput, cb: (err: S3Error | null, data?: CompleteMultipartUploadCommandOutput) => void) {
        try {
            const s3 = new S3({ region });
            const uploadCommand = new Upload({
                client: s3,
                params,
            });

            uploadCommand
                .done()
                .then((result) => cb(null, result))
                .catch((err) => cb(err));
        } catch (err) {
            cb(err as S3Error);
        }
    }

    getObject(region: string, params: GetObjectCommandInput): Promise<GetObjectCommandOutput> {
        const s3 = new S3({ region });
        return s3.getObject(params);
    }

    headObject(region: string, params: HeadObjectCommandInput, cb: (err: S3Error | null, data?: HeadObjectCommandOutput) => void): void {
        const s3 = new S3({ region });
        s3.headObject(params, cb);
    }

    deleteObjects(
        region: string,
        params: DeleteObjectsCommandInput,
        cb: (err: S3Error | null, data?: DeleteObjectsCommandOutput) => void
    ): void {
        const s3 = new S3({ region });
        s3.deleteObjects(params, cb);
    }
}

export type BucketResidencyProps = {
    bucketRegion: string;
    bucketName: string;
    baseDir: string;
};

export type PermalinkImageFormat = 'png' | 'jpg';

export class S3PermalinksImageStorageAdapter {
    private dataResidencies: Record<DataResidencyName, BucketResidencyProps>;
    private defaultDataResidencyName: DataResidencyName;
    private s3: IS3Adapter;

    constructor(
        dataResidencies: Record<DataResidencyName, BucketResidencyProps>,
        defaultDataResidencyName: DataResidencyName,
        s3: IS3Adapter
    ) {
        this.dataResidencies = dataResidencies;
        this.defaultDataResidencyName = defaultDataResidencyName;
        this.s3 = s3;
    }

    async uploadPermalinkImage({
        dataStream,
        mimeType,
        platformKind,
        permalinkID,
        format = 'png',
        dataResidency,
    }: {
        dataStream: StreamingBlobPayloadInputTypes;
        mimeType: string;
        platformKind: string;
        permalinkID: string;
        format?: PermalinkImageFormat;
        dataResidency: DataResidencyName;
    }) {
        const { bucketRegion, bucketName } = this.dataResidencies[dataResidency];

        const params = {
            Bucket: bucketName,
            Key: this._getPermalinkKey({ platformKind, permalinkID, format, dataResidency }),
            Body: dataStream,
            ContentType: mimeType,
            CacheControl: 'max-age=5',
        };

        return await new Promise((resolve, reject) => {
            let error = new Error('Error on S3 API upload');
            this.s3.upload(bucketRegion, params, function (s3err, data) {
                if (s3err) {
                    reject(new AugmentedS3Error(error, s3err));
                } else {
                    resolve(data);
                }
            });
        });
    }

    async uploadSnapshotImage({
        dataStream,
        mimeType,
        bucketDir,
        UUID,
        metadata,
        format,
        dataResidency,
    }: {
        dataStream: StreamingBlobPayloadInputTypes;
        mimeType: string;
        bucketDir: string;
        UUID: string;
        metadata: {
            platformArchiveID: string;
            platformSiteID: string;
            platformKind: string;
            userID: string;
            branchID: string;
            resourceID: string;
        };
        format: PermalinkImageFormat;
        dataResidency: DataResidencyName;
    }) {
        const { bucketRegion, bucketName } = this.dataResidencies[dataResidency];

        const params = {
            Bucket: bucketName,
            Key: `snapshots/${bucketDir}/${UUID}.${format}`,
            Body: dataStream,
            ContentType: mimeType,
            Metadata: {
                platformarchiveid: metadata.platformArchiveID,
                platformsiteid: metadata.platformSiteID,
                platformkind: metadata.platformKind,
                userid: metadata.userID,
                branchid: metadata.branchID,
                resourceid: metadata.resourceID,
            },
        };

        return await new Promise((resolve, reject) => {
            let error = new Error('Error on S3 API upload');
            this.s3.upload(bucketRegion, params, function (s3err, data) {
                if (s3err) {
                    reject(new AugmentedS3Error(error, s3err));
                } else {
                    resolve(data);
                }
            });
        });
    }

    async getPermalinkImageStream({
        platformKind,
        permalinkID,
        format = 'png',
        dataResidency,
    }: {
        platformKind: string;
        permalinkID: string;
        format?: PermalinkImageFormat;
        dataResidency: DataResidencyName;
    }) {
        const { bucketRegion, bucketName } = this.dataResidencies[dataResidency];
        const params = {
            Bucket: bucketName,
            Key: this._getPermalinkKey({ platformKind, permalinkID, format, dataResidency }),
        };
        const response = await this.s3.getObject(bucketRegion, params);
        return response.Body;
    }

    async getPermalinkImageBase64({
        platformKind,
        permalinkID,
        format = 'png',
        dataResidency,
    }: {
        platformKind: string;
        permalinkID: string;
        format?: PermalinkImageFormat;
        dataResidency: DataResidencyName;
    }) {
        try {
            const { bucketRegion, bucketName } = this.dataResidencies[dataResidency];

            const params = {
                Bucket: bucketName,
                Key: this._getPermalinkThumbnailKey({ platformKind, permalinkID, format, dataResidency }),
            };

            const response = await this.s3.getObject(bucketRegion, params);
            if (!response.Body) {
                throw new Error('Error on getPermalinkImageBase64: No response body');
            }
            const byteArray = await response.Body.transformToByteArray();

            // Convert the image buffer to a base64-encoded string
            return Buffer.from(byteArray).toString('base64');
        } catch (error) {
            // should we log something or just raise the exception?
            // console.error('Error fetching image from S3:', error);
            throw error;
        }
    }

    async getPermalinkHeadersWithMetadata({
        platformKind,
        permalinkID,
        format,
        dataResidency,
    }: {
        platformKind: string;
        permalinkID: string;
        format: PermalinkImageFormat;
        dataResidency: DataResidencyName;
    }) {
        const { ETag, LastModified, CacheControl, ContentType } = await this.getPermalinkMetadata({
            platformKind,
            permalinkID,
            format,
            dataResidency,
        });
        let headers = ContentType ? [['Content-Type', `${ContentType}`]] : [['Content-Type', 'image/' + format]];
        if (ETag) {
            headers.push(['ETag', `${ETag}`]);
        }
        if (CacheControl) {
            headers.push(['Cache-Control', `${CacheControl}`]);
        }
        if (LastModified) {
            headers.push(['Last-Modified', `${LastModified}`]);
        }
        return headers;
    }

    async getPermalinkMetadata({
        platformKind,
        permalinkID,
        format = 'png',
        dataResidency,
    }: {
        platformKind: string;
        permalinkID: string;
        format?: PermalinkImageFormat;
        dataResidency: DataResidencyName;
    }): Promise<HeadObjectCommandOutput> {
        const { bucketRegion, bucketName } = this.dataResidencies[dataResidency];
        const params = {
            Bucket: bucketName,
            Key: this._getPermalinkKey({ platformKind, permalinkID, format, dataResidency }),
        };
        return await new Promise((resolve, reject) => {
            let error = new Error('Error on getPermalinkMetadata');
            this.s3.headObject(bucketRegion, params, function (s3err: S3Error | null, data?: HeadObjectCommandOutput) {
                if (s3err) {
                    reject(new AugmentedS3Error(error, s3err));
                } else {
                    if (!data) {
                        reject(new Error('Error on S3 API headObject'));
                        return;
                    }

                    resolve(data);
                }
            });
        });
    }

    async getSnapshotImageStream({
        bucketDir,
        UUID,
        dataResidency,
    }: {
        bucketDir: string;
        UUID: string;
        dataResidency: DataResidencyName;
    }) {
        const { bucketRegion, bucketName } = this.dataResidencies[dataResidency];
        const params = {
            Bucket: bucketName,
            Key: `snapshots/${bucketDir}/${UUID}`,
        };
        let response = await this.s3.getObject(bucketRegion, params);
        return response.Body as Readable | undefined;
    }

    async deletePermalinkImages({
        platformKind,
        permalinkIDs,
        chunkSize = 500,
        format = 'png',
        dataResidency = this.defaultDataResidencyName,
    }: {
        platformKind: string;
        permalinkIDs: string[];
        chunkSize?: number;
        format?: PermalinkImageFormat;
        dataResidency?: DataResidencyName;
    }) {
        const promises = [];

        // Split permalinkIDs into chunks
        for (let i = 0; i < permalinkIDs.length; i += chunkSize) {
            const chunk = permalinkIDs.slice(i, i + chunkSize);

            const { bucketRegion, bucketName } = this.dataResidencies[dataResidency];

            const params = {
                Bucket: bucketName,
                Delete: {
                    Objects: chunk.map((permalinkID) => ({
                        Key: this._getPermalinkKey({ platformKind, permalinkID, format, dataResidency }),
                    })),
                    Quiet: true, // returns only keys of failed-to-delete items
                },
            };

            const promise = new Promise((resolve, reject) => {
                this.s3.deleteObjects(bucketRegion, params, (s3err: S3Error | null, data?: DeleteObjectsCommandOutput) => {
                    if (s3err !== null) {
                        reject(new AugmentedS3Error(new Error('Error on S3 API deleteObjects'), s3err));
                    } else {
                        resolve(data);
                    }
                });
            });

            promises.push(promise);
        }

        return Promise.all(promises);
    }

    async deletePermalinkThumbnailImages({
        platformKind,
        permalinkIDs,
        chunkSize = 500,
        format = 'png',
        dataResidency = this.defaultDataResidencyName,
    }: {
        platformKind: string;
        permalinkIDs: string[];
        chunkSize?: number;
        format?: PermalinkImageFormat;
        dataResidency?: DataResidencyName;
    }) {
        const promises = [];

        // Split permalinkIDs into chunks
        for (let i = 0; i < permalinkIDs.length; i += chunkSize) {
            const chunk = permalinkIDs.slice(i, i + chunkSize);

            const { bucketRegion, bucketName } = this.dataResidencies[dataResidency];

            const params = {
                Bucket: bucketName,
                Delete: {
                    Objects: chunk.map((permalinkID) => ({
                        Key: this._getPermalinkThumbnailKey({ platformKind, permalinkID, format, dataResidency }),
                    })),
                    Quiet: true, // returns only keys of failed-to-delete items
                },
            };

            const promise = new Promise((resolve, reject) => {
                this.s3.deleteObjects(bucketRegion, params, (s3err: S3Error | null, data?: DeleteObjectsCommandOutput) => {
                    if (s3err !== null) {
                        reject(new AugmentedS3Error(new Error('Error on S3 API deleteObjects'), s3err));
                    } else {
                        resolve(data);
                    }
                });
            });

            promises.push(promise);
        }

        return Promise.all(promises);
    }

    _getPermalinkKey({
        platformKind,
        permalinkID,
        format = 'png',
        dataResidency,
    }: {
        platformKind: string;
        permalinkID: string;
        format?: PermalinkImageFormat;
        dataResidency: DataResidencyName;
    }) {
        const { baseDir } = this.dataResidencies[dataResidency];
        return `${baseDir}/${platformKind}/${permalinkID}.${format}`;
    }

    _getPermalinkThumbnailKey({
        platformKind,
        permalinkID,
        format = 'png',
        dataResidency,
    }: {
        platformKind: string;
        permalinkID: string;
        format?: PermalinkImageFormat;
        dataResidency: DataResidencyName;
    }) {
        const { baseDir } = this.dataResidencies[dataResidency];
        return `${baseDir}/${platformKind}/${permalinkID}_thumbnail.${format}`;
    }
}

type S3Error = {
    message: string;
    code: string;
    region: string;
    time: Date;
    extendedRequestId: string;
};

class AugmentedS3Error extends Error {
    public s3error: S3Error;
    constructor(error: Error, s3error: S3Error) {
        super(`${error.message}: ${s3error.message}`);
        this.s3error = s3error;
    }
}
