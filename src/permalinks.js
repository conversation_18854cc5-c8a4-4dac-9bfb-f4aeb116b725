import con from './constants.ts';
import { callWithLegacyCallback } from './calling-style.ts';
import * as Consts from '@balsamiq/bmpr/lib/BalsamiqArchiveConstants.js'

async function runTasksInBatches(tasks, concurrency) {
    let results = [];
    try {
        while (tasks.length > 0) {
            let currentBatch = tasks.slice(0, concurrency);
            tasks = tasks.slice(concurrency);
            let currentResults = await Promise.all(currentBatch.map(task => task()));
            results = results.concat(currentResults);
        }
    } catch (e) {
        throw e;
    }
    return results;
}


async function markPermalinkAsOutdated({
    dbConnector,
    broadcastRTCMessage,

    basArchiveID,
    username,
    resourceID,
    branchID,
    archiveRevision
}) {
    if (!basArchiveID || !resourceID || !branchID || !archiveRevision) {
        throw new Error(`Invalid Parameters: ${basArchiveID} ${resourceID} ${branchID} ${archiveRevision}`);
    }

    let affectedRows = await dbConnector.markPermalinkAsOutdated({basArchiveID, resourceID, branchID});

    if (affectedRows > 0) {
        // Do not set the userID, so that the sender will also receive the message
        broadcastRTCMessage(basArchiveID, archiveRevision, null, username, {
            operation: con.API_CREATE_OR_UPDATE_IMAGE_LINK,
            resourceID,
            branchID,
        });
    }

    return {};
}

async function deletePermalinksByArchiveID({
    dbConnector,
    getConnector,
    platformKind,
    platformSiteID,
    platformArchiveID,
}) {
    return await deletePermalinksByMinTimestamp({
        dbConnector,
        getConnector,
        platformKind,
        platformSiteID,
        platformArchiveID,
        timestamp: 0,
    });
}

/**
 * Deletes permalinks by minimum timestamp.
 * @param {Object} param0 - The parameters.
 * @param {JSDocTypes.DBConnector} param0.dbConnector - The database connector.
 * @param {(kind: string | null) => JSDocTypes.Connector | null} param0.getConnector - Function to get the connector.
 * @param {string} param0.platformKind - The platform kind.
 * @param {string} param0.platformSiteID - The platform site ID.
 * @param {string} param0.platformArchiveID - The platform archive ID.
 * @param {string} param0.timestamp - The minimum timestamp for deletion.
 * @returns {Promise<{}>} - The result of the deletion.
 */
async function deletePermalinksByMinTimestamp({
    dbConnector,
    getConnector,
    platformKind,
    platformSiteID,
    platformArchiveID,
    timestamp,
}) {
    timestamp = Number.parseInt(timestamp, 10);
    const connector = getConnector(platformKind);
    if (!platformKind || !platformArchiveID || isNaN(timestamp) || !connector) {
        throw new Error(`Invalid Parameters: ${platformKind}, ${platformSiteID}, ${platformArchiveID}, ${timestamp}`);
    }

    const { permalinksData } = await dbConnector.getPermalinksByArchiveIDAndMinTimestamp({platformKind, platformSiteID, platformArchiveID, timestamp});
    if (permalinksData.length > 0) {
        await deletePermalinksImages(permalinksData, connector);
    }

    await dbConnector.deletePermalinksByArchiveIDAndMinTimestamp({platformKind, platformSiteID, platformArchiveID, timestamp});
    if (timestamp > 0) {
        await dbConnector.markPermalinksAsOutdated({platformKind, platformSiteID, platformArchiveID});
    }

    return {};
}

const deletePermalinksImages = async (permalinksData, connector) => {
    // The reason we group by data residency is because ultimately S3 API isn't
    // meant to be called on different regions for multiple objects, therefore by
    // grouping permalinks by data residency we make sure to call S3 client once
    // per data residency (or AWS region) in lower levels of abstraction:
    const permalinksDataGroupedByDataResidency = permalinksData.reduce((result, permalinkData) => {
        const dataResidency = permalinkData.permalinkInfo?.dataResidency || connector.config.defaultDataResidencyName;
        result[dataResidency] = result[dataResidency] || [];
        result[dataResidency].push(permalinkData);
        return result;
    }, {});

    for (const [dataResidency, permalinksData] of Object.entries(permalinksDataGroupedByDataResidency)) {
        const permalinkIDs_png = permalinksData
            .filter(permalinkData => {
                const format = permalinkData.permalinkInfo?.image?.format;
                return format ? format === "png" : true;
            })
            .map(permalinkData => permalinkData.permalinkID);
        if (permalinkIDs_png.length > 0) {
            await connector.deletePermalinkImages({permalinkIDs: permalinkIDs_png, dataResidency});
            await connector.deletePermalinkThumbnailImages({permalinkIDs: permalinkIDs_png, dataResidency});
        }
        const permalinkIDs_jpg = permalinksData
            .filter(permalinkData => {
                const format = permalinkData.permalinkInfo?.image?.format;
                return format ? format === "jpg" : false;
            })
            .map(permalinkData => permalinkData.permalinkID);
        if (permalinkIDs_jpg.length > 0) {
            await connector.deletePermalinkImages({permalinkIDs: permalinkIDs_jpg, format: 'jpg', dataResidency});
            await connector.deletePermalinkThumbnailImages({permalinkIDs: permalinkIDs_jpg, format: 'jpg', dataResidency});
        }
    }

    return permalinksData.map(permalinkData => permalinkData.permalinkID);
}

async function deletePermalinksByResourceIDs({
                                                  dbConnector,
                                                  getConnector,
                                                  platformKind,
                                                  platformSiteID,
                                                  platformArchiveID,
                                                  resourceIDs,
                                                  branchID,
                                                  permalinkKind
                                              }) {
    if (!platformKind || !platformArchiveID || !resourceIDs || !branchID) {
        throw new Error(`Invalid Parameters: ${platformKind}, ${platformSiteID}, ${platformArchiveID}, ${resourceIDs}, ${branchID}`);
    }

    let resp = {affectedRows: 0};
    const connector = getConnector(platformKind);
    if (connector) {
        const { permalinksData } = await dbConnector.getPermalinksDataByResourceIDs({platformKind, platformSiteID, platformArchiveID, resourceIDs, branchID, permalinkKind});
        if (permalinksData.length > 0) {
            const permalinkIDs = await deletePermalinksImages(permalinksData, connector);
            resp = await dbConnector.deletePermalinksByPermalinkIDs({platformKind, platformSiteID, platformArchiveID, permalinkIDs, permalinkKind});
        }
    }

    return resp;
}

/**
 * 
 * @param {object} param0 
 * @param {JSDocTypes.DBConnector} param0.dbConnector
 * @param {(kind: string | null) => JSDocTypes.Connector | null} param0.getConnector
 * @param {string} param0.platformArchiveID
 * @param {string | null} param0.platformKind
 * @param {string | null} param0.platformSiteID
 * @param {function} param0.broadcastRTCMessage
 * @param {string} param0.userName
 * @param {string} param0.archiveID
 * @param {number} param0.timestamp
 * @param {JSDocTypes.Logger} param0.logger
 * 
 * @returns {Promise<{}>}
 */
async function updatePermalinkImages ({
                                         dbConnector,
                                         getConnector,
                                         platformArchiveID,
                                         platformKind,
                                         platformSiteID,
                                         broadcastRTCMessage,
                                         userName,
                                         archiveID,
                                         timestamp,
                                         logger
                                     })
{
    const connector = getConnector(platformKind);
    if (!connector) {
        return {};
    }

    const startTime = Date.now();
    let w2iTasks = [];
    let runs = 0;
    const { permalinksData } = await callWithLegacyCallback(cb => dbConnector.getPermalinksDataFromArchiveID(platformKind, platformSiteID, platformArchiveID, cb));

    const filteredPermalinkData = permalinksData.filter((permalinkData) => {
        // looking for permalinks image to be updated (dirty)
        return (permalinkData.permalinkKind === Consts.PermalinkKind.image ||
            permalinkData.permalinkKind === Consts.PermalinkKind.image_unfurling) &&
            permalinkData.dirty;
    });

    if (filteredPermalinkData.length > 0) {
        const permalinkIDs = filteredPermalinkData.map(permalinkData => permalinkData.permalinkID);

        await dbConnector.markPermalinkByPermalinkIDs({platformKind, platformSiteID, platformArchiveID, permalinkIDs, dirty: false, timestamp});
        filteredPermalinkData.forEach((permalinkData) => {
            w2iTasks.push( () => _updatePermalinkImage(permalinkData));
            broadcastRTCMessage(archiveID, null, null, userName, {
                operation: con.API_CREATE_OR_UPDATE_IMAGE_LINK,
                resourceID: permalinkData.resourceID,
                branchID: permalinkData.branchID,

                platformKind: platformKind,
                platformArchiveID: platformArchiveID,
                platformSiteID: platformSiteID,
                permalinkUUID: permalinkData.permalinkID
            });
        });

        if ((runs = w2iTasks.length) > 0) {
            // running w2i image update, lazy image generation
            runTasksInBatches(w2iTasks, 5)
                .then(data => logger.info("w2iTasks runs result: " + data))
                .catch(err => logger.error(err))
        }
    }

    // w2i task
    async function _updatePermalinkImage(permalinkData) {
        let ret = {};
        try {
            ret = await connector.generatePermalinkImage({logger, permalinkData})
        } catch(err) {
            logger.error(`Unexpected error in connector.generatePermalinkImage: ${err.message} - ${JSON.stringify(permalinkData)}`);
        }
        return ret;
    }

    if (runs > 0) {
        logger = logger.getLogger({timeElapsed: (Date.now() - startTime), runs });
        logger.info("updatePermalinkImages: executed w2i");
    }

    return {};
}

export {
    markPermalinkAsOutdated,
    deletePermalinksByMinTimestamp,
    deletePermalinksByArchiveID,
    deletePermalinksByResourceIDs,
    updatePermalinkImages,
    deletePermalinksImages,
};
