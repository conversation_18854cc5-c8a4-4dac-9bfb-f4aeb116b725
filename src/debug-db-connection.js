// Quick debug script to test database connections
import { MySQLDriverPool } from './mysql-driver.ts';
import { RedisAdapter } from './redisAdapter.ts';
import { buildMainLogger } from '@balsamiq/logging';
import { Metrics } from './metrics.ts';
import { CloudWatchMock } from './test/utils/mocks.ts';

async function testConnections() {
    console.log('Testing database connections...');
    
    const logger = buildMainLogger(() => {}).getLogger({ context: 'test' });
    
    const metrics = new Metrics({
        logger: logger,
        namespace: 'test',
        buildNumber: '999',
        dbCloudWatch: new CloudWatchMock(),
        cloudWatch: new CloudWatchMock(),
    });
    
    try {
        // Test MySQL connection
        console.log('Testing MySQL connection...');
        const mySqlDriver = await new MySQLDriverPool(
            process.env['BAS_DB_HOST'] || '127.0.0.1',
            process.env['BAS_DB_USER'] || 'root',
            process.env['BAS_DB_PASSWORD'] || '',
            3306,
            metrics
        ).initialize(1, logger);
        
        console.log('MySQL driver initialized, testing connection...');
        
        await new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                reject(new Error('MySQL connection timeout'));
            }, 5000);
            
            mySqlDriver.waitForMySQLToBeReady((error) => {
                clearTimeout(timeout);
                if (error) {
                    reject(error);
                } else {
                    console.log('MySQL connection successful!');
                    resolve();
                }
            });
        });
        
        // Test Redis connection
        console.log('Testing Redis connection...');
        const redisAdapter = new RedisAdapter(
            { 
                port: 6379, 
                host: process.env['REDIS_HOST'] || 'localhost', 
                database: 1 
            },
            logger
        );
        
        const redisTimeout = setTimeout(() => {
            throw new Error('Redis connection timeout');
        }, 5000);
        
        await redisAdapter.init();
        clearTimeout(redisTimeout);
        console.log('Redis connection successful!');
        
        // Cleanup
        await redisAdapter.quit();
        await new Promise((resolve) => mySqlDriver.shutdown(resolve));
        
        console.log('All connections successful!');
        
    } catch (error) {
        console.error('Connection test failed:', error);
        console.error('Stack trace:', error.stack);
    }
}

testConnections();
