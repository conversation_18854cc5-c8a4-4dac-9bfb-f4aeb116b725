import util from 'util';
import { type Payload } from '@balsamiq/logging';
import chalk from 'chalk';

const AWS_INSTANCE_ID = process.env['AWS_INSTANCE_ID'];
const MONITOR_RUN_ID = process.env['MONITOR_RUN_ID'];
const LOG_COLORS = {
    INFO: chalk.blue,
    WARNING: chalk.yellow,
    ERROR: chalk.red,
};

export function formatLogForElasticSearch(payload: Payload) {
    // See: logs/src/lambda/ts/log_manipulators/bas.ts in https://github.com/balsamiq/logs
    const { timestamp, message, level } = payload;

    const cleanedPayload = Object.assign({}, payload) as Partial<typeof payload>;

    delete cleanedPayload.timestamp;
    delete cleanedPayload.message;
    delete cleanedPayload.level;

    return `${JSON.stringify({
        ['@timestamp']: timestamp,
        message: message,
        severity: level,

        ...cleanedPayload,

        fields: cleanedPayload,
        ec2id: AWS_INSTANCE_ID,
        monitorRunID: MONITOR_RUN_ID,
    })}\n`;
}

export function formatLogForConsole(payload: Payload) {
    const { timestamp, level } = payload;

    const color = LOG_COLORS[level];

    let msg = util.format(
        '%s %s %s\n',
        chalk.gray(timestamp),
        chalk.gray('[' + (payload.module ? 'M:' + payload.module + ' - ' : '') + (payload.action ? 'A:' + payload.action : '') + ']'),
        color ? color(payload.message) : payload.message
    );

    const cleanedPayload = Object.assign({}, payload) as Partial<typeof payload>;

    delete cleanedPayload['action'];
    delete cleanedPayload['module'];
    delete cleanedPayload['hostname'];
    delete cleanedPayload['message'];
    delete cleanedPayload['timestamp'];
    delete cleanedPayload['level'];
    delete cleanedPayload['stack'];
    delete cleanedPayload['errorInspect'];

    if (Object.keys(cleanedPayload).length > 0) {
        msg += chalk.gray(JSON.stringify(cleanedPayload) + '\n');
        if (payload.stack) {
            msg += chalk.red(payload.stack + '\n');
            if (payload.errorInspect) {
                msg += chalk.red(`errorInspect information available in log payload\n`);
            }
        }
    }
    return msg;
}
