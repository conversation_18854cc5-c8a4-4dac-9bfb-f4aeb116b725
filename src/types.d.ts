// NOTE: The following @ts-ignore directive is necessary to prevent conflicts from external modules that might inadvertently export the same name.
//@ts-ignore
declare var testContext: import('./test/test-context.ts').TestContext;

// No TS definitions
declare module 'hsts';

// These are utility types for writing JSDoc without having to re-import them everytime.
// Also consider that it's not possible to import _only_ types or interfaces into js files, so this is a convenient workaround.
declare namespace JSDocTypes {
    // Local BAS types
    type BASLegacyErrorObject = import('./calling-style.ts').BASLegacyErrorObject;
    type BASLegacyCallback<T> = import('./calling-style.ts').BASLegacyCallback<T>;
    type SessionManager = import('./session-manager.ts').SessionManager;
    type SessionData = import('./session-manager.ts').SessionData;
    type Metrics = import('./metrics.ts').Metrics;
    type MySQLDriverPool = import('./mysql-driver.ts').MySQLDriverPool;
    type DBConnector = import('./database.ts').DBConnector;
    type Config = import('./configLoader.ts').Config;
    type ConfigLoaderResult = import('./configLoader.ts').ConfigLoaderResult;
    type BuildNumberTracker = import('./track_build_number.ts').BuildNumberTracker;
    type RtcAdapter = import('./rtc-adapter.ts').RtcAdapter;
    type BackendRTCInterface = import('./rtc-adapter.ts').BackendRTCInterface;
    type Clock = import('./clock.ts').Clock;
    type ServerUtils = import('./server_utils.js').ServerUtils;
    type RedisAdapter = import('./redisAdapter.ts').RedisAdapter;
    type BASRequest = import('./request-context.ts').BASRequest;
    type Wireframe2imageAdapter = import('./wireframe2image-adapter.ts').Wireframe2imageAdapter;
    type AppContext = import('./app-context.ts').AppContext;
    type CloudConnector = import('./connectors/cloud.js').CloudConnector;
    type ConfluenceAdapter = import('./atlassian-adapter.ts').ConfluenceAdapter;
    type S3PermalinksImageStorageAdapter = import('./s3adapter.ts').S3PermalinksImageStorageAdapter;
    type Connector = import('./connectors/connectors-registry.ts').Connector;
    type DataResidencyName = import('./environment-variables-schemas.ts').DataResidencyName;

    // Database types
    type PermalinkData = import('./model.ts').PermalinkData;
    type User = import('./database.ts').User;
    type PlatformData = import('./model.ts').PlatformData;

    // Other Balsamiq types
    type Logger = import('@balsamiq/logging').Logger;

    // External types
    type PoolConnection = import('mysql').PoolConnection;
    type Pool = import('mysql').Pool;
    type MysqlError = import('mysql').MysqlError;
    type CloudWatch = import('@aws-sdk/client-cloudwatch').CloudWatch;

    type Response = import('express').Response;
    type Request = import('express').Request;
}

type BmprDump = any; // Consider using a more specific type
type BalsamiqArchive = any;
<<<<<<< HEAD
type AnyConnector =
    | import('./connectors/jira.js').JIRAConnector
    | import('./connectors/webdemo.ts').WebDemoConnector
    | import('./connectors/confluence.js').ConfluenceConnector
    | import('./connectors/cloud.js').CloudConnector
    | import('./connectors/jira-forge.js').JIRAForgeConnector;

declare module 'BalsamiqArchive';
declare module 'BalsamiqArchiveClient';
declare module 'BarManager';
declare module 'BmprConstants';
declare module 'BmprUtils';
declare module 'BalsamiqArchiveConstants' {
    export const Branch: {
        MasterBranchID: string;
        AllBranches: string;
        NoBranch: string;
    };

    export const Info: {
        SchemaVersion: string;
        ArchiveFormat: string;
        ArchiveRevision: string;
        ArchiveAttributes: string;
        ArchiveRevisionUUID: string;
    };

    export const ErrorCodes: {
        ArchiveNotOpen: string;
        UserUnknown: string;
        OutOfDate: string;
        CreateFailed: string;
        SchemaVersionNotSupported: string;
        NeedToMigrate: string;
        ArchiveNotLoaded: string;
        MaintenanceMode: string;
    };

    export const Role: {
        ROLE_NO_ACCESS: number;
        ROLE_VIEWER: number;
        ROLE_COMMENTER: number;
        ROLE_EDITOR: number;
        ROLE_ADMIN: number;
    };

    export const PermalinkKind: {
        image: string;
        public_share: string;
        image_unfurling: string;
        url_unfurling: string;
    };

    export const LikeStatus: {
        like: string;
        unlike: string;
    };

    export const defaultUserInfo: {
        name: string;
        avatarURL: string;
    };

    export const CommentAttributes: {
        ParentID: string;
        ReadBy: string;
        Timestamp: string;
        Trashed: string;
        TrashedBy: string;
        LikedBy: string;
        Timestamps: string;
        Imported: string;
        OriginalAuthor: string;
    };

    export const CommentAttributesSetters: {
        setReadStatus: string;
        setLikedStatus: string;
        setTrashedStatus: string;
        setTimestamps: string;
        deleteTimestamps: string;
        setParentID: string;
        setTimestamp: string;
        setImported: string;
        setOriginalAuthor: string;
    };

    export const CommentData: {
        Text: string;
        Callouts: string;
    };

    export const IMPORTED_USER_ID: string;

    export const BinaryDataType: {
        ARRAYBUFFER: string;
        BASE64: string;
    };

    export const SchemaVersions: {
        OneDotTwo: string;
        Two: string;
    };

    export const CurrentSchemaVersion: string;
}

declare module '@balsamiq/bmpr' {
    import BalsamiqArchive from 'BalsamiqArchive';
    import BalsamiqArchiveClient from 'BalsamiqArchiveClient';
    import BalsamiqArchiveConstants from 'BalsamiqArchiveConstants';
    import BarManager from 'BarManager';
    import BmprConstants from 'BmprConstants';
    import BmprUtils from 'BmprUtils';

    export { BalsamiqArchive, BalsamiqArchiveClient, BalsamiqArchiveConstants, BarManager, BmprConstants, BmprUtils };
}
=======
type ThumbnailInfo = any;
type BalsamiqArchiveAttributes = any;
>>>>>>> master

// The following modules in BMPR are missing types for some exported functions
declare module '@balsamiq/bmpr/lib/BmprUtils.js';
declare module '@balsamiq/bmpr/lib/BalsamiqArchive.js';
