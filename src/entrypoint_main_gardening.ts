import type { Logger } from '@balsamiq/logging';
import { makeAppContext } from './app-context.ts';
import { isBASLegacyErrorObject } from './calling-style.ts';
import type { SessionData, SessionManager } from './session-manager.ts';
import type { ServerUtils } from './server_utils.js';
import type { Clock } from './clock.ts';
import { acquireApplicationLockOnNewConnection } from './utils.ts';

const sessionCachedLimit = 24 * 60 * 60 * 1000; // 24h

export const mainGardeningJob = function (
    logger: Logger,
    sessionManager: SessionManager,
    serverUtils: ServerUtils,
    clock: Clock,
    dropZombieArchiveTimeoutInMs: number,
    onDone: () => void
) {
    /** @type {number} */
    let startingTaskTS = new Date().getTime();
    logger = logger.getLogger({ action: 'main-gardening', module: 'gar' });

    let logTaskDuration = function (jobName: string) {
        let now = new Date().getTime();
        let duration = (now - startingTaskTS) / 1000;
        startingTaskTS = now;
        logger.info('metric [' + jobName + '] duration ' + duration + ' secs');
    };

    let runJob = function (sessionData?: SessionData) {
        if (sessionData) {
            sessionManager.releaseSession(sessionData, function () {
                onDone();
            });
        }
    };

    sessionManager.createSession(logger, 'main-gardening', function (obj) {
        /** @type {JSDocTypes.SessionData} */
        let sessionData;

        /** @type {JSDocTypes.DBConnector} */
        let dbConnector;

        if (isBASLegacyErrorObject(obj)) {
            logger.error('unexpected error: ' + obj.error);
            runJob();
            return;
        }
        sessionData = obj;
        // add the log info to the current session in order to better trace gardening sub-tasks
        dbConnector = sessionData.dbConnector;
        // delete not consistent entries in the DB
        dbConnector.deleteArchiveZombieEntries(clock, function () {
            // unload zombie archive
            logTaskDuration('deleteArchiveZombieEntries');
            dbConnector.deleteSessionZombieEntries(function () {
                logTaskDuration('deleteSessionZombieEntries');
                dbConnector.dropZombieArchive(dropZombieArchiveTimeoutInMs, function () {
                    let ts = new Date().getTime() - sessionCachedLimit;
                    logTaskDuration('dropZombieArchive');
                    dbConnector.deleteZombieSession(ts, function () {
                        logTaskDuration('deleteZombieSession');
                        serverUtils.unloadUnusedArchiveJob(logger, sessionData, null, function () {
                            logTaskDuration('unloadUnusedArchiveJob');
                            runJob(sessionData);
                        });
                    });
                });
            });
        });
    });
};

async function main() {
    const metricsDelay = 5 * 1000; // Small delay for metrics to be sent to AWS CloudWatch.
    const initializationData = await makeAppContext();
    const logger = initializationData.logger.getLogger({ action: 'main-gardening', module: 'gar' });
    const config = initializationData.config;
    const metrics = initializationData.metrics;
    const sessionManager = initializationData.sessionManager;
    const serverUtils = initializationData.serverUtils;
    const clock = initializationData.clock;
    const mySqlDriverInstance = initializationData.mySqlDriverInstance;

    acquireApplicationLockOnNewConnection(mySqlDriverInstance, 'MAIN_GARDENING', function (obj) {
        if (isBASLegacyErrorObject(obj)) {
            logger.error(
                `Cannot acquire application lock MAIN_GARDENING: ${obj.error}`,
                typeof obj.error !== 'string' ? obj.error : undefined
            );
            process.exit(1);
        } else {
            logger.info(`Application lock MAIN_GARDENING acquired`);

            logger.info('Main gardening started');
            let timer = metrics.trackTimeInterval('main gardening duration');
            mainGardeningJob(logger, sessionManager, serverUtils, clock, 30000, function () {
                let elapsed = timer.stop();
                logger.info('Main gardening ended. Elapsed ' + (elapsed / 1000 / 60).toFixed(1) + ' min');

                if (!config.metricDisabled) {
                    metrics.putMetricData();
                    setTimeout(function () {
                        process.exit(0);
                    }, metricsDelay);
                } else {
                    process.exit(0);
                }
            });
        }
    });
}

// Start the main function if this file is run directly
if (import.meta.url === `file://${process.argv[1]}`) {
    main();
}
