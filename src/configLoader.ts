import type { BalsamiqEnvironmentAccessFactory, StringAccessor } from '@balsamiq/serverconfig/lib/environment-accessors.js';
import { parseRtcConfigSecrets, type RtcSecrets } from '@balsamiq/serverconfig/lib/rtc-config.js';
import { BalsamiqSecretsOnAWS, HardcodedBalsamiqSecrets, RDSCredentialsSecretOnAWS } from '@balsamiq/serverconfig/lib/secrets.js';
import { buildLocalConfig } from './config-local.ts';
import { buildECSConfig } from './config-ecs.ts';
import type { DataResidenciesConfig, DataResidencyName } from './environment-variables-schemas.ts';
import type { BucketResidencyProps } from './s3adapter.ts';

export interface RTCConfig {
    secrets: RtcSecrets | ReturnType<typeof parseRtcConfigSecrets>;
    websocketsUri: string | StringAccessor;
    environmentAccess?: BalsamiqEnvironmentAccessFactory;
    jwtCallbackInfo: {
        basBaseURL: string;
        secret: {
            type: string;
            username: string;
            password?: string;
            environment?: string;
        };
    };
}

export interface Config {
    port: number;
    clusterSize: number;
    mySQLConfig: {
        credentialsSecret:
            | {
                  getSecret: () => {
                      host: string;
                      username: string;
                      password: string;
                      port: number;
                  };
              }
            | RDSCredentialsSecretOnAWS;
        region: string;
        basDBName: string;
        permalinksDBName: string;
    };
    environmentName: string;
    archiveIDPrefix: string;
    appName: string;
    baseUrl: string;
    shareUrls: DataResidenciesConfig;
    defaultDataResidencyName: DataResidencyName;
    cloudBaseUrl: string;
    archivesPath: string;
    https: boolean;
    cloudTimeDeltaForSavingLiveProjectsInMinutes?: number;
    maxNumberOfProjectsToUnload?: number;
    metricRegion: string;
    cloudProjectsMaxAgeInDays: number;
    unloadUnusedArchivesForConnectors: string[];
    connectors: string[];
    confluenceNamespace: string;
    jiraNamespace: string;
    getRtcConfig: () => RTCConfig;
    getServerApiSecrets: () => HardcodedBalsamiqSecrets<unknown> | BalsamiqSecretsOnAWS<unknown>;
    metricNamespace: string;
    metricDisabled?: boolean;
    buildNumber: string;
    proxyConfig: {
        prefix: string;
        host: string;
        path: string;
    }[];
    redisURL: string;
    redisPort: number;
    redisDB: number;
    kmsService: {
        region: string;
        key_arn: string;
        environment: string;
    };
    i2wService: {
        url: string;
    };
    w2iService: {
        region: string;
        key_arn: string;
    };
    permalinkS3Storage: Record<DataResidencyName, BucketResidencyProps>;
    reduceLogging: boolean;
    loggerOutputForElasticSearch: boolean;
    cloudConfiguration: {
        jwtSecret: string;
        cloudServerBaseUrl: string;
        cloudBasicAuthCredentials: {
            username: string;
            password: string;
        };
    };
}

export interface ConfigLoaderResult {
    config: Config;
    environment: string;
}

export function loadConfig(): ConfigLoaderResult {
    if (!process.env.BAS_ENV) {
        throw new Error(`Couldn't figure out the environment. Did you set 'BAS_ENV' environment variable?`);
    }
    const environment = process.env.BAS_ENV;
    const config = buildConfigForEnvironment(environment);
    return { config, environment };
}

function buildConfigForEnvironment(environment: string): Config {
    if (environment === 'local') {
        return buildLocalConfig(true);
    } else {
        return buildECSConfig(true);
    }
}
