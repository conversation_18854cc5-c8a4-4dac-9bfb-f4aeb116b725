import { Metrics } from './metrics.ts';
import { SessionManager } from './session-manager.ts';
import { acquireApplicationLock, MySQLDriverPool } from './mysql-driver.ts';
import fs from 'fs';
import diskspace from 'diskspace';
import path from 'path';
import { BuildNumberTracker } from './track_build_number.ts';
import busboy from 'busboy';
import { RtcAdapter } from './rtc-adapter.ts';
import { callWithLegacyCallback } from './calling-style.ts';
import Consts from '@balsamiq/bmpr/lib/BalsamiqArchiveConstants.js';
import getFolderSize from 'get-folder-size';
import { object2sqliteBuffer, sqliteBuffer2object, walk_directory, getBASAuthorizationHeaderStringFactory, watchdog } from './utils.ts';


/** @typedef {ReturnType<makeServerUtils>} ServerUtils */

/**
 * Creates server utilities.
 * 
 * @param {Object} params - The parameters for creating server utilities.
 * @param {SessionManager} params.sessionManager - The session manager instance.
 * @param {MySQLDriverPool} params.mySqlDriverInstance - The MySQL driver instance.
 * @param {JSDocTypes.Logger} params.logger - The logger instance.
 * @param {Function} params.getConnector - The function to get the connector.
 * @param {JSDocTypes.Config} params.config - The configuration object.
 * @param {Metrics} params.metrics - The metrics instance.
 * @param {BuildNumberTracker} params.buildNumberTracker - The build number tracker instance.
 * @param {RtcAdapter} params.rtcAdapter - The RTC adapter instance.
 * @param {JSDocTypes.Clock} params.clock - The clock instance.
 */
function makeServerUtils({
    sessionManager,
    mySqlDriverInstance,
    logger,
    getConnector,
    config,
    metrics,
    buildNumberTracker,
    rtcAdapter,
    clock
}) {
    function broadcastRTCMessage(archiveID, archiveRevision, internalUserID, username, objToBroadcast, callback) {
       if (!archiveID) {
            // Safety check
            callback({ error: 'Null or undefined archiveID' });
        } else {
            let action = objToBroadcast && objToBroadcast.operation || "unknown";
            let logObj = {module: action, username: username, archiveID: archiveID};
            // logger.info("Broadcast message " + internalUserID + " " + archiveRevision, logObj);
            objToBroadcast.archiveRevision = archiveRevision;
            objToBroadcast.author = internalUserID;
            objToBroadcast.username = username;
            objToBroadcast.timestamp = new Date().getTime();

            rtcAdapter.sendMessage(archiveID, objToBroadcast);
            callback && callback({});
        }
    }

    let diskUsageTask = function(logger) {
        let percUsed, used;
        let oneMega = 1024*1024;
        // var oneGiga = oneMega*1024;
        let fiveMinutes = 5 * 60 * 1000;
        let absPath;
        logger = logger.getLogger({action: "disk usage", module: "gar"});

        // var convert2Giga = function(n) {
        //    return Math.round(n/oneGiga * 100) / 100;
        // };

        let convert2Mega = function(n) {
            return Math.round(n/oneMega * 100) / 100;
        };

        let cleanUpOlderFiles = function() {
            fs.readdir(config.archivesPath, function(err, files) {
                if (err) {
                    logger.error("[DF] archivesDiskUsed ERROR listing old files " + err, err);
                } else if (files) {
                    files.forEach(function(file) {
                        absPath = path.join(config.archivesPath, file);
                        (function(absPath) {
                            fs.stat(absPath, function(err, stat) {
                                let endTime, now;
                                if (err) {
                                    logger.error("[DF] archivesDiskUsed ERROR fs.stat " + file + " " + err);
                                } else {
                                    now = new Date().getTime();
                                    endTime = new Date(stat.ctime).getTime() + fiveMinutes;
                                    if (now > endTime) {
                                        fs.unlink(absPath, function(err) {
                                            if (err) {
                                                logger.error("[DF] archivesDiskUsed ERROR deleting old file " + absPath + " " + err);
                                            } else {
                                                logger.info("[DF] archivesDiskUsed deleted old file " + absPath + " size: " + convert2Mega(stat.size) + " MB ", {fileSize: stat.size});
                                            }
                                        });
                                    } else {
                                        logger.info("[DF] archivesDiskUsed skipping file: " + absPath + " size: " + convert2Mega(stat.size) + " MB " + (endTime - now)/1000 + " secs", {fileSize: stat.size});
                                    }
                                }
                            });
                        })(absPath);
                    });
                }
            });
        };

        let checkParentDir = function() {
            let parentDir = path.resolve(config.archivesPath, '..');

            walk_directory(parentDir, function (err, files) {
                if (err) {
                    logger.error("[DF] checkParentDir ERROR listing files in parent dir " + err);
                } else {
                    for (let { filePath, size } of files) {
                        logger.info("[DF] checkParentDir, listing file " + filePath + " size: " + convert2Mega(size) + " MB ");
                    }
                }
            });
        };

        logger.info("[DF] analysing memory usage " + (config.archivesPath || "config.archivesPath not defined"));

        diskspace.check('/etc/hosts', function (err, result)
        {
            if (err) {
                logger.error("[DF] ERROR " + err);
            } else {
                const { total, used } = result;
                percUsed = Math.round(used / total * 100);
                //logger.info("[DF] " + convert2Giga(total) + " GB " + convert2Giga(used) + " GB");
                //logger.info("[DF] disk used: " + percUsed + "%");
                metrics.addValue('diskUsed', percUsed, 'Percent');
            }
        });

        if (config.archivesPath) {
            if (!fs.existsSync(config.archivesPath)) {
                fs.mkdirSync(config.archivesPath);
            }
            logger.info("[DF] going to check shared memory usage " + config.archivesPath);
            diskspace.check(config.archivesPath, function(err, result)
            {
                if (err) {
                    logger.error("[DF] ERROR " + err);
                } else {
                    const { total, used, free } = result;
                    percUsed = Math.round(used / total * 100);
                    const tmpObj = {
                        totalMemory: total,
                        freeMemory: free,
                        percUsed: percUsed,
                    };
                    if (used || used === 0) {
                        logger.info("[DF] archivesDiskUsed total " + convert2Mega(total) + " MB used " + convert2Mega(used) + " MB", tmpObj);
                    } else {
                        logger.warn("[DF] unable to estimate shared memory usage", tmpObj);
                    }
                    //logger.info("[DF] disk used: " + percUsed + "%");
                    metrics.addValue('archivesDiskUsed', percUsed, 'Percent');
                }
            });

            getFolderSize.loose(config.archivesPath).then(function(size) {
                let sizeInMega = convert2Mega(size);
                if (!Number.isNaN(sizeInMega)) {
                    logger.info("[DF] archivesDiskUsed getFolderSize " + sizeInMega + ' MB', {folderSize: size});
                    metrics.addValue('archivesDiskSizeInMega', sizeInMega, 'Bytes');
                }
            });

            cleanUpOlderFiles();
            checkParentDir();
        }
    };

    function flushToConnectorHoldingTheTruth(logger, sessionData, archiveID, newPlatformArchiveName, kind, options, fromClose, callback) {
        options = options || {};
        let dbConnector = sessionData.dbConnector;
        logger = logger.getLogger({action: sessionData.action, sessionToken: sessionData.token, kind: kind, archiveID: archiveID});
        let unloadBar = options.unloadBar;

        // TODO: to be tested. WRITE lock should reduce race condition not decreasing performance
        let timer = metrics.trackTimeInterval('flushToConnectorHoldingTheTruth openBarLocked');
        sessionManager.openBarLocked(sessionData, archiveID, "WRITE", function (obj) {
            timer.stop();
            if (obj.error) {
                logger.error("flushing, failed to open the archive: " + obj.error);
                callback(obj);
            } else {
                // need a READ LOCK
                let bar = obj.bar;

                bar.getArchiveRevision(function(obj) {
                    let archiveRevision;
                    if (obj.error) {
                        logger.error("getArchiveRevision failed: " + obj.error);
                        sessionManager.unlockConnection(sessionData, function (/*objUnlock*/) {
                            callback(obj);
                        });
                        return;
                    }
                    archiveRevision = obj.archiveRevision;
                    dbConnector.getPlatformData(archiveID, function(obj) {
                        let platformInfo;
                        if (obj.error) {
                            logger.error("dbConnector.getPlatformData failed: " + obj.error);
                            sessionManager.unlockConnection(sessionData, function (/*objUnlock*/) {
                                callback(obj);
                            });
                            return;
                        }

                        // check if the archive is existent, if it doesn't exist getPlatformData returns {}
                        if (!obj.PLATFORM_ARCHIVE_ID) {
                            logger.warn("archive has been already deleted in the meantime");
                            sessionManager.unlockConnection(sessionData, function (/*objUnlock*/) {
                                callback({error: "archive is not loaded on BAS"});
                            });
                            return;
                        }
                        try {
                            platformInfo = obj.PLATFORM_INFO ? JSON.parse(obj.PLATFORM_INFO) : {};
                        } catch(err) {
                            logger.error("parsing failed: " + err);
                            sessionManager.unlockConnection(sessionData, function (/*objUnlock*/) {
                                callback(obj);
                            });
                            return;
                        }

                        function finalizeFlush() {
                            if (!options.force && platformInfo && platformInfo.archiveRevisionOnPlatform !== undefined && archiveRevision <= platformInfo.archiveRevisionOnPlatform) {
                                logger.info("flushToConnectorHoldingTheTruth archive is already updated");
                                sessionManager.unlockConnection(sessionData, function (/*objUnlock*/) {
                                    callback({wasAlreadyUpdated: true, platformArchiveID: obj.PLATFORM_ARCHIVE_ID});
                                });
                                return;
                            }

                            logger.info("flushToConnectorHoldingTheTruth going to flush the updated archive");

                            // archive need to be flushed on platform
                            // make the dump of all the branches
                            timer = metrics.trackTimeInterval('flushToConnectorHoldingTheTruth dump');
                            bar.dump(null, function (obj) {
                                timer.stop();
                                let dump;
                                if (obj.error) {
                                    logger.error("flushing, failed to create the dump");
                                    sessionManager.unlockConnection(sessionData, function (/*objUnlock*/) {
                                        callback(obj);
                                    });
                                } else {
                                    dump = obj.dump;
                                    dump.forceFlush = options.force;
                                    timer = metrics.trackTimeInterval('flushToConnectorHoldingTheTruth unlockConnection');
                                    sessionManager.unlockConnection(sessionData, function (/*objUnlock*/) {
                                        timer.stop();
                                        timer = metrics.trackTimeInterval('flushToConnectorHoldingTheTruth object2sqliteBuffer');
                                        object2sqliteBuffer(config, dump, function (obj) {
                                            timer.stop();
                                            if (obj.error) {
                                                logger.error("flushing, failed to create the buffer from db " + obj.error);
                                                callback(obj);
                                            } else {
                                                let internalUserID, username;
                                                let objToBroadcast, buffer = obj.buffer;

                                                if (sessionData.user) {
                                                    internalUserID = sessionData.user.INTERNAL_ID;
                                                    username = sessionData.user.USERNAME;
                                                }

                                                // begin transaction
                                                timer = metrics.trackTimeInterval('flushToConnectorHoldingTheTruth lock');
                                                dbConnector.lock("WRITE", function (obj) {
                                                    timer.stop();
                                                    if (obj.error) {
                                                        callback(obj);
                                                    } else {
                                                        objToBroadcast = {
                                                            operation: 'archiveIsGoingToBeFlushedOnPlatform',
                                                            ArchiveRevision: dump.Info.ArchiveRevision
                                                        };
                                                        internalUserID && broadcastRTCMessage(archiveID, dump.Info.ArchiveRevision, internalUserID, username, objToBroadcast, function (obj) {
                                                            if (obj.error) {
                                                                logger.error("flushing, failed to broadcast RTC archiveIsGoingToBeFlushedOnPlatform message for archive " + archiveID + " " + obj.error);
                                                            }
                                                        });
                                                        timer = metrics.trackTimeInterval('flushToConnectorHoldingTheTruth save');
                                                        getConnector(kind).save(logger, dbConnector, sessionData.user, archiveID, newPlatformArchiveName, dump.Info.ArchiveRevision, buffer, dump, { fromClose: fromClose, fromRestore: false, force: options.force }, function (obj) {
                                                            timer.stop();
                                                            if (obj.error) {
                                                                logger.warn("flushing, failed to save the database " + obj.error + " " + archiveID);
                                                                dbConnector.unlock(function (objLock) {
                                                                    if (objLock.error) {
                                                                        logger.error("flushing, failed to unlock the database " + objLock.error + " " + archiveID + " " + sessionData.user.USERNAME);
                                                                    }
                                                                    callback(obj);
                                                                });
                                                            } else {
                                                                // if save succeed, we set the WARNING_FLAG to 0 (i.e. revision saved on the platform is the last one, so we can unload the BAR on gardening)
                                                                timer = metrics.trackTimeInterval('flushToConnectorHoldingTheTruth updateArchiveWarningFlag');
                                                                logger.info("Going to updateArchiveWarningFlag " + obj.platformArchiveID);
                                                                dbConnector.updateArchiveWarningFlag(archiveID, 0, function (resWarningFlag) {
                                                                    timer.stop();
                                                                    // in some platform (e.g. JIRA), saving the archive produce a new platform archive ID
                                                                    let platformArchiveID = obj.platformArchiveID;
                                                                    let prevPlatformArchiveID = obj.prevPlatformArchiveID;

                                                                    if (resWarningFlag.error) {
                                                                        logger.error("flushing, failed to set WARNING_FLAG for archive " + archiveID);
                                                                    }

                                                                    // if the flush was successful, broadcast the good news
                                                                    if (obj.wasAlreadyUpdated) {
                                                                        logger.info("flushing, archive was already updated for archive " + archiveID);
                                                                    } else {
                                                                        objToBroadcast = {
                                                                            operation: 'archiveHasBeenFlushedOnPlatform',
                                                                            ArchiveRevision: dump.Info.ArchiveRevision,
                                                                            platformArchiveID: platformArchiveID
                                                                        };
                                                                        internalUserID && broadcastRTCMessage(archiveID, dump.Info.ArchiveRevision, internalUserID, username, objToBroadcast, function (obj) {
                                                                            if (obj.error) {
                                                                                logger.error("flushing, failed to broadcast RTC message for archive " + archiveID + " " + obj.error);
                                                                            }
                                                                        });
                                                                    }

                                                                    logger.info("Going to unlock " + platformArchiveID);
                                                                    dbConnector.unlock(function (objLock) {
                                                                        logger.info("Unlocked " + platformArchiveID);
                                                                        if (objLock.error) {
                                                                            logger.error("flushing, failed to unlock the database " + objLock.error);
                                                                            callback(obj);
                                                                        } else {
                                                                            let unloadFunction = function () {
                                                                                logger.info("Almost done " + platformArchiveID);
                                                                                if (unloadBar) {
                                                                                    logger.info("flushing, unload archive " + archiveID);
                                                                                    timer = metrics.trackTimeInterval('flushToConnectorHoldingTheTruth destroy');
                                                                                    bar.destroy(archiveID, function (obj) {
                                                                                        timer.stop();
                                                                                        if (obj.error) {
                                                                                            logger.warn("flushing, unable to destroy archive " + archiveID + " " + obj.error);
                                                                                            callback(obj);
                                                                                        } else {
                                                                                            logger.info("flushing, unloaded platform info for archive " + archiveID);
                                                                                            timer = metrics.trackTimeInterval('flushToConnectorHoldingTheTruth deleteArchivePlatformData');
                                                                                            dbConnector.deleteArchivePlatformData(archiveID, function (obj) {
                                                                                                timer.stop();
                                                                                                callback(obj);
                                                                                            });
                                                                                        }
                                                                                    });
                                                                                } else {
                                                                                    callback(obj);
                                                                                }
                                                                            };

                                                                            // in JIRA attachment cannot be updated
                                                                            if (prevPlatformArchiveID && platformArchiveID !== prevPlatformArchiveID) {
                                                                                objToBroadcast = {
                                                                                    operation: 'platformArchiveIDChanged',
                                                                                    ArchiveRevision: dump.Info.ArchiveRevision,
                                                                                    platformArchiveID: platformArchiveID
                                                                                };
                                                                                internalUserID && broadcastRTCMessage(archiveID, dump.Info.ArchiveRevision, internalUserID, username, objToBroadcast, function (obj) {
                                                                                    if (obj.error) {
                                                                                        logger.error("flushing, failed to broadcast RTC message for archive " + archiveID + " " + obj.error);
                                                                                    }
                                                                                    unloadFunction();
                                                                                });
                                                                            } else {
                                                                                // we do not need to broadcast any message
                                                                                unloadFunction();
                                                                            }
                                                                        }
                                                                    });
                                                                });
                                                            }
                                                        });
                                                    }
                                                });
                                            }
                                        });
                                    });
                                }
                            });
                        }

                        if (bar.purgeCommentsAndUsers && sessionData.user && fromClose) {
                            let internalUserID, username;
                            let objToBroadcast;

                            if (sessionData.user) {
                                internalUserID = sessionData.user.INTERNAL_ID;
                                username = sessionData.user.USERNAME;
                            }
                            bar.purgeCommentsAndUsers(function (res) {
                                if (res.error) {
                                    logger.error("error purging comments and users: " + res.error);
                                } else {
                                    if (res.purgedComments.length + res.purgedUsers.length > 0) {
                                        logger.info("purged comments: " + res.purgedComments.length + ", purged users: " + res.purgedUsers.length);
                                        objToBroadcast = {
                                            operation: 'commentsAndUsersPurged',
                                            ArchiveRevision: res.archiveRevision,
                                            purgedComments: res.purgedComments,
                                            purgedUsers: res.purgedUsers
                                        };
                                        internalUserID && broadcastRTCMessage(archiveID, res.ArchiveRevision, internalUserID, username, objToBroadcast, function (obj) {
                                            if (obj.error) {
                                                logger.error("flushing, failed to broadcast RTC message for archive " + archiveID + " " + obj.error);
                                            }
                                        });
                                    }
                                }
                                finalizeFlush();
                            });
                        } else {
                            finalizeFlush();
                        }
                    });
                });
            }
        });
    }


    function tryToSyncArchiveOnPlatformOffline(logger, sessionData, archiveID, kind, platformInfo, options, callback) {
        options = options || {};
        if (!getConnector(kind)) {
            callback && callback({error: "offline sync to platform failed: " + kind + " connector not found"});
            return;
        }
        // try to sync the archive on platform
        getConnector(kind).getAuthTokenFromPlatform(logger, platformInfo, function(obj) {
            if (obj.error) {
                callback && callback({error: "offline sync to platform failed: " + kind + " error: " + obj.error, url: obj.url});
            } else {
                // some platform will return the user object needed to impersonate the action
                // otherwise we bubble up the platform token
                sessionData.user = obj.user || {
                    PLATFORM_TOKEN: obj.platformToken
                };

                // flushToConnectorHoldingTheTruth(sessionData, archiveID, newPlatformArchiveName, kind, options, fromClose, callback)
                flushToConnectorHoldingTheTruth(logger, sessionData, archiveID, null, kind, {force: options.force}, false, function (obj) {
                    callback && callback(obj);
                });
            }
        });
    }


    let getLoadedProjectStats = function(logger, dbConnector, warningFlag) {
        warningFlag = warningFlag || 0;
        logger = logger.getLogger({action: "getLoadedProjectStats", module: "gar"});
        // logger.info("Start getLoadedProjectStats");
        dbConnector.getLoadedArchiveForKind(warningFlag, function (obj) {
            let keys, kind, i, metricName, tot = 0;
            if (obj.error) {
                logger.info("ERROR : getLoadedProjectStats " + obj.error);
            } else {
                keys = Object.keys(obj);
                for (i=0; i<keys.length; i++) {
                    kind = keys[i];
                    metricName = 'projects-loaded-' + kind + (warningFlag ? "-not-synced" : "");
                    logger.info("Actual" + (warningFlag ? " NOT SYNCED " : " ") + "loaded projects for kind " + kind + " " + obj[kind]);
                    metrics.addValue(metricName, obj[kind], 'Count');
                    tot += obj[kind];
                }
                metricName = 'projects-loaded' + (warningFlag ? "-not-synced" : "");
                logger.info("Actual TOT" + (warningFlag ? " NOT SYNCED " : " ") + "loaded projects " + tot);
                metrics.addValue(metricName, tot, 'Count');
            }
        })
    };

    function unloadUnusedArchiveJob(logger, sessionData, options, callback) {
        options = options || {};
        let MAX_UNLOADING_BULK = config.maxNumberOfProjectsToUnload || 5000;
        let DELAYED_UNLOAD_TIMEOUT = 1000; // 1 second
        let dbConnector = sessionData.dbConnector;
        let i, k;
        let connectorsList = options.connectors ? options.connectors : config.unloadUnusedArchivesForConnectors;
        let archiveIDsList = [], contentData = {};
        logger = logger.getLogger({action: "unloadUnusedArchiveJob", module: "gar"});

        getLoadedProjectStats(logger, dbConnector);
        getLoadedProjectStats(logger, dbConnector, 1);

        // push metrics
        metrics.putMetricData();

        logger.info("looking for unused archives in connectors: " + connectorsList.join(', '));
        for (k=0; k<connectorsList.length; k++) {
            (function(kind) {
                let now = (new Date()).getTime(), warningFlag = 0;
                let max_timestamp = now - getConnector(kind).maxAge();

                contentData[kind] = {status: "onprogress"};
                dbConnector.selectPlatformDataToUnload(kind, max_timestamp, warningFlag, function (obj) {
                    if (obj.error) {
                        logger.error("unloading old archive data: " + obj.error);
                        contentData[kind] = {status: "error", error: obj.error};
                    } else {
                        logger.info("found " + obj.length + " potential archives to unload of kind " + kind);
                        for (i = 0; i < obj.length; i++) {
                            archiveIDsList.push(obj[i].BAS_ARCHIVE_ID);
                        }
                        contentData[kind] = {status: "success"};
                    }
                });
            })(connectorsList[k]);
        }

        watchdog(contentData, function() {
            let totalNumber = archiveIDsList.length;
            let actualNumber;
            metrics.addValue('projects-to-unload', totalNumber, 'Count');
            if (options.onlyDoArchiveID) {
                archiveIDsList = archiveIDsList.filter(function (archiveID) { return archiveID === options.onlyDoArchiveID; });
            }
            archiveIDsList = archiveIDsList.slice(0, options.maxBulkSize || MAX_UNLOADING_BULK);
            if (options.debugPrint) {
                options.debugPrint('' + archiveIDsList.length + ' archives to process');
                for (let i=0; i<archiveIDsList.length; i+=1) {
                    options.debugPrint(archiveIDsList[i]);
                }
            }
            if (options.skipProcessing) {
                callback({});
                return;
            }
            actualNumber = archiveIDsList.length;
            logger.info("Unloading " + actualNumber + " archives out of " + totalNumber);
            unloadMultipleArchives(logger, sessionData, archiveIDsList, options, DELAYED_UNLOAD_TIMEOUT, function(obj) {
                if (actualNumber) {
                    logger.info("[DONE] Unloaded " + (actualNumber - archiveIDsList.length) + " archives out of " + totalNumber);
                } else {
                    logger.info("[DONE] Nothing to unload, enjoying free time");
                }
                callback(obj);
            });
        });
    }

    function unloadMultipleArchives(logger, sessionData, archiveIDsList, options, delayedTimeout, callback) {
        let archiveID;
        logger = logger.getLogger({action: "unloadMultipleArchives", module: "gar"});

        if (archiveIDsList.length === 0) {
            logger.info("unloadMultipleArchives finished");
            callback({});
        } else {
            logger.info("unloadMultipleArchives still " + archiveIDsList.length + " archives to unload");
            archiveID = archiveIDsList.pop();
            unloadSingleArchive(logger, sessionData, archiveID, options, function (obj) {
                if (obj && obj.wasAlreadyUpdated) {
                    logger.info("unloadMultipleArchives archive was already updated, skip the timeout");
                    unloadMultipleArchives(logger, sessionData, archiveIDsList, options, delayedTimeout, callback);
                } else {
                    setTimeout(function() {
                        unloadMultipleArchives(logger, sessionData, archiveIDsList, options, delayedTimeout, callback);
                    }, delayedTimeout);
                }
            });
        }
    }


    function unloadSingleArchive(logger, sessionData, archiveID, options, callback) {
        options = options || {};

        let dbConnector = sessionData.dbConnector, archiveRevisionOnPlatform, now;
        logger = logger.getLogger({action: "unloadSingleArchive", module: "gar"});
        logger.info("Unloading single archive " + archiveID);

        let finalise = function(obj) {
            // release session
            sessionManager.unlockConnection(sessionData, function (objUnlock) {
                if (objUnlock.error) {
                    logger.error("unlocking connection error " + objUnlock.error);
                }

                if (obj.error) {
                    logger.warn("unloading archive error " + archiveID + " " + obj.error);
                } else {
                    let delta = (new Date()).getTime() - now;
                    logger.info("successfully unloaded archive in " + delta  + " msec: " + archiveID);
                }

                if (objUnlock.error) {
                    callback(objUnlock);
                } else {
                    callback(obj);
                }
            });
        };

        let setWarningFlagAndFinalise = function(obj, message) {
            if (options.skipSettingWarningFlag) {
                finalise(obj);
                return;
            }
            logger.error('Setting warning flag for archive ' + archiveID + '. Reason: ' + message + '. Error: ' + obj.error);
            dbConnector.updateArchiveWarningFlag(archiveID, 1, function(updateObj) {
                if (updateObj.error) {
                    logger.error("not able to set the warning flag on not updated archive: " + archiveID);
                    finalise(updateObj);
                } else {
                    finalise(obj);
                }
            });
        };

        let tryToForceSavingAndFinalise = function(kind, platformInfo) {
            tryToSyncArchiveOnPlatformOffline(logger, sessionData, archiveID, kind, platformInfo, { force: true }, function(obj) {
                if (obj.error) {
                    setWarningFlagAndFinalise(obj, "not able to unload archive to platform. archiveRevisionOnPlatform: " + archiveRevisionOnPlatform);
                } else {
                    logger.info("archive synced offline: " + archiveID);
                    finalise(obj);
                }
            });
        };

        now = (new Date()).getTime();
        // lock the BAR and the PLATFORM_INFO database
        sessionManager.openBarLockedExt(sessionData, archiveID, "WRITE", true, function (obj) {
            let platformSiteID, platformArchiveID, kind, platformInfo;

            if (obj.error) {
                setWarningFlagAndFinalise(obj, 'Error in openBarLockedExt');
                return;
            }

            let bar = obj.bar;
            bar.getArchiveRevision(function (obj) {
                let revision;
                if (obj.error) {
                    setWarningFlagAndFinalise(obj, 'Error in bar.getArchiveRevision');
                    return;
                }
                revision = obj.archiveRevision;
                dbConnector.getPlatformData(archiveID, function(obj) {
                    if (obj.error) {
                        setWarningFlagAndFinalise(obj, 'Error in dbConnector.getPlatformData');
                        return;
                    }

                    try {
                        platformInfo = obj.PLATFORM_INFO ? JSON.parse(obj.PLATFORM_INFO) : {};
                    } catch(e) {
                        logger.warn('unloading single archive: unexpected exception parsing PLATFORM_INFO: ' + obj.PLATFORM_INFO);
                    }

                    kind = obj.PLATFORM_KIND;
                    platformSiteID = obj.PLATFORM_SITE_ID;
                    platformArchiveID = obj.PLATFORM_ARCHIVE_ID;
                    logger.updateParams({
                        kind: kind,
                        platformSiteID: platformSiteID,
                        platformArchiveID: platformArchiveID,
                        archiveID: archiveID,
                    })
                    archiveRevisionOnPlatform = platformInfo.archiveRevisionOnPlatform;

                    if (revision !== archiveRevisionOnPlatform) {
                        logger.info('unloading single archive: tryToSyncArchiveOnPlatformOffline, archiveRevisionOnPlatform ' + archiveRevisionOnPlatform + " revision " + revision);
                        tryToForceSavingAndFinalise(kind, platformInfo);
                        // we can try to delete the archive here in order to not have to wait another 30 days
                    } else {
                        if (!getConnector(kind)) {
                            finalise({}); // kind not supported
                            return;
                        }
                        getConnector(kind).projectHasBeenSavedOnPlatform(logger, dbConnector, platformSiteID, platformArchiveID, function(obj) {
                            if (obj.error) {
                                // project has been deleted from Cloud, we flag the project it will be unloaded by Cloud gardening
                                setWarningFlagAndFinalise(obj, 'Error in projectHasBeenSavedOnPlatform');
                                return;
                            }
                            if (obj.status !== true) {
                                // project has not been saved (i.e. no snapshot in Cloud) , we force the saving
                                logger.info('unloading single archive: archive DOES NOT exist on platform, tryToSyncArchiveOnPlatformOffline');
                                tryToForceSavingAndFinalise(kind, platformInfo);
                            } else {
                                // project has been saved and is synced, we can safely unload it from BAS
                                logger.info('unloading single archive: archive exists on platform');
                                if (options.skipDelete) {
                                    finalise({});
                                    return;
                                }
                                dbConnector.deleteArchivePlatformData(archiveID, function (obj) {
                                    if (obj.error) {
                                        setWarningFlagAndFinalise(obj, 'Error in deleteArchivePlatformData');
                                    } else {
                                        bar.destroyExt(archiveID, true, function (obj) {
                                            logger.info('unloading single archive: archive unloaded from BAS');
                                            // archive has been already synced on platform, setting "wasAlreadyUpdated" property will avoid the not needed timeout
                                            obj.wasAlreadyUpdated = true;
                                            finalise(obj);
                                        });
                                    }
                                });
                            }
                        });
                    }
                });
            });
        });
    }

    return {
        flushToConnectorHoldingTheTruth,
        tryToSyncArchiveOnPlatformOffline,
        unloadUnusedArchiveJob,
        unloadSingleArchive,
        diskUsageTask,
        broadcastRTCMessage,
        getBASAuthorizationHeaderString: getBASAuthorizationHeaderStringFactory(config),
    };
}

export {
    makeServerUtils,
};
