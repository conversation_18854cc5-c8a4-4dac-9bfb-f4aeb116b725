/** @noformat */
import {
    getUUID,
    isWebUri,
    isImage,
    object2sqliteBuffer,
    stripThumbnailImageFromDump,
    sqliteBuffer2object,
    isAnonymousUser,
} from './utils.ts';
import superagent from 'superagent';
import ip from 'ip';
import sax from 'sax';
import express from 'express';
import cors from 'cors';
import hsts from 'hsts';
import bodyParser from 'body-parser';
import bodyParserErrorHandler from 'express-body-parser-error-handler';
import compression from 'compression';
import proxy from 'express-http-proxy';
import path from 'path';
import xss from 'xss';
import * as rfc5987 from './rfc-5987-encoding.ts';
import con from './constants.ts';
import { basRequestHandler, basMiddleware, globalErrorHandler, createApiAuthPipeline, makeHandler, checkValidUser } from './middlewares.ts';
import { registerBlockListAPI } from './blocklist.ts';
import { markPermalinkAsOutdated } from './permalinks.js';
import { deletePermalinksByArchiveID } from './permalinks.js';
import { deletePermalinksByResourceIDs } from './permalinks.js';
import { updatePermalinkImages } from './permalinks.js';
import { callSaneFunctionFromLegacy } from './calling-style.ts';
import { getValidUserForRole as sane_getValidUserForRole } from './sane-helpers.js';
import { parseProductInfoFromHeaders } from '@balsamiq/saas-utils';
import Consts from '@balsamiq/bmpr/lib/BalsamiqArchiveConstants.js';
import { parseMultipartFormData, createBARFromBuffer } from './utils.ts';
import { augmentRequestObject, assertBASRequest, isBASRequest } from './request-context.ts';

import { createSnapshot } from './operations/create-snapshot.ts';
import { getSnapshot } from './operations/get-snapshot.ts';
import { getResourceData } from './operations/get-resource-data.js';
import { getUserAuthToken } from './operations/get-user-auth-token.js';
import { getUsersList } from './operations/get-users-list.js';
import { setPermalink } from './operations/set-permalink.ts';
import { deletePermalinkData } from './operations/delete-permalink-data.ts';
import { getPermalinkData } from './operations/get-permalink-data.ts';
import { getPermalinksData } from './operations/get-permalinks-data.ts';
import { createOrUpdateImageLink } from './operations/create-or-update-permalink.ts';
import { getPublicShare } from './operations/get-public-share.ts';
import { getPermalink } from './operations/get-permalink.ts';
import { usersLeftArchive } from './operations/users-left-archive.ts';
import { getRtcAuthToken } from './operations/get-rtc-auth-token.ts';
import { health } from './operations/health.ts';
import { restore } from './operations/restore.js';

/**
 *
 * @param {JSDocTypes.AppContext} appContext
 * @returns {express.Application}
 */
export function createApp(appContext) {
    const {
        config,
        metrics,
        sessionManager,
        connectors,
        getConnector,
        clock,
        roleForAccessTable,
        bmprUtilsMod,
        serverUtils,
        rateLimiterConfiguration,
        blockList,
        redisAdapter,
        buildNumberTracker,
        srcDirName,
    } = appContext;

    const app = express();

    // Middlewares configuration
    app.disable('x-powered-by');
    app.set('trust proxy', true); // https://expressjs.com/en/api.html - "Application Settings" section - Needed to have a meaningful "req.ip" value
    app.set('port', config.port);

    app.use(function (_req, res, next) {
        res.setHeader('Referrer-Policy', 'origin');
        next();
    });

    app.use(function (req, res, next) {
        augmentRequestObject(req, res, appContext);
        req.bas.notifyBASRequest?.(req);
        next();
    });

    app.use(
        cors({
            exposedHeaders: ['x-balsamiq-archive-revision'],
            maxAge: 86400,
        })
    );

    app.use(bodyParser.urlencoded({ limit: '100mb', extended: true }));

    app.use(bodyParser.json({ limit: '100mb' }));

    app.use(
        bodyParserErrorHandler({
            onError: (err, req) => {
                const message = err.toString();
                if (isBASRequest(req)) {
                    req.bas.logger.info(message);
                } else {
                    console.log(message);
                }
            },
        })
    );

    app.use(function (req, res, next) {
        try {
            decodeURIComponent(req.path);
        } catch (err) {
            if (isBASRequest(req)) {
                req.bas.logger.securityWarning(`${err.message} + ${req.path}`, 'DECODE_URI_COMPONENT_HANDLER_ERROR', err);
            }
            res.status(400).json({ error: 'Bad Request' });
            return;
        }
        next();
    });

    app.use(
        hsts({
            maxAge: 31536000, // Must be at least 1 year to be approved
            includeSubDomains: true, // Must be enabled to be approved
        })
    );

    app.use('/static', express.static(path.join(srcDirName, 'static'), { maxAge: 1000 * 60 * 60 * 24 * 365 })); // one year in milliseconds

    app.use(compression());

    // Gather build number information from the request headers and collects metrics
    app.use(basMiddleware(buildNumberTracker.asyncMiddleware.bind(buildNumberTracker)));

    app.use(function (req, _res, next) {
        // Log headers coming from BWD requests
        assertBASRequest(req);
        // NOTE: in theory it would be nice to log each and every request.
        // In practise, as of today, we don't have enough capacity in LBC
        // to ingest such a volume of logs, so we limit the log to
        // BWD only, to build the statistics that we need.
        if (
            req.headers['user-agent'] &&
            req.headers['user-agent'].startsWith('Balsamiq Wireframes') &&
            req.path !== '/getRTCAuthToken' &&
            req.path !== '/getUsersList' &&
            req.path !== '/health' &&
            req.path !== '/getArchiveRevision' &&
            req.path !== '/broadcastMessage'
        ) {
            req.bas.logger.info(req.method + ' ' + req.path + '. Start request', {
                action: 'root',
                userAgent: { header: req.headers['user-agent'] },
            });
        }
        next();
    });

    // Setup bas proxying
    config.proxyConfig.forEach(function (route) {
        app.use(
            route.prefix,
            proxy(route.host, {
                https: true,
                proxyReqPathResolver: function (req) {
                    return route.path + (req.url.startsWith('/') ? req.url.substring(1) : req.url);
                },
            })
        );
    });

    var flushToConnectorHoldingTheTruth = serverUtils.flushToConnectorHoldingTheTruth;
    var tryToSyncArchiveOnPlatformOffline = serverUtils.tryToSyncArchiveOnPlatformOffline;
    var unloadSingleArchive = serverUtils.unloadSingleArchive;
    var broadcastRTCMessage = serverUtils.broadcastRTCMessage;

    // NOTE: Middleware configuration is done in configureMiddlewares function.
    // Consider moving the function back here once app.js has been translated to TypeScript.
    registerBlockListAPI(app);

    // Initialize HTTP entry points for connectors
    app.get('/jira/endpoint/:endpoint', (p_req, p_res) => connectors.jira.endPoint(p_req, p_res));
    app.get('/jira/:api', (p_req, p_res) => connectors.jira.directAPICall("GET", p_req, p_res));
    app.post('/jira/:api', (p_req, p_res) => connectors.jira.directAPICall("POST", p_req, p_res));

    app.get('/confluence/endpoint/:endpoint', (p_req, p_res) => connectors.confluence.endPoint(p_req, p_res));
    app.get('/confluence/:api', (p_req, p_res) => connectors.confluence.directAPICall("GET", p_req, p_res));
    app.post('/confluence/:api', (p_req, p_res) => connectors.confluence.directAPICall("POST", p_req, p_res));

    app.get('/wd/:api', (p_req, p_res) => connectors.webDemo.directAPICall("GET", p_req, p_res));
    app.post('/wd/:api', (p_req, p_res) => connectors.webDemo.directAPICall("POST", p_req, p_res));

    app.get('/cloud/:api', (p_req, p_res) => connectors.cloud.directAPICall("GET", p_req, p_res));
    app.post('/cloud/:api', (p_req, p_res) => connectors.cloud.directAPICall("POST", p_req, p_res));

    // Permalinks
    app.post(`/setPermalinkData`, basRequestHandler(setPermalink));
    app.post(`/permalink`, basRequestHandler(setPermalink));  // Deprecated. Use setPermalinkData
    app.post(`/deletePermalinkData`, basRequestHandler(deletePermalinkData));
    app.get(`/getPermalinkData`, basRequestHandler(getPermalinkData));
    app.get(`/getPermalinksData`, basRequestHandler(getPermalinksData));
    app.post(`/createOrUpdateImageLink`, basRequestHandler(createOrUpdateImageLink));
    app.get('/ps/:id', basRequestHandler(getPublicShare));
    app.get('/[p,c,j,a,g]/:id', basRequestHandler(getPermalink));
    app.get(`/permalink`, basRequestHandler(getPermalink));

    app.get(`/getResourceData`, basRequestHandler(getResourceData));
    app.post(`/getUserAuthToken`, basRequestHandler(getUserAuthToken));
    app.get(`/getUsersList`, basRequestHandler(getUsersList));
    app.get(`/health`, basRequestHandler(health));

    app.get('/slack/:id', basRequestHandler(getSnapshot));
    app.post(`/createSnapshot`,basRequestHandler(createSnapshot));

    app.post(`/usersLeftArchive`, basRequestHandler(usersLeftArchive));
    app.get(`/getRTCAuthToken`, basRequestHandler(getRtcAuthToken));
    app.post(`/restore`, basRequestHandler(restore));

    // bas20-staging google verification
    app.get('/googlea1dc77e22fef8e3a.html', function(req, res) {
        assertBASRequest(req);
        var str = 'google-site-verification: googlea1dc77e22fef8e3a.html';

        req.bas.logger.info("Google verification " + str);

        res.setHeader('Content-Type', 'text/xml');
        res.setHeader('Content-Length', Buffer.byteLength(str));
        res.write(str);
        res.end();
    });

    app.get('/crossdomain.xml', function (req, res) {
        assertBASRequest(req);
        let domain = '*';
        try {
            const refererURL = new URL(req.headers.referer);
            domain = refererURL.host;
        } catch(e) {
            // ignore
        }

        var str = '';
        str += '<?xml version="1.0" encoding="utf-8" ?>\n' +
            '<!DOCTYPE cross-domain-policy SYSTEM "http://www.adobe.com/xml/dtds/cross-domain-policy.dtd">\n' +
            '<cross-domain-policy>\n' +
            '   <site-control permitted-cross-domain-policies="master-only"/>\n' +
            '   <allow-access-from domain="' + domain + '" secure="'+((config.https == true) ? "true" : "false")+'"/>\n' +
            '</cross-domain-policy>\n';

        req.bas.logger.info("Returning crossdomain.xml to " + domain + ": " + str);

        res.setHeader('Content-Type', 'text/xml');
        res.setHeader('Content-Length', Buffer.byteLength(str));
        res.write(str);
        res.end();
    });

    // Response time metric
    // TODO how to use for different endpoints
    app.post('/'+con.API_OPEN, function (req, res, next) {
        var start = Date.now();
        var jsonObj = req.body;
        var kind = jsonObj ? jsonObj.kind : "unknown";
        metrics.addValue('open-count-' + kind, 1, 'Count');
        res.on('finish', function () {
            metrics.addValue('responseTime open-' + kind, Date.now() - start, 'Milliseconds');
        });
        next();
    });

    app.post('/'+con.API_CREATE, function (req, res, next) {
        var start = Date.now();
        if (req.headers['content-type'] && req.headers['content-type'].startsWith('multipart/form-data')) {
            res.on('finish', function () {
                metrics.addValue('responseTime upload', Date.now() - start, 'Milliseconds');
                if (req.startProcessing) {
                    metrics.addValue('processTime upload', Date.now() - req.startProcessing, 'Milliseconds');
                }
            });
        } else {
            res.on('finish', function () {
                metrics.addValue('responseTime create', Date.now() - start, 'Milliseconds');
            });
        }
        next();
    });

    app.get('/'+con.API_FETCH, function (req, res, next) {
        var start = Date.now();
        res.on('finish', function () {
            metrics.addValue('responseTime fetch', Date.now() - start, 'Milliseconds');
        });
        next();
    });
    function closeFinishHandler (req, res, next) {
        var start = Date.now();
        res.on('finish', function () {
            var kind = req.bas_kind;
            var metricName = kind ? 'responseTime close-' + kind : 'responseTime close';
            metrics.addValue(metricName, Date.now() - start, 'Milliseconds');
        });
        next();
    }
    app.get('/'+con.API_CLOSE, closeFinishHandler);
    app.post('/'+con.API_CLOSE, closeFinishHandler);
    app.get('/'+con.API_FLUSH, function (req, res, next) {
        var start = Date.now();
        res.on('finish', function () {
            var kind = req.bas_kind;
            var metricName = kind ? 'responseTime flush-' + kind : 'responseTime flush';
            metrics.addValue(metricName, Date.now() - start, 'Milliseconds');
        });
        next();
    });
    app.post('/'+con.API_GETRESOURCESDATA, function (req, res, next) {
        var start = Date.now();
        res.on('finish', function () {
            metrics.addValue('responseTime getResourcesData', Date.now() - start, 'Milliseconds');
        });
        next();
    });


    // HTTP ENDPOINTS
    // -----------------------------------------------------------------

    app.get('/', function (req, res) {
        res.redirect("https://balsamiq.com");
    });

    app.get('/'+con.API_DOWNLOAD, function (req, res) {
        var token = req.query.token;
        var filename = req.query.filename ? req.query.filename : "Project.bmpr";
        var archiveID = req.query.archiveID;
        var platformArchiveID = req.query.platformArchiveID;
        var platformSiteID = req.query.platformSiteID || "";
        // var platformKind = req.query.platformKind;

        let logger = req.bas.logger.getLogger({action: "download", sessionToken: token, archiveID: archiveID});
        logger.info("start downloading");

        var sendStatusError = function(code, message) {
            logger.error(message);
            res.status(code).send(message);
        };

        var finalise = function(sessionData, obj) {
            // release session
            sessionManager.releaseSession(sessionData, function(sessionObj) {
                var contentDisposition;
                if (sessionObj.error) {
                    // we log error but we try to complete the call
                    logger.error("sessionManager.releaseSession failed " + sessionObj.error);
                }

                if (obj.error) {
                    sendStatusError(obj.code, obj.error);
                } else {
                    try {
                        contentDisposition = "attachment; filename*=" + rfc5987.encode(filename);
                        res.setHeader('Content-Disposition', contentDisposition);
                    } catch (e) {
                        logger.warn(e.message);
                        filename = "Project.bmpr";
                        res.setHeader('Content-Disposition', 'attachment; filename="' + filename + '"');
                    }
                    res.setHeader('Content-Type', 'application/octet-stream');
                    res.setHeader('Content-Length', obj.buffer.length);
                    res.write(obj.buffer);
                    res.end();
                }
            });
        };

        var doTheJob = function(sessionData, archiveID) {
            sessionManager.openBarLocked(sessionData, archiveID, "READ", function (obj) {
                var bar;
                if (obj.error) {
                    finalise(sessionData, {error: obj.error, code: 404});
                } else {
                    // need a READ LOCK
                    bar = obj.bar;
                    // make the dump of all the branches
                    bar.dump(null, function (obj) {
                        var dump;
                        if (obj.error) {
                            finalise(sessionData, {error: obj.error, code: 404});
                        } else {
                            dump = obj.dump;
                            sessionManager.unlockConnection(sessionData, function (/*objUnlock*/) {
                                object2sqliteBuffer(config, dump, function (obj) {
                                    if (obj.error) {
                                        finalise(sessionData, {error: obj.error, code: 404});
                                    } else {
                                        finalise(sessionData, obj);
                                    }
                                });
                            });
                        }
                    });
                }
            });
        };

        if (token) {
            checkValidUser(con.API_DOWNLOAD, req, res, function (sessionData) {
                archiveID = sessionData.user.ARCHIVE_ID;
                sessionManager.openBarLocked(sessionData, archiveID, "READ", function (obj) {
                    if (obj.error) {
                        finalise(sessionData, {error: obj.error, code: 404});
                    }
                    else {
                        // need a READ LOCK
                        var bar = obj.bar;
                        // make the dump of all the branches
                        bar.dump(null, function (obj) {
                            var dump;
                            if (obj.error) {
                                finalise(sessionData, {error: obj.error, code: 404});
                            }
                            else {
                                dump = obj.dump;
                                sessionManager.unlockConnection(sessionData, function (/*objUnlock*/) {
                                    object2sqliteBuffer(config, dump, function (obj) {
                                        if (obj.error) {
                                            finalise(sessionData, {error: obj.error, code: 404});
                                        } else {
                                            finalise(sessionData, obj);
                                        }
                                    });
                                });
                            }
                        });
                    }
                });
            });
        } else {
            req.bas.verifyAdminCredentials()
                .then(allowed => {
                    if (allowed) {
                        if (archiveID) {
                            sessionManager.createSession(logger, "download", function (obj) {
                                var sessionData;
                                if (obj.error) {
                                    sendStatusError(501, obj.error);
                                } else {
                                    sessionData = obj;
                                    doTheJob(sessionData, archiveID);
                                }
                            });
                        } else if (platformArchiveID) { // Google Drive has no platformSiteID
                            sessionManager.createSession(logger, "download", function (obj) {
                                var sessionData;
                                if (obj.error) {
                                    sendStatusError(501, obj.error);
                                } else {
                                    sessionData = obj;
                                    sessionData.dbConnector.getBASArchiveID(platformSiteID, platformArchiveID, function (obj) {
                                        if (obj.error) {
                                            sendStatusError(501, obj.error);
                                        } else {
                                            if (obj.BAS_ARCHIVE_ID) {
                                                archiveID = obj.BAS_ARCHIVE_ID;
                                                doTheJob(sessionData, archiveID);
                                            } else {
                                                sendStatusError(501, "Not Found");
                                            }
                                        }
                                    });
                                }
                            });
                        } else {
                            sendStatusError(404, "Wrong parameters");
                        }
                    } else {
                        sendStatusError(401, "Not Authenticated");
                    }
                })
                .catch(err => {
                    req.bas.logger.error('Unexpected error while checking server API credentials', err);
                    sendStatusError(500, "Unexpected error while downloading");
                });
        }
    });

    app.get('/'+con.API_FETCH, function (req, res) {
        var token = req.query.token;
        var url = req.query.url;
        var GENERIC_ERROR = 'Unexpected error: unable to fetch the requested url';
        req.bas.addLoggerInfo({action: "fetch", sessionToken: token});
        let contentBody, contentType, contentLength;

        req.bas.logger.info("fetching " + url);

        var sendStatusError = function(code, message) {
            req.bas.logger.warn(message + " error code " + code);
            res.status(code).send(GENERIC_ERROR);
        };

        var getMimetypeFromSignature = function(signature) {
            switch (signature) {
                case '89504E47':
                    return {contentType: 'image/png', result: true};
                case '47494638':
                    return {contentType:'image/gif', result: true};
                case '25504446':
                    return {contentType:'application/pdf', result: false};
                case 'FFD8FFDB':
                case 'FFD8FFE0':
                case 'FFD8FFE1':
                    return {contentType:'image/jpeg', result: true};
                case '504B0304':
                    return {contentType:'application/zip', result: true};
                case '53514C69':
                    return {contentType:'application/sqlite', result: true};
                default:
                    return {contentType:'Unknown filetype', result: false};
            }
        };

        var isSafeUrl = function(inputUrl, callback) {
            let url;
            try {
                url = new URL(inputUrl)
            } catch(e) {
                return callback({error: "Invalid URL"});
            }
            var isPrivateHostname = function(hostname) {
                var privateHostname = [
                    "localhost"
                ];
                return privateHostname.indexOf(hostname) > -1;
            };
            var isPrivateIP = function(ipaddr) {
                var privateCIDRs = [
                    '0.0.0.0/8',
                    '10.0.0.0/8',
                    '**********/10',
                    '*********/8',
                    '***********/16',
                    '**********/12',
                    '*********/29',
                    '***********/31',
                    '*********/24',
                    '***********/24',
                    '***********/16',
                    '**********/15',
                    '************/24',
                    '***********/24',
                    '*********/4',
                    '240.0.0.0/4',
                    '***************/32'
                ].map(ip.cidrSubnet);
                var validateIPaddress = function(ipaddr)
                {
                    var ret = false;
                    if (/^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/.test(ipaddr))
                    {
                        ret = true;
                    }
                    return ret;
                };
                return validateIPaddress(ipaddr) && privateCIDRs.some(function(subnet) {
                    return subnet.contains(ipaddr);
                })
            };
            var isAllowedPort = function(port) {
                return ['80', '8080', '443', '8443', '8000', ''].includes(port)
            };

            if (url.protocol === 'http:' || url.protocol === 'https:') {
                if (isAllowedPort(url.port)) {
                    if (isPrivateHostname(url.hostname) || isPrivateIP(url.hostname)) {
                        callback({error: "Host is not permitted " + url.hostname});
                    } else {
                        superagent.get(url.href).responseType('arraybuffer').timeout(15000).end(function (error, response) {
                            let bytes, data, hex, signature;
                            if (error) {
                                req.bas.logger.info("Unexpected error: unable to fetch the requested url");
                                callback({error: "Unexpected error: unable to fetch the requested url: " + (error.message ? error.message : error)});
                            } else {
                                contentBody = response.body;
                                if (response && response.headers && response.headers['content-type']) {
                                    if (isImage(response.headers['content-type'])) {
                                        contentType = response.headers['content-type'];
                                        contentLength = response.headers['content-length'];
                                        bytes = [];
                                        data = response.body.slice(0, 4);
                                        data.forEach(function (byte) {
                                            bytes.push(byte.toString(16))
                                        });
                                        hex = bytes.join('').toUpperCase();
                                        signature = getMimetypeFromSignature(hex);
                                        if (signature.result) {
                                            req.bas.logger.info("fetch valid signature " + signature.contentType);
                                            callback({});
                                        } else {
                                            req.bas.logger.info("fetch invalid signature");
                                            callback({error: "Invalid signature"});
                                        }
                                    } else if (response.headers['content-type'].startsWith("text/xml")) {
                                        contentType = 'text/xml'
                                        if (url.origin === "https://api.flickr.com") {
                                            superagent.get(url.href).timeout(15000).end(function (error, response) {
                                                if (error) {
                                                    callback({error: "unexpected error: " + error.message});
                                                } else {
                                                    try {
                                                        const parser = sax.parser(true);
                                                        parser.onend = function () {
                                                            callback({});
                                                        };
                                                        parser.write(response.text).close();
                                                    } catch (e) {
                                                        callback({error: "XML parse error: " + e.message});
                                                    }
                                                }
                                            });
                                        } else {
                                            callback({error: "URL is not whitelisted for XML content"});
                                        }
                                    } else {
                                        callback({error: "Content type is not permitted"});
                                    }
                                } else {
                                    callback({error: "Content type is missing"});
                                }
                            }
                        });
                    }
                } else {
                    callback({error: "Port is not permitted " + url.port});
                }
            } else {
                callback({error: "Protocol is not HTTP or HTTPS"});
            }
        };

        var finalise = function(sessionData, obj) {
            // release session
            sessionManager.releaseSession(sessionData, function(sessionObj) {
                if (sessionObj.error) {
                    // we log error but we try to complete the call
                    req.bas.logger.error("sessionManager.releaseSession failed " + sessionObj.error);
                }

                if (contentType === 'text/html') {
                    // NOTE(Snyk): verifies the contentType is safe (not a text/html that could be used for XSS)
                    sendStatusError(500, 'Content type not allowed: ' + contentType);
                } else if (obj.error) {
                    sendStatusError(obj.code, obj.error);
                } else {
                    contentType && res.setHeader('Content-Type', contentType);
                    contentLength && res.setHeader('Content-Length', contentLength);
                    res.write(contentBody);
                    res.end();
                }
            });
        };

        sessionManager.createSession(req.bas.logger, "fetch", function (obj) {
            var sessionData, dbConnector;
            if (obj.error) {
                sendStatusError(403, "Unable to open a session " + obj.error);
            } else {
                sessionData = obj;
                dbConnector = sessionData.dbConnector;
                if (token && url) {
                    try {
                        if (isWebUri(url)) {
                            isSafeUrl(url, function(obj) {
                                if (obj.error) {
                                    finalise(sessionData, {error: "Not safe URL: " + obj.error, code: 404});
                                } else {
                                    dbConnector.getUser(token, function (obj) {
                                        if (obj.error) {
                                            finalise(sessionData, {error: "Unable to find an authorised session " + obj.error, code: 403});
                                        } else {
                                            // we are authorized
                                            finalise(sessionData, {url: url});
                                        }
                                    });
                                }
                            });
                        } else {
                            finalise(sessionData, {error: "Invalid URI", code: 404});
                        }
                    } catch (e) {
                        finalise(sessionData, {error: "Malformed URI", code: 404});
                    }
                } else {
                    finalise(sessionData, {error: "Wrong parameters", code: 404});
                }
            }
        });
    });

    app.post('/'+con.API_CREATE,
        createApiAuthPipeline(con.API_CREATE),
        function (req, res) {
        let logger = req.bas.logger.getLogger({})
        // TODO: validate input
        if (req.headers['content-type'] && req.headers['content-type'].startsWith('multipart/form-data')) {
            parseMultipartFormDataAndDoCreate(logger, req, res);
        } else {
            parseJsonBodyAndDoCreate(logger, req, res);
        }
    });


    function parseMultipartFormDataAndDoCreate(logger, req, res) {
        parseMultipartFormData(metrics, logger, req, res, function (params, uploadedFileBuffer) {
            var platformArchiveID = params.platformArchiveID ? params.platformArchiveID : params.archiveID ? params.kind + "_" + params.archiveID : null;
            var platformInfo;
            try {
                platformInfo = JSON.parse(params.platformInfo);
            } catch (e) {
                //do nothing
            }
            try {
                doCreate(
                    logger,
                    req,
                    res,
                    params.platformToken,
                    params.platformSiteID,
                    params.platformArchiveName,
                    params.archiveAttributes,
                    params.kind,
                    platformArchiveID,
                    platformInfo,
                    params.cloneFromPlatformArchiveID,
                    uploadedFileBuffer
                );
            } catch (err) {
                returnJSON({ error: err.message }, res, null);
            }
        });
    }


    function parseJsonBodyAndDoCreate(logger, req, res) {
        var jsonObj = req.body;
        var platformToken = jsonObj.platformToken;
        var platformSiteID = jsonObj.platformSiteID;
        var platformArchiveName = jsonObj.platformArchiveName;
        var archiveAttributes = jsonObj.archiveAttributes;
        var kind = jsonObj.kind;
        // archiveID is supplied by webdemo not specifying the kind prefix as by convention
        var platformArchiveID = jsonObj.platformArchiveID ? jsonObj.platformArchiveID : jsonObj.archiveID ? kind + "_" + jsonObj.archiveID : null;
        var platformInfo;
        try {
            platformInfo = JSON.parse(jsonObj.platformInfo);
        } catch (e) {
            //do nothing
        }

        try {
            doCreate(logger, req, res, platformToken, platformSiteID, platformArchiveName, archiveAttributes, kind, platformArchiveID, platformInfo, jsonObj.cloneFromPlatformArchiveID, null);
        } catch (err) {
            returnJSON({ error: err.message }, res, null);
        }

    }


    function doCreate(logger, req, res, platformToken, platformSiteID, platformArchiveName, archiveAttributes, kind, platformArchiveID, platformInfo, cloneFromPlatformArchiveID, uploadedFileBuffer) {
        logger = logger.getLogger({
            action: "create",
            kind: kind,
            platformArchiveName: platformArchiveName || "",
            platformToken: platformToken,
            platformSiteID: platformSiteID || "",
            platformArchiveID: platformArchiveID || ""
        });

        logger.info(`create new archive ${platformArchiveID}`);

        const connector = getConnector(kind);
        if (!connector) {
            returnJSON({error: "Unknown connector"}, res, null);
            return;
        }

        const rateLimiterGroup = rateLimiterConfiguration.getRateLimitersGroup(['create:global'], { application: 'bas' }, logger);
        const productInfo = parseProductInfoFromHeaders(req.headers);
        Promise.all([
            blockList.isProductBuildBlocked(productInfo),
            rateLimiterGroup.checkExceeded(),
        ])
        .then(([isBlocked, rateLimiterStatus]) => {
            if (isBlocked) {
                returnJSON({error: "Client version blocked"}, res, null);
                return;
            }
            if (rateLimiterStatus.hasExceeded) {
                returnJSON({
                    error: rateLimiterStatus.publicInfo.publicErrorMessage,
                    info: { maxValue: rateLimiterStatus.publicInfo.maxValue, expirationInSeconds: rateLimiterStatus.publicInfo.expirationInSeconds }
                }, res, null);
                return;
            }
            rateLimiterGroup.incr();

            metrics.addValue('create-count-' + kind, 1, 'Count');

            sessionManager.createSession(logger, "create", function (obj) {
                var sessionData, dbConnector;
                if (obj.error) {
                    returnJSON(obj, res, null);
                } else {
                    sessionData = obj;
                    dbConnector = sessionData.dbConnector;
                    return connector
                    .getUserKeyFromPlatformToken({platformToken, dbConnector, logger})
                    .then((userKey) => {
                        if (!userKey) {
                            return false;
                        }
                        logger.updateParams({userKey});
                        return blockList.isUserKeyBlocked({userKey});
                    })
                    .then((isBlocked) => {
                        if (isBlocked) {
                            returnJSON({error: `User Blocked`}, res, sessionData);
                        } else {
                            var bar;
                            connector.allowsArchiveCreation(dbConnector, platformToken, function (result) {
                                if (result.error) {
                                    returnJSON(result, res, sessionData);
                                    return;
                                }

                                bar = sessionManager.getBar(sessionData);
                                if (uploadedFileBuffer) {
                                    // A BMPR was uploaded
                                    var archiveID = connector.generateArchiveID();
                                    openBmprAndStoreInDb(dbConnector, res, req, sessionData, bar, archiveID, kind, platformSiteID, platformArchiveID, platformArchiveName, platformInfo, archiveAttributes, uploadedFileBuffer);
                                } else if (cloneFromPlatformArchiveID) {
                                    // Another project is taken as a starting point

                                    // Check with the connector to see if this user can open the project we want to clone
                                    // TODO: not giving user info in GD connector we fall back in VIEWER role. We should  retrieve UserInfo from the platformToken
                                    connector.getRole(logger, dbConnector, platformToken, platformSiteID, cloneFromPlatformArchiveID, {}, function (obj) {
                                        if (obj.error) {
                                            returnJSON(obj, res, sessionData);
                                        } else {
                                            if (obj.role === con.ROLE_NO_ACCESS) {
                                                logger.error("Project not found, or you don't have permission to view it " + platformArchiveID + " " + platformSiteID);
                                                returnJSON({error: "Project not found, or you don't have permission to view it"}, res, sessionData);
                                            } else {
                                                dbConnector.getBASArchiveID(platformSiteID, cloneFromPlatformArchiveID, function (obj) {
                                                    if (obj.error) {
                                                        returnJSON(obj, res, sessionData);
                                                    } else {
                                                        var doClone = function(archiveID) {
                                                            sessionManager.openBarLocked(sessionData, archiveID, "READ", function (obj) {
                                                                if (obj.error) {
                                                                    returnJSON(obj, res, sessionData);
                                                                } else {
                                                                    var bar = obj.bar;
                                                                    // make the dump of all the branches
                                                                    bar.dump(null, function (obj) {
                                                                        if (obj.error) {
                                                                            returnJSON(obj, res, sessionData);
                                                                        } else {
                                                                            var dump = obj.dump;
                                                                            sessionManager.unlockConnection(sessionData, function (/*objUnlock*/) {
                                                                                var archiveID = connector.generateArchiveID();
                                                                                storeDumpInDb(dbConnector, res, req, sessionData, bar, archiveID, kind, platformSiteID, platformArchiveID, platformArchiveName, platformInfo, archiveAttributes, dump);
                                                                            });
                                                                        }
                                                                    });
                                                                }
                                                            });
                                                        };
                                                        if (obj.BAS_ARCHIVE_ID) {
                                                            doClone(obj.BAS_ARCHIVE_ID);
                                                        } else {
                                                            // This may not be an error, but the case in which BAS is not holding the project and must acquire it from S3.
                                                            connector.loadFromPlatform(logger, dbConnector, platformToken, platformSiteID, cloneFromPlatformArchiveID, null, platformInfo, function (obj) {
                                                                if (obj.error) {
                                                                    returnJSON(obj, res, sessionData);
                                                                    return;
                                                                }
                                                                var archiveID = obj.id;
                                                                var buffer = obj.buffer;
                                                                // update the platformInfo if changed by the loadFromPlatform
                                                                platformInfo = obj.platformInfo ? obj.platformInfo : platformInfo;
                                                                logger.info("loaded the archive from platform " + archiveID);
                                                                createBARFromBuffer(archiveID, buffer, sessionData, platformInfo, kind, platformSiteID, cloneFromPlatformArchiveID, platformArchiveName, dbConnector, logger, config, sessionManager, function (obj) {
                                                                    if (obj.error) {
                                                                        returnJSON(obj, res, sessionData);
                                                                        return;
                                                                    }
                                                                    doClone(archiveID);
                                                                });
                                                            });
                                                        }
                                                    }
                                                });
                                            }
                                        }
                                    });
                                } else {
                                    // A local template is taken as starting point
                                    if (kind === "wd") {
                                        let finalizeWebDemoProjectCreation = function (obj) {
                                            if (obj.error) {
                                                returnJSON(obj, res, sessionData);
                                            } else {
                                                let bar = sessionManager.getBar(sessionData);
                                                let archiveID = connector.generateArchiveID();
                                                openBmprAndStoreInDb(dbConnector, res, req, sessionData, bar, archiveID, kind, platformSiteID, platformArchiveID, platformArchiveName, platformInfo, archiveAttributes, obj.buffer);
                                            }
                                        }
                                        // check if an archive with the same IDs already exists
                                        dbConnector.getBASArchiveID(platformSiteID, platformArchiveID, function (obj) {
                                            if (obj.error) {
                                                returnJSON(obj, res, sessionData);
                                            } else {
                                                if (obj.BAS_ARCHIVE_ID) {
                                                    returnJSON({
                                                        platformArchiveID: platformArchiveID,
                                                        platformSiteID: platformSiteID,
                                                        success: "archiveAlreadyExists"
                                                    }, res, sessionData);
                                                } else {
                                                    if (platformInfo && platformInfo.initializeFromPlatformTemplate && connector.getBufferFromSpecificTemplate) {
                                                        connector.getBufferFromSpecificTemplate(platformInfo.initializeFromPlatformTemplate, finalizeWebDemoProjectCreation);
                                                    } else {
                                                        connector.getBufferFromTemplate(finalizeWebDemoProjectCreation);
                                                    }
                                                }
                                            }
                                        });
                                    } else {
                                        connector.getBufferFromTemplate(function (obj) {
                                            if (obj.error) {
                                                returnJSON(obj, res, sessionData);
                                            } else {
                                                var buffer = obj.buffer;
                                                connector.create(logger, dbConnector, platformToken, platformSiteID, platformArchiveName, platformInfo, buffer, function (obj) {
                                                    if (obj.error) {
                                                        returnJSON(obj, res, sessionData);   //could say already exists
                                                    } else {
                                                        logger.info("new archive created");
                                                        returnJSON({
                                                            platformArchiveID: obj.platformArchiveID,
                                                            platformArchiveName: obj.platformArchiveName
                                                        }, res, sessionData);
                                                    }
                                                });
                                            }
                                        });
                                    }
                                }
                            });
                        }
                    })
                    .catch(err => {
                        logger.error('Unexpected error', err);
                        returnJSON({error: 'Unexpected error'}, res, null);
                    });
                }
            });
        }).catch(err => {
            logger.error('Unexpected error', err);
            returnJSON({error: 'Unexpected error'}, res, null);
        });
    }

    function openBmprAndStoreInDb(dbConnector, res, req, sessionData, bar, archiveID, kind, platformSiteID, platformArchiveID, platformArchiveName, platformInfo, archiveAttributes, bmprBuffer) {
        req.bas.addLoggerInfo({action: "create", connector: kind, platformArchiveName: platformArchiveName})
        req.bas.logger.info("openBmprAndStoreInDb " + platformArchiveID);
        sqliteBuffer2object(config.archivesPath, bmprBuffer, kind, function (obj) {
            if (obj.error) {
                returnJSON(obj, res, sessionData);
            }
            else {
                // TODO: randomise UUID for first resource
                storeDumpInDb(dbConnector, res, req, sessionData, bar, archiveID, kind, platformSiteID, platformArchiveID, platformArchiveName, platformInfo, archiveAttributes, obj.dump);
            }
        });
    }

    function storeDumpInDb(dbConnector, res, req, sessionData, bar, archiveID, kind, platformSiteID, platformArchiveID, platformArchiveName, platformInfo, archiveAttributes, dump) {
        req.bas.addLoggerInfo({action: "create", connector: kind, platformArchiveName: platformArchiveName});
        req.bas.logger.info("storeDumpInDb " + platformArchiveID);
        if (archiveAttributes) {
            dump.Info.ArchiveAttributes = JSON.parse(archiveAttributes);
        }
        // we do not need LOCK, archiveID is unique and PLATFORM_DATA is not yet saved
        bar.createFromDump(archiveID, dump, function (obj) {
            if (obj.error) {
                returnJSON(obj, res, sessionData);
            }
            else {
                if (kind === 'cloud') {
                    if (!platformInfo) {
                        platformInfo = {};
                    }
                    platformInfo.archiveRevisionOnPlatform = dump.Info.ArchiveRevision;
                }
                if (!platformArchiveID) {
                    // if not specified, by convention we use the same ID
                    platformArchiveID = archiveID;
                }
                req.bas.logger.info("saveArchivePlatformData " + platformArchiveID);
                dbConnector.saveArchivePlatformData(archiveID, kind, platformSiteID, platformArchiveID, platformArchiveName, platformInfo, function (obj) {
                    if (obj.error) {
                        returnJSON(obj, res, sessionData);
                    } else {
                        req.bas.logger.info("returnJSON " + platformArchiveID);
                        returnJSON({platformArchiveID: platformArchiveID}, res, sessionData);
                    }
                });
            }
        });
    }

    // Pre processing open in case of public share link
    app.post('/'+con.API_OPEN,
        createApiAuthPipeline(con.API_OPEN),
        function (req, res, next) {
            var permalinkID = req.body.publicShareID;
            if (permalinkID) {
                req.bas.addLoggerInfo({ action: "open-permalink", permalinkID: permalinkID });
                req.bas.logger.info("Open via permalink");
                sessionManager.createSession(req.bas.logger, "open-permalink", function(obj) {
                    var sessionData, dbConnector;
                    if (obj.error) {
                        req.bas.logger.info("Failed to open a session with the db");
                        returnJSON(obj, res, null);
                    } else {
                        sessionData = obj;
                        dbConnector = sessionData.dbConnector;
                        dbConnector.getPermalinkData(permalinkID, function(obj) {
                            if (obj.error) {
                                returnJSON(obj, res, sessionData);
                            } else {
                                if (obj && obj.permalinkKind && obj.permalinkKind === Consts.PermalinkKind.public_share) {
                                    // we blank the platform token in order to avoid the forgery
                                    delete req.body.platformToken;
                                    req.body.platformSiteID = obj.platformSiteID;
                                    req.body.platformArchiveID = obj.platformArchiveID;
                                    req.body.platformInfo = JSON.stringify(obj.platformInfo);
                                    req.body.kind = obj.platformKind;
                                    sessionManager.releaseSession(sessionData, function(sessionObj) {
                                        if (sessionObj.error) {
                                            // we log error but we try to complete the call
                                            req.bas.logger.error("sessionManager.releaseSession failed " + sessionObj.error);
                                        }
                                        // proceed with a standard open
                                        next();
                                    });
                                } else {
                                    returnJSON({ error: "not authorized" }, res, sessionData);
                                }
                            }
                        });
                    }
                });
            } else {
                next();
            }
        }
    );

    app.post('/'+con.API_OPEN, function (req, res) {
        var jsonObj = req.body;
        var platformToken = jsonObj.platformToken;
        var platformSiteID = jsonObj.platformSiteID;
        var platformArchiveID = jsonObj.platformArchiveID;
        var platformArchiveName = jsonObj.platformArchiveName;
        var branchID = jsonObj.branchID;
        let skipThumbnailImage = jsonObj.skipThumbnailImage && jsonObj.skipThumbnailImage === 'true';
        let unknownUsernameTracking;
        const productInfo = parseProductInfoFromHeaders(req.headers);

        var platformInfo;
        var userInfo, logObj;
        try {
            platformInfo = JSON.parse(jsonObj.platformInfo);
        } catch (e) {
            //do nothing
        }
        var kind = jsonObj.kind;

        if (jsonObj.userName) {
            userInfo = {name: jsonObj.userName}
            unknownUsernameTracking = "jsonObj.userName";
        } else {
            try {
                userInfo = JSON.parse(jsonObj.userInfo);
                if (userInfo && userInfo.name) {
                    // nothing to do
                    unknownUsernameTracking = "userInfo.name";
                } else {
                    // TODO: probably is not necessary, userName can be null
                    userInfo = {name: "unknown", isAnonymous: true}
                    unknownUsernameTracking = "userInfo.name is missing " + jsonObj.userInfo;
                }
            } catch (e) {
                userInfo = {name: "unknown", isAnonymous: true}
                unknownUsernameTracking = "userInfo parse error";
            }
        }

        const connector = getConnector(kind);
        if (!connector) {
            returnJSON({error: "Unknown connector"}, res, null);
            return;
        }

        logObj = {
            action: "open",
            kind: kind,
            platformToken: platformToken,
            username: userInfo.name,
            platformSiteID: platformSiteID || "",
            platformArchiveID: platformArchiveID || "",
            platformArchiveName: platformArchiveName || "",
            unknownUsernameTracking: unknownUsernameTracking,
            isAnonymous: userInfo.isAnonymous || false
        };
        req.bas.addLoggerInfo(logObj);
        req.bas.logger.info("Block List check: going to check user name and archive id");

        Promise.all([
            blockList.isPlatformArchiveBlocked({kind, platformSiteID, platformArchiveID}),
            blockList.isProductBuildBlocked(productInfo),
            blockList.isProductBlocked(productInfo),
        ])
        .then(([isArchiveBlocked, isBuildBlocked, isProductBlocked]) => {
            if (isArchiveBlocked) {
                returnJSON({error: "Access to archive blocked"}, res, null);
                return;
            }
            if (isBuildBlocked) {
                returnJSON({error: "Client version blocked"}, res, null);
                return;
            }
            if (isProductBlocked) {
                returnJSON({error: "Sorry, Balsamiq for Desktop can no longer open Balsamiq Cloud projects."}, res, null);
                return;
            }

            sessionManager.createSession(req.bas.logger, "open", function (obj) {
                var sessionData, dbConnector;
                if (obj.error) {
                    returnJSON(obj, res, null);
                } else {
                    sessionData = obj;
                    dbConnector = sessionData.dbConnector;

                    req.bas.logger.info("checking user info");
                    connector.checkUserInfo(req.bas.logger, dbConnector, userInfo, platformToken, function (obj) {
                        if (obj.error) {
                            // req.bas.logger.error("user info does not match with authentication token: " + JSON.stringify(obj.error));
                            // Session expired, we return the error in order to force the user to log in
                            returnJSON({error: "User info provided does not match with info of user logged in or session expired"}, res, sessionData);
                        } else {
                            req.bas.logger.info("open archive");

                            connector
                            .getUserKeyFromPlatformToken({platformToken, dbConnector, logger: req.bas.logger, platformInfo, userInfo})
                            .then((userKey) => {
                                if (!userKey) {
                                    return false;
                                }
                                req.bas.addLoggerInfo({userKey});
                                logObj.userKey = userKey;
                                return blockList.isUserKeyBlocked({userKey});
                            })
                            .then((isBlocked) => {
                                if (isBlocked) {
                                    returnJSON({error: `User Blocked`}, res, sessionData);
                                    return;
                                }
                                // TODO: inject platformInfo, we could change the prototype of the getRole
                                userInfo.platformInfo = platformInfo;
                                // Check with the connector to see if this user can open this project
                                connector.getRole(req.bas.logger, dbConnector, platformToken, platformSiteID, platformArchiveID, userInfo, function (obj) {
                                    if (obj.error) {
                                        returnJSON(obj, res, sessionData);
                                    } else {
                                        var role = obj.role;
                                        // if user is anonymous (and a public share exists) the platform token is provided by the getRole
                                        if (!platformToken && obj.platformToken) {
                                            req.bas.logger.info("Opening archive for public share " + platformArchiveID + " " + platformSiteID);
                                            platformToken = obj.platformToken;
                                        }

                                        if (role === con.ROLE_NO_ACCESS) {
                                            req.bas.logger.error("Project not found, or you don't have permission to view it " + platformArchiveID + " " + platformSiteID);
                                            returnJSON({error: "Project not found, or you don't have permission to view it"}, res, sessionData);
                                        } else {
                                            //see if it's a known or new project
                                            dbConnector.getBASArchiveIDWithExclusiveRowLock(platformSiteID, platformArchiveID, true, function (obj) {
                                                var buffer, archiveID;
                                                if (obj.error) {
                                                    returnJSON(obj, res, sessionData);
                                                } else {
                                                    if (obj.BAS_ARCHIVE_ID) {
                                                        archiveID = obj.BAS_ARCHIVE_ID;
                                                        logObj.archiveID = archiveID;
                                                        req.bas.addLoggerInfo({archiveID});
                                                        req.bas.logger.info("access an already opened basArchiveID " + archiveID + " platformArchiveID " + platformArchiveID + " platformSiteID " + platformSiteID);
                                                        sessionManager.openBarLocked(sessionData, archiveID, "READ", function (obj) {
                                                            if (obj.error) {
                                                                returnJSON(obj, res, sessionData);
                                                            } else {
                                                                // need READ LOCK
                                                                var bar = obj.bar;
                                                                // if branchID == null we return the dump of all branches
                                                                bar.getToc(branchID, function (obj) {

                                                                    if (skipThumbnailImage) {
                                                                        stripThumbnailImageFromDump(obj.dump);
                                                                    }

                                                                    bar.getHeuristicArchiveSize(function (objSize) {
                                                                        var heuristicArchiveSize;
                                                                        if (objSize.error) {
                                                                            req.bas.logger.error("error calculating archive heuristic size: " + objSize.error);
                                                                        } else {
                                                                            logObj.heuristicArchiveSize = objSize.heuristicArchiveSize;
                                                                            req.bas.addLoggerInfo({heuristicArchiveSize: objSize.heuristicArchiveSize});
                                                                            req.bas.logger.info("archive heuristic size: " + objSize.heuristicArchiveSize);
                                                                            heuristicArchiveSize = objSize.heuristicArchiveSize;
                                                                        }

                                                                        sessionManager.unlockConnection(sessionData, function (/*objUnlock*/) {
                                                                            // TODO: handle objUnlock
                                                                            if (obj.error) {
                                                                                returnJSON(obj, res, sessionData);
                                                                            } else {
                                                                                saveUserAndReturnInitialData(sessionData, platformToken, userInfo, archiveID, kind, role, obj.dump, heuristicArchiveSize, res);
                                                                            }
                                                                        });
                                                                    });
                                                                });
                                                            }
                                                        });
                                                    } else {
                                                        // TODO load the template name passed in the platformInfo
                                                        if (platformInfo && platformInfo.initializeFromPlatformTemplate) {
                                                            connector.getBufferFromTemplate(function (obj) {
                                                                if (obj.error) {
                                                                    returnJSON(obj, res, sessionData);
                                                                } else {
                                                                    archiveID = obj.id;
                                                                    buffer = obj.buffer;
                                                                    req.bas.logger.info("loaded the archive from template " + archiveID);
                                                                    createBARFromBuffer(archiveID, buffer, sessionData, platformInfo, kind, platformSiteID, platformArchiveID, platformArchiveName, dbConnector, req.bas.logger, config, sessionManager, function (obj) {
                                                                        if (obj.error) {
                                                                            returnJSON(obj, res, sessionData);
                                                                            return;
                                                                        }
                                                                        saveUserAndReturnInitialData(sessionData, platformToken, userInfo, archiveID, kind, role, obj.dump, obj.heuristicArchiveSize, res);
                                                                    });
                                                                }
                                                            });
                                                        } else {
                                                            // web demo connector will return an error here, i.e. {error: "Call create first", ignore: true}
                                                            connector.loadFromPlatform(req.bas.logger, dbConnector, platformToken, platformSiteID, platformArchiveID, null, platformInfo, function (obj) {
                                                                if (obj.error) {
                                                                    returnJSON(obj, res, sessionData);
                                                                } else {
                                                                    archiveID = obj.id;
                                                                    buffer = obj.buffer;
                                                                    // update the platformInfo if changed by the loadFromPlatform
                                                                    platformInfo = obj.platformInfo ? obj.platformInfo : platformInfo;
                                                                    req.bas.logger.info("loaded the archive from platform " + archiveID);
                                                                    createBARFromBuffer(archiveID, buffer, sessionData, platformInfo, kind, platformSiteID, platformArchiveID, platformArchiveName, dbConnector, req.bas.logger, config, sessionManager, function (obj) {
                                                                        if (obj.error) {
                                                                            returnJSON(obj, res, sessionData);
                                                                            return;
                                                                        }
                                                                        if (skipThumbnailImage) {
                                                                            stripThumbnailImageFromDump(obj.dump);
                                                                        }
                                                                        saveUserAndReturnInitialData(sessionData, platformToken, userInfo, archiveID, kind, role, obj.dump, obj.heuristicArchiveSize, res);
                                                                    });
                                                                }
                                                            });
                                                        }
                                                    }
                                                }
                                            });
                                        }
                                    }
                                });
                            })
                            .catch(err => {
                                req.bas.logger.error('Unexpected error', err);
                                returnJSON({error: 'Unexpected error'}, res, null);
                            });
                        }
                    });
                }
            });
        });
    });

    app.post('/'+con.API_OPEN_FROM_SERVER, function (req, res) {
        const productInfo = parseProductInfoFromHeaders(req.headers);
        const jsonObj = req.body;
        const platformToken = jsonObj.platformToken;
        const platformSiteID = jsonObj.platformSiteID;
        const platformArchiveID = jsonObj.platformArchiveID;
        const branchID = jsonObj.branchID;
        const skipThumbnailImage = jsonObj.skipThumbnailImage && jsonObj.skipThumbnailImage === 'true';
        const kind = jsonObj.kind;
        // TODO: permission is forced to viewer

        let platformInfo, logObj;
        try {
            platformInfo = JSON.parse(jsonObj.platformInfo);
        } catch (e) {
            //do nothing
        }

        const connector = getConnector(kind);
        if (!connector) {
            returnJSON({error: "Unknown connector"}, res, null);
            return;
        }
        req.bas.verifyAdminCredentials()
            .then(allowed => {
                if (!allowed) {
                    returnJSON({error: "Invalid Credentials"}, res, null);
                    return;
                }

                logObj = {
                    action: con.API_OPEN_FROM_SERVER,
                    kind: kind,
                    platformToken: platformToken,
                    platformSiteID: platformSiteID || "",
                    platformArchiveID: platformArchiveID || ""
                };
                req.bas.addLoggerInfo(logObj);
                req.bas.logger.info("Block List check: going to check archive id");

                Promise.all([
                    blockList.isPlatformArchiveBlocked({kind, platformSiteID, platformArchiveID}),
                    blockList.isProductBuildBlocked(productInfo),
                    blockList.isProductBlocked(productInfo),
                ])
                    .then(([isArchiveBlocked, isBuildBlocked, isProductBlocked]) => {
                        if (isArchiveBlocked) {
                            returnJSON({error: "Access to archive blocked"}, res, null);
                            return;
                        }
                        if (isBuildBlocked) {
                            returnJSON({error: "Client version blocked"}, res, null);
                            return;
                        }
                        if (isProductBlocked) {
                            returnJSON({error: "Sorry, Balsamiq for Desktop can no longer open Balsamiq Cloud projects."}, res, null);
                            return;
                        }

                        sessionManager.createSession(req.bas.logger, con.API_OPEN_FROM_SERVER, function (obj) {
                            let sessionData, dbConnector;
                            if (obj.error) {
                                returnJSON(obj, res, null);
                            } else {
                                sessionData = obj;
                                dbConnector = sessionData.dbConnector;

                                dbConnector.getBASArchiveIDWithExclusiveRowLock(platformSiteID, platformArchiveID, true, function (obj) {
                                    let buffer, archiveID;
                                    if (obj.error) {
                                        returnJSON(obj, res, sessionData);
                                    } else {
                                        // archive is already loaded
                                        if (obj.BAS_ARCHIVE_ID) {
                                            archiveID = obj.BAS_ARCHIVE_ID;
                                            logObj.archiveID = archiveID;
                                            req.bas.addLoggerInfo({archiveID});
                                            req.bas.logger.info("access an already opened basArchiveID " + archiveID + " platformArchiveID " + platformArchiveID + " platformSiteID " + platformSiteID);
                                            sessionManager.openBarLocked(sessionData, archiveID, "READ", function (obj) {
                                                if (obj.error) {
                                                    returnJSON(obj, res, sessionData);
                                                } else {
                                                    // need READ LOCK
                                                    let bar = obj.bar;
                                                    // if branchID == null we return the dump of all branches
                                                    bar.getToc(branchID, function (obj) {
                                                        if (skipThumbnailImage) {
                                                            stripThumbnailImageFromDump(obj.dump);
                                                        }

                                                        sessionManager.unlockConnection(sessionData, function (/*objUnlock*/) {
                                                            if (obj.error) {
                                                                returnJSON(obj, res, sessionData);
                                                            } else {
                                                                saveUserAndReturnInitialData(sessionData, platformToken, {}, archiveID, kind, con.ROLE_VIEWER, obj.dump, null, res);
                                                            }
                                                        });
                                                    });
                                                }
                                            });
                                        } else {
                                            connector.loadFromPlatform(req.bas.logger, dbConnector, platformToken, platformSiteID, platformArchiveID, null, platformInfo, function (obj) {
                                                if (obj.error) {
                                                    returnJSON(obj, res, sessionData);
                                                } else {
                                                    archiveID = obj.id;
                                                    buffer = obj.buffer;
                                                    // update the platformInfo if changed by the loadFromPlatform
                                                    platformInfo = obj.platformInfo ? obj.platformInfo : platformInfo;
                                                    req.bas.logger.info("loaded the archive from platform " + archiveID);
                                                    createBARFromBuffer(archiveID, buffer, sessionData, platformInfo, kind, platformSiteID, platformArchiveID, null, dbConnector, req.bas.logger, config, sessionManager, function (obj) {
                                                        if (obj.error) {
                                                            returnJSON(obj, res, sessionData);
                                                            return;
                                                        }
                                                        if (skipThumbnailImage) {
                                                            stripThumbnailImageFromDump(obj.dump);
                                                        }
                                                        saveUserAndReturnInitialData(sessionData, platformToken, {}, archiveID, kind, con.ROLE_VIEWER, obj.dump, null, res);
                                                    });
                                                }
                                            });
                                        }
                                    }
                                });
                            }
                        });
                    });
            })
            .catch(err => {
                req.bas.logger.error('Unexpected error while checking server API credentials', err);
                returnJSON({error: "Unexpected error while opening from server"}, res, null);
            });
    });


    app.post('/'+con.API_GETUSERSINFOANDSETTINGS, function (req, res) {
        var jsonObj = req.body;
        // var logObj = {action: "getUsersInfoAndSettings"};
        var platformKind = jsonObj.platformKind;
        if (platformKind && getConnector(platformKind)?.getUsersInfoAndSettings) {
            getConnector(platformKind).getUsersInfoAndSettings(req, jsonObj, function (obj) {
                returnJSON(obj, res, null);
            });
        } else {
            returnJSON({error: "not implemented"}, res, null);
        }
    });

    app.post('/'+con.API_LOGUSEREVENT, function (req, res) {
        var jsonObj = req.body; // { userEvent }
        var token = req.query.token;
        var userEvent;

        checkValidUser(con.API_LOGUSEREVENT, req, res, function (sessionData) {
            var dbConnector = sessionData.dbConnector;
            var archiveID = sessionData.user.ARCHIVE_ID;
            var userPlatformToken = sessionData.user.PLATFORM_TOKEN;
            var userInfo = JSON.parse(sessionData.user.USERINFO);
            req.bas.addLoggerInfo({action: "logUserEvent", sessionToken: token, archiveID});
            req.bas.logger.info("logUserEvent for archive " + archiveID);

            try {
                userEvent = JSON.parse(jsonObj.userEvent);
            } catch (e) {
                userEvent = {error: e.message || "Unknown error parsing notification info " + jsonObj.notificationInfo};
            }

            if (userEvent.error) {
                returnJSON(userEvent, res, null);
            } else {
                dbConnector.getPlatformData(archiveID, function (obj) {
                    var kind;
                    if (obj.error) {
                        returnJSON(obj, res, sessionData);
                    } else if (obj.PLATFORM_KIND) {
                        kind = obj.PLATFORM_KIND;
                        // store the user token
                        obj.userPlatformToken = userPlatformToken;
                        if (getConnector(kind)?.logUserEvent) {
                            getConnector(kind).logUserEvent(req.bas.logger, dbConnector, req.ip, userInfo, userEvent, obj, function (result) {
                                returnJSON(result, res, sessionData);
                            });
                        } else {
                            returnJSON({success: "function not available for this connector"}, res, sessionData);
                        }
                    } else {
                        returnJSON({error: "platform data unavailable for archiveID " + archiveID}, res, sessionData);
                    }
                });
            }
        });
    });

    app.get('/'+con.API_GETHEURISTICARCHIVESIZE, function (req, res) {
        var token = req.query.token;
        checkValidUser(con.API_GETHEURISTICARCHIVESIZE, req, res, function (sessionData) {
            var internalUserID = sessionData.user.INTERNAL_ID;
            var username = sessionData.user.USERNAME;
            var archiveID = sessionData.user.ARCHIVE_ID;
            var bar;

            req.bas.addLoggerInfo({action: con.API_GETHEURISTICARCHIVESIZE, sessionToken: token, archiveID})
            sessionManager.openBarLocked(sessionData, archiveID, "READ", function (obj) {
                if (obj.error) {
                    returnJSON(obj, res, sessionData);
                } else {
                    bar = obj.bar;
                    req.bas.logger.info("getHeuristicArchiveSize for archive " + archiveID);

                    bar.getHeuristicArchiveSize(function (obj) {
                        broadcastRTCMessage(archiveID, obj.archiveRevision, internalUserID, username, {
                            operation: 'getHeuristicArchiveSize',
                            heuristicArchiveSize: obj.heuristicArchiveSize
                        });

                        returnJSON(obj, res, sessionData);
                    });
                }
            });
        });
    });

    app.post('/'+con.API_GETPROJECTMEMBERS, function (req, res) {
        var jsonObj = req.body; // { data }
        var token = req.query.token;
        var data;

        checkValidUser(con.API_GETPROJECTMEMBERS, req, res, function (sessionData) {
            var dbConnector = sessionData.dbConnector;
            var archiveID = sessionData.user.ARCHIVE_ID;
            var userInfo = JSON.parse(sessionData.user.USERINFO);
            req.bas.addLoggerInfo({action: "getProjectMembers", sessionToken: token, archiveID});
            req.bas.logger.info("getProjectMembers for archive " + archiveID);

            try {
                data = JSON.parse(jsonObj.data);
            } catch (e) {
                data = {error: e.message || "Unknown error parsing data " + jsonObj.notificationInfo};
            }

            if (data.error) {
                returnJSON(data, res, null);
            } else {
                dbConnector.getPlatformData(archiveID, function (obj) {
                    var kind;
                    if (obj.error) {
                        returnJSON(obj, res, sessionData);
                    } else if (obj.PLATFORM_KIND) {
                        kind = obj.PLATFORM_KIND;
                        if (getConnector(kind)?.getProjectMembers) {
                            getConnector(kind).getProjectMembers(req.bas.logger, dbConnector, req.ip, userInfo, data, obj, function (result) {
                                returnJSON(result, res, sessionData);
                            });
                        } else {
                            returnJSON({error: "function not available for this connector"}, res, sessionData);
                        }
                    } else {
                        returnJSON({error: "platform data unavailable for archiveID " + archiveID}, res, sessionData);
                    }
                });
            }
        });
    });

    app.post('/'+con.API_UNLOAD_ARCHIVE_OFFLINE, function (req, res) {
        var jsonObj = req.body;
        var platformSiteID = jsonObj.platformSiteID;
        var platformArchiveID = jsonObj.platformArchiveID;

        req.bas.addLoggerInfo({
            action: "unloadArchiveOffline",
            platformArchiveID: platformArchiveID || "",
            platformSiteID: platformSiteID || "",
        });
        req.bas.logger.info("unload archive offline");

        sessionManager.createSession(req.bas.logger, "unloadArchiveOffline", function (obj) {
            var sessionData, dbConnector;
            if (obj.error) {
                returnJSON(obj, res, null);
            } else {
                sessionData = obj;
                dbConnector = sessionData.dbConnector;

                req.bas.verifyAdminCredentials()
                    .then(allowed => {
                        if (allowed) {
                            // get platform info data if archive is loaded
                            dbConnector.getBASArchiveID(platformSiteID, platformArchiveID, function (obj) {
                                var archiveID;
                                if (obj.error) {
                                    returnJSON(obj, res, sessionData);
                                } else {
                                    if (obj.BAS_ARCHIVE_ID) {
                                        // archive is loaded
                                        archiveID = obj.BAS_ARCHIVE_ID;
                                        req.bas.logger.info("archive is loaded " + archiveID, { archiveID });

                                        unloadSingleArchive(req.bas.logger, sessionData, archiveID, null, function (obj) {
                                            returnJSON(obj, res, sessionData);
                                        });
                                    } else {
                                        // archive is not loaded, nothing to do
                                        returnJSON({message: con.ARCHIVE_NOT_LOADED}, res, sessionData);
                                    }
                                }
                            });
                        } else {
                            req.bas.logger.error("unable to uload the archive offline, wrong credentials");
                            returnJSON({error: "wrong credentials"}, res, sessionData);
                        }
                    })
                    .catch(err => {
                        req.bas.logger.error("unexpected error while checking server API credentials", err);
                        returnJSON({error: "unexpected error while unloading archive"}, res, sessionData);
                    });
            }
        });
    });

    app.post('/unloadArchiveNotExistingOnPlatform', function (req, res) {
        var jsonObj = req.body;
        var kind = jsonObj.kind;
        var unloadArchive = jsonObj.unload;
        var deleteArchive = jsonObj.delete;
        var maxArchives = jsonObj.maxArchives;
        var archiveList;
        var results = {withError: [], notFound: [], foundAndUnSync: [], notFoundUnloaded: [], foundAndUnloaded: []};
        var verbose = jsonObj.verbose || false;

        const connector = getConnector(kind);
        if (!connector) {
            returnJSON({error: "Unknown connector"}, res, null);
            return;
        }

        req.bas.addLoggerInfo({
            action: "unloadArchiveNotExistingOnPlatform",
            kind: kind
        });
        req.bas.logger.info("unload archive not existing on platform for " + kind);

        var isArchiveSynced = function(sessionManager, sessionData, archiveData, callback) {
            // lock the BAR and the PLATFORM_INFO database
            sessionManager.openBarLockedExt(sessionData, archiveData.BAS_ARCHIVE_ID, "WRITE", true, function (obj) {
                var archiveRevisionOnPlatform, platformInfo;

                if (obj.error) {
                    callback && callback(obj);
                    return;
                }

                var bar = obj.bar;
                bar.getArchiveRevision(function (obj) {
                    var revision;
                    if (obj.error) {
                        callback && callback(obj);
                        return;
                    }
                    revision = obj.archiveRevision;
                    try {
                        platformInfo = archiveData.PLATFORM_INFO ? JSON.parse(archiveData.PLATFORM_INFO) : {};
                    } catch (e) {
                        req.bas.logger.warn('unloading single archive: unexpected exception parsing PLATFORM_INFO: ' + archiveData.PLATFORM_INFO);
                        callback && callback({error: "unloading single archive: unexpected exception parsing PLATFORM_INFO"});
                        return;
                    }

                    archiveRevisionOnPlatform = platformInfo.archiveRevisionOnPlatform;

                    if (revision === archiveRevisionOnPlatform) {
                        callback && callback({synced: true});
                    } else {
                        callback && callback({archiveRevisionOnPlatform: archiveRevisionOnPlatform, archiveRevision: revision, synced: false});
                    }
                });
            });
        };

        sessionManager.createSession(req.bas.logger, "unloadArchiveNotExistingOnPlatform", function (obj) {
            var sessionData, dbConnector;
            if (obj.error) {
                returnJSON(obj, res, null);
            } else {
                sessionData = obj;
                dbConnector = sessionData.dbConnector;

                req.bas.verifyAdminCredentials()
                    .then(allowed => {
                        if (allowed) {
                            dbConnector.getArchiveNotSyncForKind(kind, function (obj) {
                                if (obj.error) {
                                    req.bas.logger.error("getArchiveNotSyncForKind " + obj.error);
                                    returnJSON(obj, res, sessionData);
                                } else {
                                    var doJob = function (archiveList, callback) {
                                        var archiveData;

                                        if (archiveList.length) {
                                            archiveData = archiveList.pop();

                                            // check if the archive is synced
                                            isArchiveSynced(sessionManager, sessionData, archiveData, function(obj) {
                                                if (obj.synced) {
                                                    if (unloadArchive) {
                                                        dbConnector.deleteArchivePlatformData(archiveData.BAS_ARCHIVE_ID, function (obj) {
                                                            if (obj.error) {
                                                                archiveData.error = obj.error;
                                                                req.bas.logger.error("archive already synced, ERROR deleting data from BAS ", null, obj);
                                                                results.foundAndUnloaded.push(archiveData);
                                                            } else {
                                                                req.bas.logger.info("archive already synced, data deleted from BAS " + archiveData.PLATFORM_SITE_ID + " " + archiveData.PLATFORM_ARCHIVE_ID);
                                                                results.foundAndUnloaded.push(archiveData);
                                                            }
                                                            doJob(archiveList, callback);
                                                        });
                                                    } else {
                                                        req.bas.logger.info("archive is already synced, ready to be unloaded " + archiveData.ARCHIVE_ID);
                                                        results.foundAndUnloaded.push(archiveData);
                                                        doJob(archiveList, callback);
                                                    }
                                                } else {
                                                    connector.projectExistsOnPlatform(req.bas.logger, dbConnector, archiveData.PLATFORM_SITE_ID, archiveData.PLATFORM_ARCHIVE_ID, function (obj) {
                                                        req.bas.logger.info("projectExistsOnPlatform result ", obj);
                                                        if (obj.error) {
                                                            if (obj.code === 404 || obj.code === "ERR_CLOUD_ARCHIVE_EXISTS_ON_PLATFORM_4") {
                                                                req.bas.logger.info("deleteArchive option for archive " + archiveData.BAS_ARCHIVE_ID + ": " + deleteArchive);
                                                                if (deleteArchive && archiveData.BAS_ARCHIVE_ID) {
                                                                    dbConnector.deleteArchivePlatformData(archiveData.BAS_ARCHIVE_ID, function (obj) {
                                                                        if (obj.error) {
                                                                            req.bas.logger.error("ERROR deleteArchivePlatformData ", null, obj);
                                                                            archiveData.error = obj.error;
                                                                        }
                                                                        // else {
                                                                        //     req.bas.logger.info("archive data deleted from BAS " + archiveData.PLATFORM_SITE_ID + " " + archiveData.PLATFORM_ARCHIVE_ID);
                                                                        // }
                                                                        results.notFoundUnloaded.push(archiveData);
                                                                        doJob(archiveList, callback);
                                                                    });
                                                                } else {
                                                                    results.notFound.push(obj);
                                                                    doJob(archiveList, callback);
                                                                }
                                                            } else {
                                                                results.withError.push(obj);
                                                                doJob(archiveList, callback);
                                                            }
                                                        } else {
                                                            // archive exists on platform, try to unload it
                                                            if (unloadArchive && archiveData.BAS_ARCHIVE_ID) {
                                                                unloadSingleArchive(req.bas.logger, sessionData, archiveData.BAS_ARCHIVE_ID, {}, function (obj) {
                                                                    if (obj.error) {
                                                                        // req.bas.logger.error("ERROR unloadSingleArchive ", null, obj);
                                                                        archiveData.error = obj.error;
                                                                        results.foundAndUnSync.push(archiveData);
                                                                    } else {
                                                                        // req.bas.logger.info("archive successfully unloaded " + archiveData.PLATFORM_SITE_ID + " " + archiveData.PLATFORM_ARCHIVE_ID);
                                                                        results.foundAndUnloaded.push(archiveData);
                                                                    }
                                                                    doJob(archiveList, callback);
                                                                });
                                                            } else {
                                                                results.foundAndUnSync.push(archiveData);
                                                                doJob(archiveList, callback);
                                                            }
                                                        }
                                                    });
                                                }
                                            });
                                        } else {
                                            callback && callback();
                                        }
                                    };

                                    results.totNumber = obj.rows.length;
                                    if (maxArchives) {
                                        archiveList = obj.rows.slice(0, maxArchives);
                                    } else {
                                        archiveList = obj.rows;
                                    }
                                    results.archiveToProcess = archiveList.length;

                                    req.bas.logger.info("unloadArchiveNotExistingOnPlatform going to check " + archiveList.length + " archives for [" + kind + "]");
                                    doJob(archiveList, function() {
                                        var resultsNotVerbose = {
                                            withError: results.withError.length,
                                            notFound: results.notFound.length,
                                            foundAndUnSync: results.foundAndUnSync.length,
                                            notFoundUnloaded: results.notFoundUnloaded.length,
                                            foundAndUnloaded: results.foundAndUnloaded.length,
                                            totNumber: results.totNumber,
                                            archiveToProcess: results.archiveToProcess
                                        };

                                        req.bas.logger.info("unloadArchiveNotExistingOnPlatform done for [" + kind + "]", resultsNotVerbose);
                                        returnJSON((verbose ? results : resultsNotVerbose), res, sessionData);
                                    });
                                }
                            });
                        } else {
                            req.bas.logger.error("unloadArchiveNotExistingOnPlatform wrong credentials");
                            returnJSON({error: "wrong credentials"}, res, sessionData);
                        }
                    })
                    .catch(err => {
                        req.bas.logger.error("unexpected error while checking server API credentials", err);
                        returnJSON({error: "unexpected error while unloading archive not existing on platform"}, res, sessionData);
                    });
            }
        });
    });

// Parameters, either:
// - platformSiteID + platformArchiveID
// or
// - platformArchiveID + platformKind
// The reason for the latter format is that platforms like Cloud might not have the siteID information anymore by the time this API is called
    app.post('/'+con.API_DELETE_ARCHIVE_OFFLINE, function (req, res) {
        let jsonObj = req.body;
        let platformSiteID = jsonObj.platformSiteID;
        let platformArchiveID = jsonObj.platformArchiveID;
        let platformKind = jsonObj.platformKind;

        const connector = getConnector(platformKind);
        if (!connector) {
            returnJSON({error: "Unknown connector"}, res, null);
            return;
        }

        req.bas.addLoggerInfo({
            action: "deleteArchiveOffline",
            platformArchiveID: platformArchiveID || "",
            platformSiteID: platformSiteID || "",
            platformKind: platformKind || ""
        });

        req.bas.logger.info("delete archive offline");

        sessionManager.createSession(req.bas.logger, "deleteArchiveOffline", function (obj) {
            var sessionData, dbConnector;
            if (obj.error) {
                returnJSON(obj, res, null);
            } else {
                sessionData = obj;
                dbConnector = sessionData.dbConnector;

                req.bas.verifyAdminCredentials()
                    .then(allowed => {
                        if (allowed) {
                            var finalise = function(obj) {
                                var archiveID;
                                if (obj.error) {
                                    returnJSON(obj, res, sessionData);
                                } else {
                                    archiveID = obj.archiveID;
                                    if (archiveID) {
                                        dbConnector.deleteArchivePlatformData(archiveID, function (obj) {
                                            if (obj.error) {
                                                returnJSON(obj, res, sessionData);
                                            } else {
                                                returnJSON({message: "success"}, res, sessionData);
                                            }
                                        });
                                    } else {
                                        req.bas.logger.info("archive does not exist");
                                        returnJSON({message: "archive does not exist"}, res, sessionData);
                                    }
                                }
                            };

                            var getArchiveID = function(platformSiteID, platformArchiveID, finalise) {
                                if (platformSiteID) {
                                    dbConnector.getArchiveIDfromPlatformArchiveID(platformSiteID, platformArchiveID, finalise);
                                } else {
                                    dbConnector.getArchiveIDfromPlatformArchiveIDAndKind(platformArchiveID, platformKind, finalise);
                                }
                            };

                            callSaneFunctionFromLegacy(deletePermalinksByArchiveID({
                                dbConnector,
                                getConnector,
                                platformKind,
                                platformSiteID,
                                platformArchiveID,
                            }), function (obj) {
                                if (obj.error) {
                                    req.bas.logger.error("Unexpected error while deleting permalinks for archive ", new Error(`${obj.error}`));
                                    finalise(obj);
                                } else {
                                    getArchiveID(platformSiteID, platformArchiveID, finalise);
                                }
                            });
                        } else {
                            req.bas.logger.error("unable to delete the archive offline, wrong credentials");
                            returnJSON({error: "wrong credentials"}, res, sessionData);
                        }
                    })
                    .catch(err => {
                        req.bas.logger.error("unexpected error while checking server API credentials", err);
                        returnJSON({error: "unexpected error while deleting archive"}, res, sessionData);
                    });
            }
        });
    });

    app.post('/projectExistsOnPlatform', function (req, res) {
        var jsonObj = req.body;
        var platformSiteID = jsonObj.platformSiteID;
        var platformArchiveID = jsonObj.platformArchiveID;
        var kind = jsonObj.kind;

        req.bas.addLoggerInfo({
            action: "projectExistsOnPlatform",
            platformArchiveID: platformArchiveID || "",
            platformSiteID: platformSiteID || ""
        })
        req.bas.logger.info("check if archive exists");

        sessionManager.createSession(req.bas.logger, "projectExistsOnPlatform", function (obj) {
            var sessionData, dbConnector;
            if (obj.error) {
                returnJSON(obj, res, null);
            } else {
                sessionData = obj;
                dbConnector = sessionData.dbConnector;

                req.bas.verifyAdminCredentials()
                    .then(allowed => {
                        if (allowed) {
                            if (kind && getConnector(kind)) {
                                getConnector(kind).projectExistsOnPlatform(req.bas.logger, dbConnector, platformSiteID, platformArchiveID, function (obj) {
                                    returnJSON(obj, res, sessionData);
                                });
                            } else {
                                req.bas.logger.error("wrong connector kind" + kind);
                                returnJSON({error: "wrong connector kind"}, res, sessionData);
                            }
                        } else {
                            req.bas.logger.error("unable to test archive existence, wrong credentials " + platformArchiveID + ", " + platformSiteID);
                            returnJSON({error: "wrong credentials"}, res, sessionData);
                        }
                    })
                    .catch(err => {
                        req.bas.logger.error("unexpected error while checking API credentials: " + err);
                        returnJSON({error: "unexpected error while checking if project exists"}, res, sessionData);
                    });
            }
        });
    });

    app.post('/'+con.API_FLUSH_OFFLINE, function (req, res) {
        var jsonObj = req.body;
        var platformSiteID = jsonObj.platformSiteID;
        var platformArchiveID = jsonObj.platformArchiveID;

        req.bas.addLoggerInfo({
            action: "flushOffline",
            platformArchiveID: platformArchiveID || "",
            platformSiteID: platformSiteID || ""
        });
        req.bas.logger.info("flush archive offline");

        sessionManager.createSession(req.bas.logger, "flushOffline", function (obj) {
            var sessionData, dbConnector;
            if (obj.error) {
                returnJSON(obj, res, null);
            } else {
                sessionData = obj;
                dbConnector = sessionData.dbConnector;

                req.bas.verifyAdminCredentials()
                    .then(allowed => {
                        if (allowed) {
                            // get platform info data if archive is loaded
                            dbConnector.getBASArchiveID(platformSiteID, platformArchiveID, function (obj) {
                                var archiveID, kind, platformInfo;
                                if (obj.error) {
                                    returnJSON(obj, res, sessionData);
                                } else {
                                    if (obj.BAS_ARCHIVE_ID) {
                                        // archive is loaded
                                        archiveID = obj.BAS_ARCHIVE_ID;
                                        kind = obj.PLATFORM_KIND;
                                        platformInfo = JSON.parse(obj.PLATFORM_INFO);
                                        req.bas.logger.info("archive is loaded " + archiveID);
                                        tryToSyncArchiveOnPlatformOffline(req.bas.logger, sessionData, archiveID, kind, platformInfo, null, function (obj) {
                                            if (obj.error) {
                                                req.bas.logger.warn("unable to sync the archive offline " + archiveID);
                                            } else {
                                                req.bas.logger.info("successfully sync the archive offline " + archiveID);
                                            }
                                            returnJSON(obj, res, sessionData);
                                        });
                                    } else {
                                        // archive is not loaded, nothing to do
                                        returnJSON({message: con.ARCHIVE_NOT_LOADED}, res, sessionData);
                                    }
                                }
                            });
                        } else {
                            req.bas.logger.error("unable to sync the archive offline, wrong credentials " + platformArchiveID + ", " + platformSiteID);
                            returnJSON({error: "wrong credentials"}, res, sessionData);
                        }
                    })
                    .catch(err => {
                        req.bas.logger.error("unexpected error while checking server API credentials", err);
                        returnJSON({error: "unexpected error while flushing project to platform"}, res, sessionData);
                    });
            }
        });
    });

    app.post('/'+con.API_UNLOAD_ARCHIVE, function (req, res) {
        var jsonObj = req.body;
        var platformToken = jsonObj.platformToken;
        var platformSiteID = jsonObj.platformSiteID;
        var platformArchiveID = jsonObj.platformArchiveID;
        var userInfo;
        var kind = jsonObj.kind;

        if (jsonObj.userName) {
            userInfo = {name: jsonObj.userName}
        } else {
            try {
                userInfo = JSON.parse(jsonObj.userInfo);
                if (userInfo && userInfo.name) {
                    // nothing to do
                } else {
                    userInfo = {name: "unknown", isAnonymous: true}
                }
            } catch (e) {
                userInfo = {name: "unknown", isAnonymous: true}
            }
        }

        req.bas.addLoggerInfo({action: "unloadArchive", kind: kind, username: userInfo.name, platformArchiveID: platformArchiveID || "", platformSiteID: platformSiteID || ""});
        if (getConnector(kind)) {
            req.bas.logger.info("unload archive API", jsonObj);
            sessionManager.createSession(req.bas.logger, "unloadArchive", function (obj) {
                var sessionData, dbConnector;
                if (obj.error) {
                    returnJSON(obj, res, null);
                } else {
                    sessionData = obj;
                    dbConnector = sessionData.dbConnector;

                    //Check with the connector to see if this user can open this project
                    getConnector(kind).getRole(req.bas.logger, dbConnector, platformToken, platformSiteID, platformArchiveID, userInfo, function (obj) {
                        var role;
                        if (obj.error) {
                            returnJSON(obj, res, null);
                        } else {
                            role = obj.role;
                            if (role === con.ROLE_NO_ACCESS) {
                                req.bas.logger.error("Project not found, or you don't have permission to view it " + platformArchiveID + " " + platformSiteID);
                                returnJSON({error: "Project not found, or you don't have permission to view it"}, res, sessionData);
                            } else {
                                // is archive loaded?
                                dbConnector.getBASArchiveID(platformSiteID, platformArchiveID, function (obj) {
                                    var archiveID;
                                    if (obj.error) {
                                        returnJSON(obj, res, sessionData);
                                    } else {
                                        if (obj.BAS_ARCHIVE_ID) {
                                            // archive is loaded
                                            archiveID = obj.BAS_ARCHIVE_ID;
                                            req.bas.logger.info("archive is loaded " + archiveID);
                                            // flush the archive on platform, we do not destroy the BAR (and we pin the revision in GD)
                                            // simulate a user session
                                            sessionData.user = {
                                                ARCHIVE_ID: archiveID,
                                                PLATFORM_TOKEN: platformToken
                                            };
                                            flushToConnectorHoldingTheTruth(req.bas.logger, sessionData, archiveID, null, kind, null, true, function (obj) {
                                                if (obj.error) {
                                                    returnJSON(obj, res, sessionData);
                                                } else {
                                                    // park the archive entry (for backup)
                                                    req.bas.logger.info("parking archive for backup " + archiveID);
                                                    dbConnector.parkArchivePlatformData(platformArchiveID, archiveID, function (obj) {
                                                        var message = obj.error ? "ERROR: " + obj.error : "SUCCESS";
                                                        req.bas.logger.info("parked archive for backup: " + message);
                                                        returnJSON(obj, res, sessionData);
                                                    });
                                                }
                                            });
                                        } else {
                                            // archive is not loaded, nothing to do
                                            returnJSON({message: con.ARCHIVE_NOT_LOADED}, res, sessionData);
                                        }
                                    }
                                });
                            }
                        }
                    });
                }
            });
        } else {
            returnJSON({error: "Unknown connector"}, res, null);
        }
    });


    app.post('/'+con.API_UNLOAD_ARCHIVE_IF_NOT_SYNC, function (req, res) {
        var jsonObj = req.body;
        var platformToken = jsonObj.platformToken;
        var platformSiteID = jsonObj.platformSiteID;
        var platformArchiveID = jsonObj.platformArchiveID;
        var modifiedTimestamp = jsonObj.modifiedTimestamp;
        var userInfo;
        var kind = jsonObj.kind;
        var delta = 0;

        const connector = getConnector(kind);
        if (!connector) {
            returnJSON({error: "Unknown connector"}, res, null);
            return;
        }

        if (jsonObj.userName) {
            userInfo = {name: jsonObj.userName}
        } else {
            try {
                userInfo = JSON.parse(jsonObj.userInfo);
                if (userInfo && userInfo.name) {
                    // nothing to do
                } else {
                    userInfo = {name: "unknown", isAnonymous: true}
                }
            } catch (e) {
                userInfo = {name: "unknown", isAnonymous: true}
            }
        }

        req.bas.addLoggerInfo({action: "unloadArchiveIfNotSync", kind: kind, username: userInfo.name, platformArchiveID: platformArchiveID || "", platformSiteID: platformSiteID || "", platformToken: platformToken});
        if (connector && platformArchiveID) {
            req.bas.logger.info("unloadArchiveIfNotSync API", jsonObj);
            sessionManager.createSession(req.bas.logger, "unloadArchiveIfNotSync", function (obj) {
                var sessionData, dbConnector;
                if (obj.error) {
                    returnJSON(obj, res, null);
                } else {
                    sessionData = obj;
                    dbConnector = sessionData.dbConnector;

                    //Check with the connector to see if this user can open this project
                    connector.getRole(req.bas.logger, dbConnector, platformToken, platformSiteID, platformArchiveID, userInfo, function (obj) {
                        var role;
                        if (obj.error) {
                            returnJSON(obj, res, null);
                        } else {
                            role = obj.role;
                            if (role === con.ROLE_NO_ACCESS) {
                                req.bas.logger.error("Project not found, or you don't have permission to view it " + platformArchiveID + " " + platformSiteID);
                                returnJSON({error: "Project not found, or you don't have permission to view it"}, res, sessionData);
                            } else {
                                // is archive loaded?
                                dbConnector.getBASArchiveID(platformSiteID, platformArchiveID, function (obj) {
                                    var archiveID;

                                    if (obj.error) {
                                        returnJSON(obj, res, sessionData);
                                    } else {
                                        if (obj.BAS_ARCHIVE_ID) {
                                            // archive is loaded
                                            archiveID = obj.BAS_ARCHIVE_ID;
                                            // we add a delta to avoid false negative (few seconds)
                                            if (obj.TIMESTAMP < (modifiedTimestamp - delta)) {
                                                // the archive on platform, seems to be more updated than the BMPR loaded on BAS
                                                req.bas.logger.info("archive is out of sync " + archiveID + " " + obj.TIMESTAMP + "<" + modifiedTimestamp);

                                                // retrieve archive revision from BAS
                                                sessionManager.openBarLocked(sessionData, archiveID, "WRITE", function (obj) {
                                                    var bar;
                                                    if (obj.error) {
                                                        returnJSON(obj, res, sessionData);
                                                    } else {
                                                        bar = obj.bar;
                                                        bar.dump(null, function (obj) {
                                                            var dumpOnBas;
                                                            if (obj.error) {
                                                                returnJSON(obj, res, sessionData);
                                                            } else {
                                                                dumpOnBas = obj.dump;
                                                                sessionManager.unlockConnection(sessionData, function (/*objUnlock*/) {
                                                                    // we only load from platform if the archive revision is greater than what is loaded on BAS
                                                                    connector.loadFromPlatform(req.bas.logger, dbConnector, platformToken, platformSiteID, platformArchiveID, null, null, function (obj) {
                                                                        if (obj.error) {
                                                                            req.bas.logger.error("after loadFromPlatform: " + obj.error, null, obj);
                                                                            returnJSON(obj, res, sessionData);
                                                                        } else {
                                                                            sqliteBuffer2object(config.archivesPath, obj.buffer, kind,function (obj) {
                                                                                var dumpOnPlatform;
                                                                                if (obj.error) {
                                                                                    req.bas.logger.error("after sqliteBuffer2object " + obj.error, null, obj);
                                                                                    returnJSON(obj, res, sessionData);
                                                                                } else {
                                                                                    dumpOnPlatform = obj.dump;
                                                                                    if (dumpOnBas.Info.ArchiveRevision < dumpOnPlatform.Info.ArchiveRevision) {
                                                                                        // cannot save the archive loaded on BAS, otherwise we will override the BMPR on platform
                                                                                        // park the archive entry (for backup): e.g. PARKED_0B6uegXUnNpjCTnBLbXZZS21nU2c
                                                                                        req.bas.logger.info("parking archive for backup " + archiveID + " " + platformArchiveID);
                                                                                        dbConnector.parkArchivePlatformData(platformArchiveID, archiveID, function (obj) {
                                                                                            var message = obj.error ? "ERROR: " + obj.error : "SUCCESS";
                                                                                            req.bas.logger.info("parked archive for backup: " + message);
                                                                                            obj.message = con.OUT_OF_DATE;
                                                                                            returnJSON(obj, res, sessionData);
                                                                                        });
                                                                                    } else {
                                                                                        // nothing to do, on BAS there is the most updated version
                                                                                        returnJSON({}, res, sessionData);
                                                                                    }
                                                                                }
                                                                            });
                                                                        }
                                                                    });
                                                                });
                                                            }
                                                        });
                                                    }
                                                });
                                            } else {
                                                returnJSON({}, res, sessionData);
                                            }
                                        } else {
                                            // archive is not loaded, nothing to do
                                            returnJSON({message: con.ARCHIVE_NOT_LOADED}, res, sessionData);
                                        }
                                    }
                                });
                            }
                        }
                    });
                }
            });
        } else {
            req.bas.logger.error("Unknown connector or missing platformArchiveID", null, jsonObj);
            returnJSON({error: "Unknown connector or missing platformArchiveID"}, res, null);
        }
    });

    function closeHandler(req, res) {
        var token = req.query.token;
        var noflush = req.query.noflush;
        const sessionData = req.sessionData;
        req.bas.addLoggerInfo({action: 'close', sessionToken: token});
        var userAgent = req.headers['user-agent'];
        req.bas.logger.info("close " + (userAgent ? userAgent : "") + " " + (noflush ? "noflush" : ""));

        var dbConnector = sessionData.dbConnector;
        var archiveID = sessionData.user.ARCHIVE_ID;

        req.bas.logger.info("closing, delete user for archive " + archiveID );
        dbConnector.deleteSessionByToken(token, function (obj) {
            if (obj.error) {
                returnJSON(obj, res, sessionData);
            } else {
                if (noflush) {
                    // we do not need to flush, we can return now
                    returnJSON(obj, res, sessionData);
                } else {
                    dbConnector.getUsersForArchive(archiveID, function (obj) {
                        if (obj.error) {
                            returnJSON({}, res, sessionData);
                        } else {
                            if (obj.users.length == 0) {
                                req.bas.logger.info("closing, we are the last user for archive " + archiveID);
                            }
                            if (req.platformData)
                            {
                                const obj = req.platformData;
                                var kind;
                                if (obj.error) {
                                    returnJSON(obj, res, sessionData);
                                } else if (obj.PLATFORM_KIND) {
                                    kind = obj.PLATFORM_KIND;
                                    const platformArchiveID = obj.PLATFORM_ARCHIVE_ID;
                                    const platformSiteID = obj.PLATFORM_SITE_ID;
                                    req.bas_kind = kind; // Used later to collect the metrics
                                    if (getConnector(kind)) {
                                        flushToConnectorHoldingTheTruth(req.bas.logger, sessionData, archiveID, null, kind, null, true, function(obj) {
                                            const userIsEditor = sessionData.user && sessionData.user.PERMISSIONS && sessionData.user.PERMISSIONS >= con.ROLE_EDITOR;
                                            if (platformArchiveID && userIsEditor) {
                                                req.bas.logger.info("updatePermalinkImages " + platformArchiveID + " - " + platformSiteID);
                                                // no need to wait answer from updatePermalinkImages because we can run it in parallel
                                                callSaneFunctionFromLegacy(updatePermalinkImages({
                                                    broadcastRTCMessage,
                                                    archiveID: sessionData.user.ARCHIVE_ID,
                                                    userName: sessionData.user.USERNAME,
                                                    dbConnector,
                                                    getConnector,
                                                    platformArchiveID,
                                                    platformKind: kind,
                                                    platformSiteID,
                                                    timestamp: clock.now(),
                                                    logger: req.bas.logger
                                                }), function(obj) {
                                                    if (obj.error) {
                                                        req.bas.logger.error("Unexpected error while updating permalink images ", new Error(`${obj.error}`));
                                                    }
                                                });
                                            }
                                            returnJSON(obj, res, sessionData);
                                        });
                                    } else {
                                        returnJSON({ error: "Unknown connector" }, res, null);
                                    }
                                } else {
                                    // might be already closed by someone else
                                    returnJSON({}, res, sessionData);
                                }
                            }
                        }
                    });
                }
            }
        });
    }
    app.get('/'+con.API_CLOSE,
        createApiAuthPipeline(con.API_CLOSE),
        closeHandler);
    app.post('/'+con.API_CLOSE,
        createApiAuthPipeline(con.API_CLOSE),
        closeHandler);


    app.get('/'+con.API_FLUSH,
        createApiAuthPipeline(con.API_FLUSH),
        function (req, res) {
            var token = req.query.token;
            const sessionData = req.sessionData;
            var options;

            if (req.query.force) {
                options = {
                    force: true
                }
            }
            var dbConnector = sessionData.dbConnector;
            var archiveID = sessionData.user.ARCHIVE_ID;
            var newPlatformArchiveName = req.query.platformArchiveName;
            req.bas.addLoggerInfo({ action: "flush", sessionToken: token });

            req.bas.logger.info("flush " + archiveID + (newPlatformArchiveName ? " with new platformArchiveName " + newPlatformArchiveName : ""));

            var timer = metrics.trackTimeInterval('flush getPlatformData');
            dbConnector.getPlatformData(archiveID, function (obj) {
                timer.stop();
                if (obj.error) {
                    returnJSON(obj, res, sessionData);
                } else if (obj.PLATFORM_KIND) {
                    var kind = obj.PLATFORM_KIND;
                    const platformArchiveID = obj.PLATFORM_ARCHIVE_ID;
                    const platformSiteID = obj.PLATFORM_SITE_ID;
                    req.bas_kind = kind; // Used later to collect the metrics
                    if (getConnector(kind)) {
                        flushToConnectorHoldingTheTruth(req.bas.logger, sessionData, archiveID, newPlatformArchiveName, kind, options, false, function (obj) {
                            // no need to wait answer from updatePermalinkImages because we can run it in parallel
                            if (platformArchiveID) {
                                req.bas.logger.info("updatePermalinkImages " + platformArchiveID + " - " + platformSiteID);
                                callSaneFunctionFromLegacy(updatePermalinkImages({
                                    broadcastRTCMessage, archiveID: sessionData.user.ARCHIVE_ID, userName: sessionData.user.USERNAME, dbConnector, getConnector, platformArchiveID, platformKind: kind, platformSiteID, timestamp: clock.now(), logger: req.bas.logger
                                }), function (obj) {
                                    if (obj.error) {
                                        req.bas.logger.error("Unexpected error while updating permalink images ", new Error(`${obj.error}`));
                                    }
                                });
                            }
                            returnJSON(obj, res, sessionData);
                        });
                    } else {
                        returnJSON({ error: "Unknown connector" }, res, null);
                    }
                } else {
                    //might be already closed by someone else
                    returnJSON({}, res, sessionData);
                }
            });
    });

    app.post('/'+con.API_BROADCASTMESSAGE, function (req, res) {
        let messageObj;
        try {
            messageObj = JSON.parse(req.body.message);
        } catch (e) {
            messageObj = {error: "Malformed parameters"};
            returnJSON({error: "Malformed parameters"}, res);
            return;
        }

        let token = req.query.token;
        req.bas.addLoggerInfo({action: con.API_BROADCASTMESSAGE, sessionToken: token});
        // req.bas.logger.info("message to broadcast: " + messageObj);

        if (messageObj.type === "chat") {
            req.bas.logger.error(con.API_BROADCASTMESSAGE + ", chat feature has been dismissed");
            returnJSON({error: "Unknown message type"}, res);
            return;
        }

        checkValidUser(con.API_BROADCASTMESSAGE, req, res, function (sessionData) {
            let archiveID = sessionData.user.ARCHIVE_ID;
            let internalUserID = sessionData.user.INTERNAL_ID;
            let username = sessionData.user.USERNAME;

            req.bas.addLoggerInfo({archiveID});

            if (isAnonymousUser(sessionData.user)) {
                // TODO: return an error when BW editor is fixed? Or we should allow anonymous user to send msg to notify which wireframe they select?
                // https://www.pivotaltracker.com/story/show/181113648
                req.bas.logger.warn(con.API_BROADCASTMESSAGE + " anonymous user should not send broadcast messages");
                returnJSON({warning: "not authorized "}, res, sessionData);
            } else {
                sessionManager.releaseSession(sessionData, function (obj) {
                    if (obj.error) {
                        // log about it, continue the job
                        req.bas.logger.error(con.API_BROADCASTMESSAGE + " failed on releasing db session " + obj.error);
                    }

                    // broadcast the message
                    broadcastRTCMessage(archiveID, null, internalUserID, username, {
                        operation: con.API_BROADCASTMESSAGE,
                        // the timestamp is now set for all messages
                        // timestamp: clock.now(),
                        message: messageObj
                    }, function (obj) {
                        if (obj.error) {
                            returnJSON(obj, res, null);
                        } else {
                            // do not log the message
                            messageObj.ignore = true;
                            returnJSON(messageObj, res, null);
                        }
                    });
                });
            }
        });
    });

    app.get('/'+con.API_GETUSERINFO, function (req, res) {
        var token = req.query.token;
        var internalID = req.query.internalID;
        req.bas.addLoggerInfo({action: "getUserInfo", sessionToken: token});
        req.bas.logger.info("getUserInfo internalID" + internalID);

        checkValidUser(con.API_GETUSERINFO, req, res, function (sessionData) {
            var dbConnector = sessionData.dbConnector;
            dbConnector.getUserInfo(internalID, function(obj) {
                returnJSON(obj, res, sessionData);
            });
        });
    });

    app.post('/'+con.API_UPDATEUSERINFO, function (req, res) {
        var userInfo = req.body;
        var token = req.query.token;
        req.bas.addLoggerInfo({action: con.API_UPDATEUSERINFO, sessionToken: token});
        req.bas.logger.info(con.API_UPDATEUSERINFO + " for current session " + userInfo.name + " " + userInfo.displayName);

        checkValidUser(con.API_UPDATEUSERINFO, req, res, function(sessionData)
        {
            var dbConnector = sessionData.dbConnector;
            var internalUserID = sessionData.user.INTERNAL_ID;

            dbConnector.updateUserInfo(internalUserID, userInfo, function (obj) {
                if (obj.error) {
                    returnJSON(obj, res, sessionData);
                } else {
                    returnJSON({success: "ok"}, res, sessionData);
                }
            });
        });
    });

    app.post('/'+con.API_UPLOADTEMPFILE, function (req, res) {
        var jsonObj = req.body; //{data}
        var data = Buffer.from(jsonObj.data, 'base64');
        var filename = jsonObj.filename;
        var mimetype = jsonObj.mimetype;
        var token = req.query.token;
        req.bas.addLoggerInfo({action: con.API_UPLOADTEMPFILE, sessionToken: token});
        req.bas.logger.info(con.API_UPLOADTEMPFILE + " " + token);

        checkValidUser(con.API_UPLOADTEMPFILE, req, res, function(sessionData) {
            const key = getUUID();
            req.bas.logger.info(con.API_UPLOADTEMPFILE + " " + token + " connected");
            let result;
            redisAdapter
                .setex(key, 3600, JSON.stringify({ mimetype: mimetype, data: data, filename: filename}))
                .then(_ => {
                    req.bas.logger.info(con.API_UPLOADTEMPFILE + " " + token + " setex OK " + key);
                    result = {key: key};
                })
                .catch(err => {
                    req.bas.logger.error(con.API_UPLOADTEMPFILE + " " + token + " setex " + redisObj.error);
                    result = {error: err.message};
                })
                .finally(() => {
                    returnJSON(result, res, sessionData);
                });
        });
    });

    app.get('/'+con.API_DOWNLOADTEMPFILE, function (req, res) {
        const { token, key } = req.query;
        req.bas.addLoggerInfo({action: con.API_DOWNLOADTEMPFILE, sessionToken: token});
        req.bas.logger.info(`${con.API_DOWNLOADTEMPFILE} key: ${key}`);
        const GENERIC_ERROR_MESSAGE = 'Unexpected error: unable to download temporary file';

        const safeKey = xss(key); //NODE(Snyk SAST) - avoid xss reports
        checkValidUser(con.API_DOWNLOADTEMPFILE, req, res, function (sessionData) {
            sessionManager.releaseSession(sessionData, function (sessionObj) {
                if (sessionObj.error) {
                    // we log error but we try to complete the call
                    req.bas.logger.error("sessionManager.releaseSession failed " + sessionObj.error);
                }

                redisAdapter
                    .get(safeKey)
                    .then(result => {
                        if (result === null) {
                            req.bas.logger.info(`key ${safeKey} not found`);
                            res.status(404).send('File not found');
                            return;
                        }

                        let filename, data, mimetype;
                        try {
                            const jsonResult = JSON.parse(result);
                            filename = encodeURIComponent(jsonResult.filename);
                            data = Buffer.from(jsonResult.data);
                            mimetype = jsonResult.mimetype;
                        } catch (err) {
                            req.bas.logger.error("Failed to parse temp file payload", err, { payload: result });
                            res.status(500).send(GENERIC_ERROR_MESSAGE);
                            return;
                        }
                        res.setHeader('Content-Type', mimetype);
                        res.setHeader('Content-Disposition', 'attachment;filename*=UTF-8\'\'' + filename);
                        res.setHeader('Content-Length', Buffer.byteLength(data));
                        res.setHeader('Cache-Control', 'no-cache, no-store');
                        res.write(data);
                        res.end();
                    })
                    .catch(err => {
                        req.bas.logger.error(`Failed to get redis key: ${safeKey}`, err);
                        res.status(500).send(GENERIC_ERROR_MESSAGE);
                    });
            });
        });
    });

    app.post('/'+con.API_DELETE,
        createApiAuthPipeline(con.API_DELETE),
        function (req, res) {
        var token = req.query.token;
        var jsonObj = req.body;
        var platformToken = jsonObj.platformToken;
        var platformSiteID = jsonObj.platformSiteID || "";
        var platformArchiveID = jsonObj.platformArchiveID;
        const sessionData = req.sessionData;
        var kind = jsonObj.kind;
        req.bas.addLoggerInfo({action: "delete", sessionToken: token, kind: kind});

        req.bas.logger.info("delete platformSiteID " + platformSiteID + ", platformArchiveID " + platformArchiveID);

        const connector = getConnector(kind);
        if (!connector) {
            returnJSON({error: "Unknown connector"}, res, null);
            return;
        }

        var dbConnector = sessionData.dbConnector;
        callSaneFunctionFromLegacy(deletePermalinksByArchiveID({
            dbConnector,
            getConnector,
            platformKind: kind,
            platformSiteID,
            platformArchiveID,
        }), function (obj) {
            if (obj.error) {
                req.bas.logger.info("Unexpected error while deleting permalinks for archive " + obj.error);
                returnJSON(obj, res, sessionData);
            } else {
                connector.deleteFromPlatform(req.bas.logger, dbConnector, platformToken, platformSiteID, platformArchiveID, function (obj) {
                    if (obj.error) {
                        returnJSON(obj, res, sessionData);
                    } else {
                        dbConnector.getArchiveIDfromPlatformArchiveID(platformSiteID, platformArchiveID, function(obj) {
                            var archiveID;
                            if (obj.error) {
                                returnJSON(obj, res, sessionData);
                            } else {
                                archiveID = obj.archiveID;
                                dbConnector.deleteArchivePlatformData(archiveID, function(obj) {
                                    if (obj.error) {
                                        returnJSON(obj, res, sessionData);
                                    } else {
                                        // at the moment we do not delete the sessions and the bar, they will be destroyed by the gardening job
                                        var internalUserID = sessionData.user.INTERNAL_ID;
                                        var username = sessionData.user.USERNAME;
                                        var objToBroadcast = {
                                            operation: 'archiveDeletedFromPlatform',
                                            platformArchiveID: platformArchiveID
                                        };
                                        broadcastRTCMessage(archiveID, "", internalUserID, username, objToBroadcast, function (obj) {
                                            if (obj.error) {
                                                req.bas.logger.error("delete, failed to broadcast RTC message for archive " + archiveID);
                                            }
                                            returnJSON({}, res, sessionData);
                                        });
                                    }
                                });
                            }
                        });
                    }
                });
            }
        });
    });

    
    app.get('/getArchiveRevision', function (req, res) {
        var token = req.query.token;
        var bar;
        req.bas.addLoggerInfo({action: con.API_GETARCHIVEREVISION, sessionToken: token});
        req.bas.logger.info('getArchiveRevision');
        checkValidUser(con.API_GETARCHIVEREVISION, req, res, function (sessionData) {
            var archiveID = sessionData.user.ARCHIVE_ID;
            sessionManager.openBarLocked(sessionData, archiveID, "READ", function (obj) {
                if (obj.error) {
                    returnJSON(obj, res, sessionData);
                }
                else {
                    bar = obj.bar;
                    bar.getArchiveRevision(function (obj) {
                        returnJSON(obj, res, sessionData);
                    });
                }
            });
        });
    });

    app.post('/'+con.API_SETARCHIVEATTRIBUTES, function (req, res) {
        var jsonObj = req.body; //{attributes}
        var attributes;
        var token = req.query.token;
        let logger = req.bas.logger.getLogger({action: "setArchiveAttributes", sessionToken: token});
        logger.info("setArchiveAttributes");
        try {
            attributes = JSON.parse(jsonObj.attributes);
        } catch (e) {
            returnJSON({error: e.message}, res);
            return;
        }
        checkValidUser(con.API_SETARCHIVEATTRIBUTES, req, res, function(sessionData)
        {
            var dbConnector = sessionData.dbConnector;
            var archiveID = sessionData.user.ARCHIVE_ID;
            var internalUserID = sessionData.user.INTERNAL_ID;
            var username = sessionData.user.USERNAME;
            var authToken = sessionData.user.PLATFORM_TOKEN;  // token is different for each user

            dbConnector.getPlatformData(archiveID, function (platformData) {
                if (platformData.error) {
                    returnJSON(platformData, res, sessionData);
                }
                else if (platformData.PLATFORM_KIND) {
                    var kind = platformData.PLATFORM_KIND;
                    if (!getConnector(kind)) {
                        returnJSON({error: "Unknown connector"}, res, null);
                        return;
                    }
                    getConnector(kind).aboutToSetArchiveAttributes(logger, sessionData, authToken, attributes, platformData, dbConnector, function(obj) {
                        if (obj.error) {
                            returnJSON(obj, res, sessionData);
                        } else {
                            //checkValidUser only calls back if it worked, no need to check obj.error
                            openBarLockedAndSetBar(logger, sessionData, res, jsonObj.archiveRevision, function (sessionData, obj) {
                                var bar = obj.bar;
                                // need WRITE LOCK
                                bar.setArchiveAttributes(attributes, function (obj) {
                                    if (obj.error) {
                                        returnJSON(obj, res, sessionData);
                                    } else {
                                        broadcastRTCMessage(archiveID, obj.archiveRevision, internalUserID, username, {operation: 'setArchiveAttributes', attributes: attributes});
                                        returnJSON(obj, res, sessionData);
                                    }
                                });
                            });
                        }
                    });
                } else {
                    //might be already closed by someone else
                    returnJSON({error: "Unexpected error: archive already closed"}, res, sessionData);
                }
            });

        });

    });
    /*
    app.get('/getBranches', function (req, res) {
    var token = req.query.token;
    req.bas.logger.info("/getBranches [" + token + "]");
    checkValidUser(req, res, function (userObj) {
    var archiveID = userObj.ARCHIVE_ID;

    getBar(archiveID, function (obj) {
    if (obj.error) {
    returnJSON(obj, res);
    }
    else {
    var bar = obj.bar;
    bar.getBranches(function (obj) {
    returnJSON(obj, res);
    });
    }
    });
    });
    });
    */

    app.get('/'+con.API_GETBRANCHATTRIBUTES, function (req, res) {
    var branchID = req.query.branchID;
    var token = req.query.token;
    req.bas.logger.info("/getBranchAttributes' [" + token + "]");

    checkValidUser(con.API_GETBRANCHATTRIBUTES, req, res, function (sessionData) {
        var archiveID = sessionData.user.ARCHIVE_ID;

        sessionManager.openBarLocked(sessionData, archiveID, "READ", function (obj) {
            if (obj.error) {
                returnJSON(obj, res, sessionData);
            }
            else {
                var bar = obj.bar;
                // need READ LOCK
                bar.getBranchAttributes(branchID, function (obj) {
                    returnJSON(obj, res, sessionData);
                });
            }
        });
    });
    });

    app.post('/'+con.API_SETBRANCHATTRIBUTES, function (req, res) {
        var jsonObj = req.body;
        var branchID = jsonObj.branchID;
        var attributes;
        var token = req.query.token;
        let logger = req.bas.logger.getLogger({action: "setBranchAttributes", sessionToken: token});
        logger.info("setBranchAttributes for branch " + branchID);

        try {
            attributes = JSON.parse(jsonObj.attributes);
        } catch (e) {
            returnJSON({error: e.message}, res);
            return;
        }

        checkUserAndOpenBarForWriting(logger, con.API_SETBRANCHATTRIBUTES, req, res, jsonObj.archiveRevision, function (sessionData, obj) {
            var archiveID = sessionData.user.ARCHIVE_ID;
            var internalUserID = sessionData.user.INTERNAL_ID;
            var username = sessionData.user.USERNAME;
            var bar = obj.bar;
            // need WRITE LOCK
            bar.setBranchAttributes(branchID, attributes, function (obj) {
                if (obj.error) {
                    returnJSON(obj, res, sessionData);
                } else {
                    broadcastRTCMessage(archiveID, obj.archiveRevision, internalUserID, username, {operation: 'setBranchAttributes', branchID: branchID, attributes: attributes});
                    returnJSON(obj, res, sessionData);
                }
            });
        });
    });

    app.post('/'+con.API_CREATERESOURCE, function (req, res) {
        var jsonObj = req.body; //{branchID, attributes, data}
        var branchID = jsonObj.branchID;
        var resourceID = jsonObj.resourceID;
        var attributes;
        var data = jsonObj.data;                //optional
        var token = req.query.token;
        let logger = req.bas.logger.getLogger({action: "createResource", sessionToken: token});
        logger.info("createResource, resourceID " + jsonObj.resourceID + " branchID " + branchID);

        try {
            attributes = jsonObj.attributes ? JSON.parse(jsonObj.attributes) : undefined;    //optional
        } catch(e) {
            returnJSON({error: e.message}, res);
            return;
        }

        checkUserAndOpenBarForWriting(logger, con.API_CREATERESOURCE, req, res, jsonObj.archiveRevision, function (sessionData, obj) {
            var archiveID = sessionData.user.ARCHIVE_ID;
            var internalUserID = sessionData.user.INTERNAL_ID;
            var username = sessionData.user.USERNAME;
            var bar = obj.bar;

            // need WRITE LOCK
            bar.createResource(resourceID, branchID, attributes, data, function (obj) {
                if (obj.error) {
                    returnJSON(obj, res, sessionData);
                } else {
                    bar.getHeuristicArchiveSize(function (objSize) {
                        if (objSize.error) {
                            logger.error("error calculating archive heuristic size: " + objSize.error);
                        } else {
                            logger.info("archive heuristic size: " + objSize.heuristicArchiveSize);
                            obj.heuristicArchiveSize = objSize.heuristicArchiveSize;
                        }
                        broadcastRTCMessage(archiveID, obj.archiveRevision, internalUserID, username, {
                            operation: 'createResource',
                            branchID: branchID,
                            resourceID: resourceID,
                            attributes: attributes,
                            heuristicArchiveSize: obj.heuristicArchiveSize
                        });
                        returnJSON(obj, res, sessionData);
                    });
                }
            });
        });
    });

    app.post('/'+con.API_CREATEBRANCH, function (req, res) {
        var jsonObj = req.body; //{branchID, attributes, data}
        var branchID = jsonObj.branchID;
        var attributes;
        var token = req.query.token;
        let logger = req.bas.logger.getLogger({action: "createBranch", sessionToken: token});
        logger.info("createBranch for branch " + branchID);

        try {
            attributes = jsonObj.attributes ? JSON.parse(jsonObj.attributes) : undefined;    //optional
        } catch(e) {
            returnJSON({error: e.message}, res);
            return;
        }

        checkUserAndOpenBarForWriting(logger, con.API_CREATEBRANCH, req, res, jsonObj.archiveRevision, function (sessionData, obj) {
            var archiveID = sessionData.user.ARCHIVE_ID;
            var internalUserID = sessionData.user.INTERNAL_ID;
            var username = sessionData.user.USERNAME;
            var bar = obj.bar;

            // need WRITE LOCK
            bar.createBranch(branchID, attributes, function (obj) {
                if (obj.error) {
                    returnJSON(obj, res, sessionData);
                } else {
                    broadcastRTCMessage(archiveID, obj.archiveRevision, internalUserID, username, {operation: 'createBranch', branchID: branchID, attributes:attributes});
                    returnJSON(obj, res, sessionData);
                }
            });
        });
    });

    app.post('/'+con.API_DELETERESOURCES, function (req, res) {
        var jsonObj = req.body; //{branchID, attributes, data}
        var branchID = jsonObj.branchID;
        var resourceIDs;
        var token = req.query.token;
        let logger = req.bas.logger.getLogger({action: "deleteResources", sessionToken: token});
        logger.info("deleteResources for branch " + branchID);
        try {
            resourceIDs = JSON.parse(jsonObj.resourceIDs);
        } catch (e) {
            returnJSON({error: e.message}, res);
            return;
        }

        checkUserAndOpenBarForWriting(logger, con.API_DELETERESOURCES, req, res, jsonObj.archiveRevision, function (sessionData, obj) {
            var archiveID = sessionData.user.ARCHIVE_ID;
            var internalUserID = sessionData.user.INTERNAL_ID;
            var username = sessionData.user.USERNAME;
            var bar = obj.bar;
            var dbConnector = sessionData.dbConnector;
            dbConnector.getPlatformData(archiveID, function (obj) {
                if (obj.error) {
                    returnJSON(obj, res, sessionData);
                } else if (obj.PLATFORM_KIND) {
                    const platformSiteID = obj.PLATFORM_SITE_ID;
                    const platformArchiveID = obj.PLATFORM_ARCHIVE_ID;
                    const platformKind = obj.PLATFORM_KIND;

                    // need WRITE LOCK
                    bar.deleteResources(resourceIDs, branchID, function (obj) {
                        if (obj.error) {
                            returnJSON(obj, res, sessionData);
                        } else {
                            callSaneFunctionFromLegacy(deletePermalinksByResourceIDs({
                                dbConnector,
                                getConnector,
                                platformKind,
                                platformSiteID,
                                platformArchiveID,
                                resourceIDs,
                                branchID,
                                permalinkKind: Consts.PermalinkKind.image_unfurling
                            }), function (resp) {
                                if (resp.error) {
                                    logger.error("Unexpected error while deleting permalinks", new Error(`${resp.error}`));
                                }
                                else {
                                    if (resp.affectedRows && resp.affectedRows >= 1) {
                                        logger.info("deleted image unfurling");
                                    }
                                }
                                bar.getHeuristicArchiveSize(function (objSize) {
                                    if (objSize.error) {
                                        logger.error("error calculating archive heuristic size: " + objSize.error);
                                    } else {
                                        logger.info("archive heuristic size: " + objSize.heuristicArchiveSize);
                                        obj.heuristicArchiveSize = objSize.heuristicArchiveSize;
                                    }
                                    broadcastRTCMessage(archiveID, obj.archiveRevision, internalUserID, username, {
                                        operation: 'deleteResources',
                                        resourceIDs: resourceIDs,
                                        branchID: branchID,
                                        heuristicArchiveSize: obj.heuristicArchiveSize
                                    });
                                    returnJSON(obj, res, sessionData);
                                });
                            });
                        }
                    });
                } else {
                    //might be already closed by someone else
                    returnJSON({error: "Unexpected error: archive already closed"}, res, sessionData);
                }
            });
        });
    });

    app.get('/'+con.API_GETRESOURCEATTRIBUTES, function (req, res) {
        var branchID = req.query.branchID;
        var resourceID = req.query.resourceID;
        var token = req.query.token;
        let logger = req.bas.logger.getLogger({action: "getResourceAttributes", sessionToken: token});
        logger.info("getResourceAttributes for resource " + resourceID + " branchID" + branchID);

        checkValidUser(con.API_GETRESOURCEATTRIBUTES, req, res, function (sessionData) {
            var archiveID = sessionData.user.ARCHIVE_ID;

            sessionManager.openBarLocked(sessionData, archiveID, "READ", function (obj) {
                if (obj.error) {
                    returnJSON(obj, res, sessionData);
                }
                else {
                    var bar = obj.bar;
                    // need READ LOCK
                    bar.getResourceAttributes(resourceID, branchID, function (obj) {
                        returnJSON(obj, res, sessionData);
                    });
                }
            });
        });
    });

    app.post('/'+con.API_SETRESOURCEATTRIBUTES, function (req, res) {
        var jsonObj = req.body; //{branchID, resourceID, resourceData}
        var resourceID = jsonObj.resourceID;
        var branchID = jsonObj.branchID;
        var attributes;
        var token = req.query.token;
        let logger = req.bas.logger.getLogger({action: "setResourceAttributes", sessionToken: token});

        try {
            attributes = JSON.parse(jsonObj.attributes);
        } catch(e) {
            returnJSON({error: e.message}, res);
            return;
        }

        checkUserAndOpenBarForWriting(logger, con.API_SETRESOURCEATTRIBUTES, req, res, jsonObj.archiveRevision, function (sessionData, obj) {
            var archiveID = sessionData.user.ARCHIVE_ID;
            var internalUserID = sessionData.user.INTERNAL_ID;
            var username = sessionData.user.USERNAME;
            var bar = obj.bar;
            logger.updateParams({archiveID});
            logger.info("setResourceAttributes for resource " + resourceID + " branchID " + branchID);

            // need WRITE LOCK
            bar.setResourceAttributes(resourceID, branchID, attributes, function (obj) {
                if (obj.error) {
                    returnJSON(obj, res, sessionData);
                } else {
                    if (attributes.trashed) {
                        let dbConnector = sessionData.dbConnector;
                        dbConnector.getPlatformData(archiveID, function (obj) {
                            if (obj.error) {
                                logger.error("Unexpected error on getPlatformData", new Error(`${obj.error}`));
                            } else if (obj.PLATFORM_KIND) {
                                const platformSiteID = obj.PLATFORM_SITE_ID;
                                const platformArchiveID = obj.PLATFORM_ARCHIVE_ID;
                                const platformKind = obj.PLATFORM_KIND;

                                callSaneFunctionFromLegacy(deletePermalinksByResourceIDs({
                                    dbConnector,
                                    getConnector,
                                    platformKind,
                                    platformSiteID,
                                    platformArchiveID,
                                    resourceIDs: [resourceID],
                                    branchID,
                                    permalinkKind: Consts.PermalinkKind.image_unfurling
                                }), function (resp) {
                                    if (resp.error) {
                                        logger.error("Unexpected error while deleting permalinks", new Error(`${resp.error}`));
                                    }
                                    else {
                                        if (resp.affectedRows && resp.affectedRows >= 1) {
                                            logger.info("deleted image unfurling for resource " + resourceID + " branchID " + branchID);
                                        }
                                    }
                                });
                            }
                        });
                    }
                    broadcastRTCMessage(archiveID, obj.archiveRevision, internalUserID, username, {operation: 'setResourceAttributes', resourceID: resourceID, branchID: branchID, attributes: attributes});
                    returnJSON(obj, res, sessionData);
                }
            });
        });
    });

    app.post('/'+con.API_SETRESOURCEABRANCHID, function (req, res) {
        var jsonObj = req.body; //{branchID, resourceID, resourceData}
        var resourceID = jsonObj.resourceID;
        var oldBranchID = jsonObj.oldBranchID;
        var newBranchID = jsonObj.newBranchID;
        var token = req.query.token;
        let logger = req.bas.logger.getLogger({action: "setResourceBranchID", sessionToken: token});
        logger.info("setResourceBranchID for resource " + resourceID + " oldBranchID" + oldBranchID + " newBranchID " + newBranchID);

        checkUserAndOpenBarForWriting(logger, con.API_SETRESOURCEABRANCHID, req, res, jsonObj.archiveRevision, function (sessionData, obj) {
            var archiveID = sessionData.user.ARCHIVE_ID;
            var internalUserID = sessionData.user.INTERNAL_ID;
            var username = sessionData.user.USERNAME;
            var bar = obj.bar;

            // need WRITE LOCK
            bar.setResourceBranchID(resourceID, oldBranchID, newBranchID, function (obj) {
                if (obj.error) {
                    returnJSON(obj, res, sessionData);
                } else {
                    broadcastRTCMessage(archiveID, obj.archiveRevision, internalUserID, username, {operation: 'setResourceBranchID', resourceID: resourceID, oldBranchID: oldBranchID, newBranchID : newBranchID});
                    returnJSON(obj, res, sessionData);
                }
            });
        });
    });

    app.get('/'+con.API_GETTOC, function (req, res) {
        var branchID = req.query.branchID;
        var token = req.query.token;
        let logger = req.bas.logger.getLogger({action: "getTOC", sessionToken: token});
        logger.info("getTOC for branch " + branchID);

        checkValidUser(con.API_GETTOC, req, res, function (sessionData) {
            var archiveID = sessionData.user.ARCHIVE_ID;

            sessionManager.openBarLocked(sessionData, archiveID, "READ", function (obj) {
                if (obj.error) {
                    returnJSON(obj, res, sessionData);
                }
                else {
                    var bar = obj.bar;
                    bar.getToc(branchID, function (obj) {
                        returnJSON(obj, res, sessionData);
                    });
                }
            });
        });
    });


    app.post('/'+con.API_GETRESOURCESDATA, function (req, res) {
        var jsonObj = req.body;
        var branchID = jsonObj.branchID;
        var resourceIDs;

        // var token = req.query.token;
        // req.bas.logger.info("getResourceData for branch " + branchID, {action: "getResourceData", sessionToken: token});

        try {
            resourceIDs = JSON.parse(jsonObj.resourceIDs);
        } catch (e) {
            returnJSON({error: e.message}, res);
            return;
        }

        checkValidUser(con.API_GETRESOURCESDATA, req, res, function (sessionData) {
            var archiveID = sessionData.user.ARCHIVE_ID;

            sessionData.end2endTimer = metrics.trackTimeInterval('getResourcesData end2end');

            sessionManager.openBarLocked(sessionData, archiveID, "READ", function (obj) {
                var timer;
                if (obj.error) {
                    returnJSON(obj, res, sessionData);
                }
                else {
                    var bar = obj.bar;
                    timer = metrics.trackTimeInterval('getResourcesData BAR-getResourcesData');
                    bar.getResourcesData(resourceIDs, branchID, function (obj) {
                        timer && timer.stop();
                        returnJSON(obj, res, sessionData);
                    });
                }
            });
        });
    });


    app.post('/'+con.API_SETRESOURCEADATA, function (req, res) {
        var jsonObj = req.body; //{branchID, resourceID, resourceData}
        var resourceID = jsonObj.resourceID;
        var branchID = jsonObj.branchID;
        var resourceData = jsonObj.data;
        let logger = req.bas.logger.getLogger({action: 'setToc'});
        //var token = req.query.token;
        //req.bas.logger.info("/setResourceData [" + token + "]");

        checkUserAndOpenBarForWriting(logger, con.API_SETRESOURCEADATA, req, res, jsonObj.archiveRevision, function (sessionData, obj) {
            var archiveID = sessionData.user.ARCHIVE_ID;
            var internalUserID = sessionData.user.INTERNAL_ID;
            var username = sessionData.user.USERNAME;
            var bar = obj.bar;

            // need WRITE LOCK
            bar.setResourceData(resourceID, branchID, resourceData, function (obj) {
                if (obj.error) {
                    returnJSON(obj, res, sessionData);
                }
                else {
                    broadcastRTCMessage(archiveID, obj.archiveRevision, internalUserID, username, {operation: 'setResourceData', resourceID: resourceID, branchID: branchID});
                    returnJSON(obj, res, sessionData);
                }
            });
        });
    });

    // comments
    app.post('/'+con.API_CREATECOMMENT, function (req, res) {
        var jsonObj = req.body;
        var commentID = jsonObj.commentID;
        var resourceID = jsonObj.resourceID;
        var branchID = jsonObj.branchID;
        var commentParentID = jsonObj.commentParentID;
        var commentData = jsonObj.data;  // commentData is plain string
        var token = req.query.token;
        let logger = req.bas.logger.getLogger({action: "createComment", sessionToken: token});
        logger.info("createComment for resource " + resourceID + " branch " + branchID);

        checkUserAndOpenBarForWriting(logger, con.API_CREATECOMMENT, req, res, jsonObj.archiveRevision, function (sessionData, obj) {
            var archiveID = sessionData.user.ARCHIVE_ID;
            var internalUserID = sessionData.user.INTERNAL_ID;
            var username = sessionData.user.USERNAME;
            var bar = obj.bar;

            // need WRITE LOCK
            bar.createComment(commentID, resourceID, branchID, commentParentID, commentData, function (obj) {
                if (obj.error) {
                    returnJSON(obj, res, sessionData);
                } else {
                    broadcastRTCMessage(archiveID, obj.archiveRevision, internalUserID, username, {operation: con.API_CREATECOMMENT, commentID: commentID, resourceID: resourceID, branchID: branchID, commentParentID: commentParentID});
                    returnJSON(obj, res, sessionData);
                }
            });
        });
    });

    app.post('/'+con.API_IMPORTCOMMENT, function (req, res) {
        var jsonObj = req.body;
        var resourceID = jsonObj.resourceID;
        var branchID = jsonObj.branchID;
        var comment;
        try {
            comment = JSON.parse(jsonObj.comment);
        } catch (e) {
            return returnJSON({error: e.message}, res);
        }
        var token = req.query.token;
        let logger = req.bas.logger.getLogger({action: "importComment", sessionToken: token});
        logger.info("importComment for resource " + resourceID + " branch " + branchID);

        checkUserAndOpenBarForWriting(logger, con.API_IMPORTCOMMENT, req, res, jsonObj.archiveRevision, function (sessionData, obj) {
            var archiveID = sessionData.user.ARCHIVE_ID;
            var internalUserID = sessionData.user.INTERNAL_ID;
            var username = sessionData.user.USERNAME;
            var bar = obj.bar;

            if (bar.importComment) {
                bar.importComment(resourceID, branchID, comment, function (obj) {
                    if (obj.error) {
                        returnJSON(obj, res, sessionData);
                    } else {
                        broadcastRTCMessage(archiveID, obj.archiveRevision, internalUserID, username, {
                            operation: con.API_IMPORTCOMMENT,
                            comment: comment.commentID,
                            resourceID: resourceID,
                            branchID: branchID
                        });
                        returnJSON(obj, res, sessionData);
                    }
                });
            } else {
                returnJSON({error: "Unsupported API"}, res, sessionData);
            }
        });
    });

    app.post('/'+con.API_GETCOMMENTSDATA, function (req, res) {
        var jsonObj = req.body;
        var commentIDs;
        var token = req.query.token;
        let logger = req.bas.logger.getLogger({action: "getCommentsData", sessionToken: token});
        logger.info("getCommentsData");

        try {
            commentIDs = JSON.parse(jsonObj.commentIDs);
        } catch (e) {
            returnJSON({error: e.message}, res);
            return;
        }

        checkValidUser(con.API_GETCOMMENTSDATA, req, res, function (sessionData) {
            var archiveID = sessionData.user.ARCHIVE_ID;

            sessionManager.openBarLocked(sessionData, archiveID, "READ", function (obj) {
                if (obj.error) {
                    returnJSON(obj, res, sessionData);
                }
                else {
                    var bar = obj.bar;
                    bar.getCommentsData(commentIDs, function (obj) {
                        returnJSON(obj, res, sessionData);
                    });
                }
            });
        });
    });



    let createOrUpdateUserForComment = function(req, res, apiName) {
        let jsonObj = req.body;
        let userInfoPassedViaAPI = {}, userInfoInSession, userInfoUpdated;
        let token = req.query.token;
        let logger = req.bas.logger.getLogger({action: apiName, sessionToken: token})
        logger.info(apiName);

        if (jsonObj.updatedUserInfo) {
            try {
                userInfoPassedViaAPI = JSON.parse(jsonObj.updatedUserInfo);
            } catch (e) {
                returnJSON({error: "Malformed user info"}, res);
                return;
            }
        }

        checkUserAndOpenBarForWriting(logger, apiName, req, res, jsonObj.archiveRevision, function (sessionData, obj) {
            const archiveID = sessionData.user.ARCHIVE_ID;
            const internalUserID = sessionData.user.INTERNAL_ID;
            const username = sessionData.user.USERNAME;
            let bar = obj.bar;

            try {
                userInfoInSession = JSON.parse(sessionData.user.USERINFO);
            } catch (e) {
                returnJSON({error: "Malformed user info stored in session"}, res);
                return;
            }

            if (isAnonymousUser(sessionData.user)) {
                // if the user is anonymous, we relay on the info passed via the API, but we avoid that the "userName' and the "anonymous" fields being forged
                userInfoUpdated = userInfoPassedViaAPI;
                userInfoUpdated.userName = userInfoInSession.name;
                userInfoUpdated.anonymous = true;
            } else {
                // if the user is not anonymous, we rely on the user info present in the session
                userInfoUpdated = {...userInfoPassedViaAPI}
                if (userInfoUpdated.name) {
                    // legacy nomenclature
                    userInfoUpdated.name = userInfoInSession.name;
                } else {
                    userInfoUpdated.userName = userInfoInSession.name;
                }

                userInfoUpdated.displayName = userInfoInSession.displayName;

                if (userInfoUpdated.anonymous) {
                    userInfoUpdated.anonymous = userInfoInSession.anonymous;
                }

                if (userInfoUpdated.email) {
                    userInfoUpdated.email = userInfoInSession.email;
                }

                if (userInfoUpdated.avatarUrl) {
                    userInfoUpdated.avatarUrl = userInfoInSession.avatarUrl;
                }
            }

            let finalize = function (obj) {
                if (obj.error) {
                    returnJSON(obj, res, sessionData);
                } else {
                    let objToBroadCast = {operation: apiName};
                    if (userInfoUpdated) {
                        objToBroadCast.updatedUserInfo = userInfoUpdated;
                    }

                    broadcastRTCMessage(archiveID, obj.archiveRevision, internalUserID, username, objToBroadCast);
                    returnJSON(obj, res, sessionData);
                }
            };

            // need WRITE LOCK
            if (apiName === con.API_CREATEMYUSER) {
                bar.createMyUser(userInfoUpdated, finalize);
            } else if (apiName === con.API_UPDATEMYUSER) {
                bar.updateMyUser(userInfoUpdated, finalize);
            } else {
                returnJSON({error: "Unknown API: " + apiName}, res, sessionData);
            }
        });
    }

    app.post('/'+con.API_CREATEMYUSER, function (req, res) {
        createOrUpdateUserForComment(req, res, con.API_CREATEMYUSER);
    });

    app.post('/'+con.API_UPDATEMYUSER, function (req, res) {
        createOrUpdateUserForComment(req, res, con.API_UPDATEMYUSER);
    });

    app.get('/'+con.API_GETARCHIVEUSERSLIST, function (req, res) {
        var token = req.query.token;
        req.bas.addLoggerInfo({action: "getArchiveUsersList", sessionToken: token});
        req.bas.logger.info("getArchiveUsersList");

        checkValidUser(con.API_GETARCHIVEUSERSLIST, req, res, function (sessionData) {
            var archiveID = sessionData.user.ARCHIVE_ID;

            sessionManager.openBarLocked(sessionData, archiveID, "READ", function (obj) {
                if (obj.error) {
                    returnJSON(obj, res, sessionData);
                }
                else {
                    var bar = obj.bar;
                    bar.getArchiveUsersList(function (obj) {
                        returnJSON(obj, res, sessionData);
                    });
                }
            });
        });
    });

    app.post('/'+con.API_SETCOMMENTDATA, function (req, res) {
        var jsonObj = req.body;
        var commentID = jsonObj.commentID;
        var data = jsonObj.data;
        let logger = req.bas.logger.getLogger({});
        //var token = req.query.token;

        checkUserAndOpenBarForWriting(logger, con.API_SETCOMMENTDATA, req, res, jsonObj.archiveRevision, function (sessionData, obj) {
            var archiveID = sessionData.user.ARCHIVE_ID;
            var internalUserID = sessionData.user.INTERNAL_ID;
            var username = sessionData.user.USERNAME;
            var bar = obj.bar;

            // need WRITE LOCK
            bar.setCommentData(commentID, data, function (obj) {
                if (obj.error) {
                    returnJSON(obj, res, sessionData);
                } else {
                    broadcastRTCMessage(archiveID, obj.archiveRevision, internalUserID, username, {operation: con.API_SETCOMMENTDATA, commentID: commentID, attributes: obj.attributes, data: obj.data });
                    returnJSON(obj, res, sessionData);
                }
            });
        });
    });

    app.post('/'+con.API_DELETECOMMENTS, function (req, res) {
        var jsonObj = req.body;
        var commentIDs;
        let logger = req.bas.logger.getLogger({})
        // var token = req.query.token;

        try {
            commentIDs = JSON.parse(jsonObj.commentIDs);
        } catch (e) {
            returnJSON({error: e.message}, res);
            return;
        }

        checkUserAndOpenBarForWriting(logger, con.API_DELETECOMMENTS, req, res, jsonObj.archiveRevision, function (sessionData, obj) {
            var archiveID = sessionData.user.ARCHIVE_ID;
            var internalUserID = sessionData.user.INTERNAL_ID;
            var username = sessionData.user.USERNAME;
            var bar = obj.bar;

            // need WRITE LOCK
            bar.deleteComments(commentIDs, function (obj) {
                if (obj.error) {
                    returnJSON(obj, res, sessionData);
                } else {
                    broadcastRTCMessage(archiveID, obj.archiveRevision, internalUserID, username, {operation: con.API_DELETECOMMENTS, commentIDs: commentIDs});
                    returnJSON(obj, res, sessionData);
                }
            });
        });
    });

    app.post('/'+con.API_UPDATECOMMENTATTRIBUTES, function (req, res) {
        var jsonObj = req.body;
        var commentID = req.body.commentID;
        var token = req.query.token;
        let logger = req.bas.logger.getLogger({action: "updateCommentAttributes", sessionToken: token});
        logger.info("updateCommentAttributes");

        var attributes;

        try {
            attributes = jsonObj.attributes ? JSON.parse(jsonObj.attributes) : undefined;
        } catch (e) {
            returnJSON({error: e.message}, res);
            return;
        }

        checkUserAndOpenBarForWriting(logger, con.API_UPDATECOMMENTATTRIBUTES, req, res, jsonObj.archiveRevision, function (sessionData, obj) {
            var archiveID = sessionData.user.ARCHIVE_ID;
            var internalUserID = sessionData.user.INTERNAL_ID;
            var username = sessionData.user.USERNAME;
            var bar = obj.bar;

            // need WRITE LOCK
            bar.updateCommentAttributes(commentID, attributes, function (obj) {
                if (obj.error) {
                    returnJSON(obj, res, sessionData);
                } else {
                    broadcastRTCMessage(archiveID, obj.archiveRevision, internalUserID, username, {operation: con.API_UPDATECOMMENTATTRIBUTES, commentID: commentID, attributes: obj.attributes});
                    returnJSON(obj, res, sessionData);
                }
            });
        });
    });

    app.post('/'+con.API_UPDATECOMMENTSATTRIBUTES, function (req, res) {
        var jsonObj = req.body;
        var token = req.query.token;
        let logger = req.bas.logger.getLogger({action: "updateCommentsAttributes", sessionToken: token});
        logger.info("updateCommentsAttributes");

        var attributes;
        var commentIDs;

        try {
            attributes = jsonObj.attributes ? JSON.parse(jsonObj.attributes) : undefined;
            commentIDs = jsonObj.commentIDs ? JSON.parse(jsonObj.commentIDs) : undefined;
        } catch (e) {
            returnJSON({error: e.message}, res);
            return;
        }

        checkUserAndOpenBarForWriting(logger, con.API_UPDATECOMMENTSATTRIBUTES, req, res, jsonObj.archiveRevision, function (sessionData, obj) {
            var archiveID = sessionData.user.ARCHIVE_ID;
            var internalUserID = sessionData.user.INTERNAL_ID;
            var username = sessionData.user.USERNAME;
            var bar = obj.bar;

            // need WRITE LOCK
            bar.updateCommentsAttributes(commentIDs, attributes, function (obj) {
                if (obj.error) {
                    returnJSON(obj, res, sessionData);
                } else {
                    for (var i = 0; i < obj.attributes.length; i++) {
                        var comment = obj.attributes[i];
                        if (comment.attributesChangedData) {
                            broadcastRTCMessage(archiveID, obj.archiveRevision, internalUserID, username, {operation: con.API_SETCOMMENTDATA, commentID: comment.commentID, attributes: comment.attributes, data: comment.attributesChangedData });
                        }
                    }

                    broadcastRTCMessage(archiveID, obj.archiveRevision, internalUserID, username, {operation: con.API_UPDATECOMMENTSATTRIBUTES, attributes: obj.attributes});
                    returnJSON(obj, res, sessionData);
                }
            });
        });
    });

    app.post('/'+con.API_CREATETHUMBNAIL, function (req, res) {
        var jsonObj = req.body; //{thumbnailID, attributes}
        var thumbnailID = jsonObj.thumbnailID;
        var attributes; //optional
        var token = req.query.token;
        let logger = req.bas.logger.getLogger({action: "createThumbnail", sessionToken: token});
        logger.info("createThumbnail with thumbnailID " + thumbnailID);

        try {
            attributes = jsonObj.attributes ? JSON.parse(jsonObj.attributes) : undefined;
        } catch (e) {
            returnJSON({error: e.message}, res);
            return;
        }

        checkUserAndOpenBarForWriting(logger, con.API_CREATETHUMBNAIL, req, res, jsonObj.archiveRevision, function (sessionData, obj) {
            var archiveID = sessionData.user.ARCHIVE_ID;
            var internalUserID = sessionData.user.INTERNAL_ID;
            var username = sessionData.user.USERNAME;
            var bar = obj.bar;

            // need WRITE LOCK
            bar.createThumbnail(thumbnailID, attributes, function (obj) {
                if (obj.error) {
                    returnJSON(obj, res, sessionData);
                } else {
                    broadcastRTCMessage(archiveID, obj.archiveRevision, internalUserID, username, {operation: 'createThumbnail', thumbnailID: obj.ID});
                    returnJSON(obj, res, sessionData);
                }
            });
        });
    });

    app.get('/'+con.API_GETTHUMBNAIL, function (req, res) {
        var thumbnailID = req.query.thumbnailID;
        var token = req.query.token;
        let logger = req.bas.logger.getLogger({action: "getThumbnail", sessionToken: token});
        logger.info("getThumbnail with thumbnailID " + thumbnailID);

        checkValidUser(con.API_GETTHUMBNAIL, req, res, function (sessionData) {
            var archiveID = sessionData.user.ARCHIVE_ID;

            sessionManager.openBarLocked(sessionData, archiveID, "READ", function (obj) {
                if (obj.error) {
                    returnJSON(obj, res, sessionData);
                }
                else {
                    var bar = obj.bar;
                    // need READ LOCK
                    bar.getThumbnail(thumbnailID, function (obj) {
                        returnJSON(obj, res, sessionData);
                    });
                }
            });
        });
    });
    
    app.post('/'+con.API_TOUCH_RESOURCE, function (req, res) {
        var jsonObj = req.body; //{attributes}
        var archiveRevision = jsonObj.archiveRevision;
        var attributes;
        const token = req.query.token;
        let logger = req.bas.logger.getLogger({action: "touchResource", sessionToken: token})

        try {
            attributes = JSON.parse(jsonObj.attributes);
        } catch(e) {
            returnJSON({error: e.message}, res);
            return;
        }

        //checkValidUser only calls back if it worked, no need to check obj.error
        checkValidUser(con.API_TOUCH_RESOURCE, req, res, function(sessionData) {
            var archiveID = sessionData.user.ARCHIVE_ID;
            var internalUserID = sessionData.user.INTERNAL_ID;
            var username = sessionData.user.USERNAME;

            logger.info("touchResource with " + attributes.branchID + " " + attributes.resourceID);
            callSaneFunctionFromLegacy(markPermalinkAsOutdated({
                dbConnector: sessionData.dbConnector,
                basArchiveID: archiveID,
                archiveRevision: archiveRevision,
                branchID: attributes.branchID,
                resourceID: attributes.resourceID,
                internalUserID,
                username,
                broadcastRTCMessage,
            }), function (obj) {
                returnJSON(obj, res, sessionData);
            });
        });
    });


    app.post('/'+con.API_SETTHUMBNAIL, function (req, res) {
        var jsonObj = req.body; //{thumbnailID, attributes}
        var thumbnailID = jsonObj.thumbnailID;
        var archiveRevision = jsonObj.archiveRevision;
        var attributes;
        const token = req.query.token;
        let logger = req.bas.logger.getLogger({action: "setThumbnail", sessionToken: token})
        // logger.info("setThumbnail with thumbnailID " + thumbnailID);

        try {
            attributes = JSON.parse(jsonObj.attributes);
        } catch(e) {
            returnJSON({error: e.message}, res);
            return;
        }

        //checkValidUser only calls back if it worked, no need to check obj.error
        checkValidUser(con.API_SETTHUMBNAIL, req, res, function(sessionData) {
            var archiveID = sessionData.user.ARCHIVE_ID;
            var internalUserID = sessionData.user.INTERNAL_ID;
            var username = sessionData.user.USERNAME;

            callSaneFunctionFromLegacy(markPermalinkAsOutdated({
                dbConnector: sessionData.dbConnector,
                basArchiveID: archiveID,
                archiveRevision: archiveRevision,
                branchID: attributes.branchID,
                resourceID: attributes.resourceID,
                internalUserID,
                username,
                broadcastRTCMessage,
            }), function (obj) {
                if (obj.error) {
                    returnJSON(obj, res, sessionData);
                } else {
                    openBarLockedAndSetBar(logger, sessionData, res, jsonObj.clientArchiveRevision, function (sessionData, obj) {
                        var bar = obj.bar;
                        // need WRITE LOCK
                        bar.setThumbnail(thumbnailID, attributes, function (obj) {
                            if (obj.error) {
                                returnJSON(obj, res, sessionData);
                            } else {
                                broadcastRTCMessage(archiveID, obj.archiveRevision, internalUserID, username, {operation: 'setThumbnail', thumbnailID: thumbnailID});
                                returnJSON(obj, res, sessionData);
                            }
                        });
                    });
                }
            });
        });
    });

    app.post('/'+con.API_DELETETHUMBNAILS, function (req, res) {
        var jsonObj = req.body;
        var thumbnailIDs;
        var token = req.query.token;
        let logger = req.bas.logger.getLogger({action: "deleteThumbnails", sessionToken: token});
        logger.info("deleteThumbnails");

        try {
            thumbnailIDs = JSON.parse(jsonObj.thumbnailIDs);
        } catch(e) {
            returnJSON({error: e.message}, res);
            return;
        }

        checkUserAndOpenBarForWriting(logger, con.API_DELETETHUMBNAILS, req, res, jsonObj.archiveRevision, function (sessionData, obj) {
            var archiveID = sessionData.user.ARCHIVE_ID;
            var internalUserID = sessionData.user.INTERNAL_ID;
            var username = sessionData.user.USERNAME;
            var bar = obj.bar;

            // need WRITE LOCK
            bar.deleteThumbnails(thumbnailIDs, function (obj) {
                if (obj.error) {
                    returnJSON(obj, res, sessionData);
                } else {
                    broadcastRTCMessage(archiveID, obj.archiveRevision, internalUserID, username, {operation: 'deleteThumbnails', thumbnailIDs: thumbnailIDs});
                    returnJSON(obj, res, sessionData);
                }
            });
        });
    });


    app.post('/'+con.API_DELETEBRANCHES, function (req, res) {
        var jsonObj = req.body;
        var branchIDs;
        var token = req.query.token;
        let logger = req.bas.logger.getLogger({action: "deleteBranches", sessionToken: token});
        logger.info("deleteBranches");

        try {
            branchIDs = JSON.parse(jsonObj.branchIDs);
        } catch(e) {
            returnJSON({error: e.message}, res);
            return;
        }

        checkUserAndOpenBarForWriting(logger, con.API_DELETEBRANCHES, req, res, jsonObj.archiveRevision, function (sessionData, obj) {
            var archiveID = sessionData.user.ARCHIVE_ID;
            var internalUserID = sessionData.user.INTERNAL_ID;
            var username = sessionData.user.USERNAME;
            var bar = obj.bar;

            // need WRITE LOCK
            bar.deleteBranches(branchIDs, function (obj) {
                if (obj.error) {
                    returnJSON(obj, res, sessionData);
                } else {
                    broadcastRTCMessage(archiveID, obj.archiveRevision, internalUserID, username, {operation: 'deleteBranches', branchIDs: branchIDs});
                    returnJSON(obj, res, sessionData);
                }
            });
        });
    });

    app.post('/'+con.API_SETPLATFORMARCHIVENAME, function (req, res) {
        var jsonObj = req.body;
        var token = req.query.token;
        var platformArchiveName = jsonObj.platformArchiveName;
        let logger = req.bas.logger.getLogger({action: "setPlatformArchiveName", sessionToken: token});
        logger.info("setPlatformArchiveName  " + platformArchiveName);

        checkValidUser(con.API_SETPLATFORMARCHIVENAME, req, res, function (sessionData)
        {
            var dbConnector = sessionData.dbConnector;
            var archiveID = sessionData.user.ARCHIVE_ID;
            var authToken = sessionData.user.PLATFORM_TOKEN;  // token is different for each user

            dbConnector.getPlatformData(archiveID, function (obj) {
                if (obj.error) {
                    returnJSON(obj, res, sessionData);
                }
                else if (obj.PLATFORM_KIND) {
                    var kind = obj.PLATFORM_KIND;
                    var platformArchiveID = obj.PLATFORM_ARCHIVE_ID;
                    if (getConnector(kind)?.setPlatformArchiveName) {
                        getConnector(kind).setPlatformArchiveName(platformArchiveID, authToken, platformArchiveName, function(obj) {
                            if (obj.error) {
                                returnJSON(obj, res, sessionData);
                            } else {
                                if (obj.skipPlatformInfoUpdate) {
                                    // TODO: caller shall save the actual platformArchiveName returned
                                    returnJSON({platformArchiveName: platformArchiveName}, res, sessionData);
                                } else {
                                    dbConnector.updateArchivePlatformName(archiveID, platformArchiveName, function(obj) {
                                        if (obj.error) {
                                            returnJSON(obj, res, sessionData);
                                        } else {
                                            returnJSON({platformArchiveName: platformArchiveName}, res, sessionData);
                                        }
                                    });
                                }
                            }
                        });
                    } else {
                        returnJSON({error: "Not supported API for this connector " + kind}, sessionData);
                    }
                }
                else {
                    //might be already closed by someone else
                    returnJSON({error: "Unexpected error: archive already closed"}, res, sessionData);
                }
            });
        });
    });

    app.post(
        '/'+con.API_REFRESHSESSION,
        createApiAuthPipeline(con.API_REFRESHSESSION),
        (req, res) => {
            return req.bas.returnJSON(
                req._updatedTokenResult,   // the DB-update result
                res,
                req.sessionData,
            );
        },
    );

    // Hack to determine whether cookies are forwarded
    // ------------------------------------------------
    //
    // Problem: Safari private mode, as well as a number of third party browser extensions that enforce "privacy", prevent cookies from being forwarded
    // to an external (CORS) domain. This hack allows to determine whether we are in such a situation.
    //
    // The hack works by sending 2 subsequent requests to the BAS server: the first to a specific entrypoint which sets a testing cookie in the response.
    // The second request is sent to check (on the server side) the presence of the cookie, and returns a different JSON response
    // basing on whether the testing cookie is found.
    //
    // PS. The hack uses XMLHttpRequest because the fetch API exhibits a different behaviour. Websockets, apparently, share the same implementation as XHR.
    //
    // -----------------------------------------------
    app.get('/test-cookie-simple', function (req, res) {
        let origin = req.headers['origin'];
        let cookieFound = false;
        if (req.headers['cookie']) {
            for (let cookie of req.headers['cookie'].split(';')) {
                req.bas.logger.error("Testing cookie: found");
                if (cookie.trim() === 'balsamiq-cookie-test=ok') {
                    cookieFound = true;
                }
                if (cookie.trim() === 'balsamiq-cookie-test-cors=ok') {
                    cookieFound = true;
                }
            }
        } else {
            req.bas.logger.error("Testing cookie: not found");
        }
        res.writeHead(200, {
            'Content-type':'application/json',
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Access-Control-Allow-Origin': origin || '*',
            'Access-Control-Allow-Credentials': 'true',
            'Set-Cookie': [
                'balsamiq-cookie-test=ok; Path=/; Max-Age=60; HttpOnly',
                'balsamiq-cookie-test-cors=ok; Path=/; Max-Age=60; HttpOnly; SameSite=None; Secure',
            ],
        });
        const result = {
            cookieFound : cookieFound
        }
        res.write(JSON.stringify(result));
        res.end();
    });

    app.get('/test-cookie', function (req, res) {
        const action = req.query.action;
        let origin = req.headers['origin'];
        let ret;
        if (action === "set") {
            req.bas.logger.info("Testing cookie: set");
            res.writeHead(200, {
                'Content-type': 'application/json',
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Access-Control-Allow-Origin': origin || '*',
                'Access-Control-Allow-Credentials': 'true',
                'Set-Cookie': [
                    'balsamiq-cookie-test=ok; Path=/; Max-Age=3600; HttpOnly',
                    'balsamiq-cookie-test-cors=ok; Path=/; Max-Age=3600; HttpOnly; SameSite=None; Secure',
                ],
            });
            ret = { ok: true };
        } else {
            // get
            let cookieFound = false;
            req.bas.logger.info("Testing cookie: get");
            if (req.headers['cookie']) {
                for (let cookie of req.headers['cookie'].split(';')) {
                    if (cookie.trim() === 'balsamiq-cookie-test=ok') {
                        cookieFound = true;
                    }
                    if (cookie.trim() === 'balsamiq-cookie-test-cors=ok') {
                        cookieFound = true;
                    }
                }
            }
            res.writeHead(200, {
                'Content-type':'application/json',
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Access-Control-Allow-Origin': origin || '*',
                'Access-Control-Allow-Credentials': 'true',
                'Set-Cookie': [
                    'balsamiq-cookie-test=deleted; Path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT',
                    'balsamiq-cookie-test-cors=deleted; Path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT; SameSite=None; Secure',
                ],
            });
            ret = {
                cookieFound : cookieFound
            }
        }
        res.write(JSON.stringify(ret));
        res.end();
    });

    // (ONLY FOR DEBUG) dummy implementation of the cookie dance API
    app.get('/get_config/cookie', function (req, res) {
        returnJSON({ok: "true"}, res, null, true);
    });

    app.get('/metrics', function (req, res) {
        returnJSON({status: "ok"}, res);
    });

    // for ALL other not supported path we return a 404
    app.all('*', function(req, res) {
        req.bas.addLoggerInfo({action: 'unknown'});
        req.bas.logger.warn("Unexpected API: " + req.path + " is not allowed");
        res.status(404).send("Cannot " + req.method + " requested URL");
    });

    app.use(globalErrorHandler);

    // END OF APIs

    function importantLog(action) {
        var ret = true;
        if (config.reduceLogging && (
            action && (
            action.endsWith(con.API_REFRESHSESSION) ||
            action.endsWith(con.API_GETUSERSLIST) ||
            action.endsWith(con.API_GETARCHIVEUSERSLIST) ||
            action.endsWith(con.API_SETTHUMBNAIL) ||
            action.endsWith(con.API_TOUCH_RESOURCE) ||
            action.endsWith(con.API_GETRESOURCEDATA) ||
            action.endsWith(con.API_GETRESOURCESDATA) ||
            // action.endsWith(con.API_SETRESOURCEADATA) ||
            action.endsWith(con.API_GETCOMMENTSDATA)
            ))) {
            ret = false;
        }

        return ret;
    }

    function returnJSON(data, res, sessionData, disableLog) {
        const req = res.req;
        var archiveID;
        var maxLogLength = 255;
        var dbConnector;
        req.bas.addLoggerInfo({return: true});
        let action = null;

        if (!disableLog && sessionData) {
            action = sessionData.action;
            req.bas.addLoggerInfo({action});
            if (sessionData.token) {
                req.bas.addLoggerInfo({sessionToken: sessionData.token});
            } else if (sessionData.action.startsWith("unloadArchiveOffline") || sessionData.action.startsWith("flushOffline") || sessionData.action.startsWith("unloadArchiveNotExistingOnPlatform") || sessionData.action.startsWith("deletePermalinksDataAndImageOffline") || sessionData.action.startsWith("deleteArchiveOffline") || sessionData.action.startsWith("open") || sessionData.action.startsWith("getPermalink") || sessionData.action.startsWith("create") || sessionData.action.startsWith("unloadArchiveIfNotSync")) {
                // ignore, for this calls we do not expect a session token
            } else {
                req.bas.addLoggerInfo({sessionToken: req.query && req.query.token ? req.query.token : 'unexpected_empty_token'});
                req.bas.logger.error("token expired or null: " + sessionData.action);

            }

            if (sessionData.user && sessionData.user.ARCHIVE_ID) {
                req.bas.addLoggerInfo({archiveID: sessionData.user.ARCHIVE_ID});
            }
        }

        var finaliseReturnJSON = function (data, res, sessionData) {
            var finalise = function (data, res) {
                var msg, str, timer;

                if (metrics && action && action.endsWith(con.API_GETRESOURCESDATA)) {
                    timer = metrics.trackTimeInterval('getResourcesData JSON-stringify');
                }

                str = JSON.stringify(data);

                timer && timer.stop();

                msg = "return " + action + " " + str;

                if (action === undefined && str === '{\"users\":[]}') {
                    msg = "return " + action + " " + str;
                }

                if (data.error) {
                    if (data.busy) {
                        req.bas.addLoggerInfo({errorType: "rte"});
                        if (data.error === "Session closed" || data.error === "Connection already released") {
                            data.ignore = true;
                        }
                        !disableLog && !data.ignore && req.bas.logger.warn(msg);
                    } else {
                        !disableLog && !data.ignore && req.bas.logger.error(msg);
                    }
                } else {
                    if (importantLog(action)) {
                        if (msg.length > maxLogLength) {
                            !disableLog && !data.ignore && req.bas.logger.info(msg.substring(0, maxLogLength - 1));
                        } else {
                            !disableLog && !data.ignore && req.bas.logger.info(msg);
                        }
                    }
                }

                if (metrics && action && action.endsWith(con.API_GETRESOURCESDATA)) {
                    timer = metrics.trackTimeInterval('getResourcesData Response-Write');
                }

                try {
                    res.setHeader('Content-Type', 'application/json; charset=utf-8');
                    res.setHeader('Content-Length', Buffer.byteLength(str));
                    res.setHeader('Cache-Control', 'no-cache, no-store');
                    res.write(str);
                    res.end();
                } catch (e) {
                    req.bas.logger.error("Unexpected error returnJSON: " + e.message + " " + str, e);
                }

                sessionData && sessionData.end2endTimer && sessionData.end2endTimer.stop();
                timer && timer.stop();
            };

            function releaseSession() {
                if (sessionData)
                {
                    sessionManager.releaseSession(sessionData, function(obj) {
                        if (obj.error && !data.error) {
                            finalise(obj, res);
                        } else {
                            finalise(data, res);
                        }
                    });
                } else {
                    finalise(data, res);
                }
            }
            releaseSession();
        };

        if (sessionData) {
            dbConnector = sessionData.dbConnector;
        } else
        {
            // in case of /health
            finaliseReturnJSON(data, res);
            return;
        }

        if (data.error) {
            if (data.busy) {
                // try to heal the not consistent db
                if (data.zombie_db && data.zombie_db[0]) {
                    archiveID = data.zombie_db[0];
                    req.bas.logger.warn("heal db: delete zombie user for archive " + archiveID);

                    dbConnector.deleteSessionsByArchiveID(archiveID, function (obj) {
                        if (obj.error) {
                            req.bas.logger.error("Unexpected error while healing db for archive " + archiveID + ", " + JSON.stringify(obj));
                        }
                        finaliseReturnJSON(data, res, sessionData);
                    });
                } else if (data.zombie_bar && data.zombie_bar[0]) {
                    archiveID = data.zombie_bar[0];
                    req.bas.logger.info("heal db: delete zombie bar for archive " + archiveID);
                    sessionManager.openBarLocked(sessionData, archiveID, "WRITE", function (obj) {
                        var tmpBar;
                        if (obj.error) {
                            req.bas.logger.error("heal db: unable to get the zombie for archive " + archiveID + ", " + JSON.stringify(obj));
                            finaliseReturnJSON(data, res, sessionData);
                        }
                        else {
                            // need WRITE LOCK
                            tmpBar = obj.bar;
                            tmpBar.destroy(archiveID, function (obj) {
                                if (obj.error) {
                                    req.bas.logger.warn("heal db: unable to destroy the zombie for archive " + archiveID + ", " + JSON.stringify(obj));
                                }
                                finaliseReturnJSON(data, res, sessionData);
                            });
                        }
                    });
                } else {
                    finaliseReturnJSON(data, res, sessionData);
                }
            } else {
                finaliseReturnJSON(data, res, sessionData);
            }
        } else {
            finaliseReturnJSON(data, res, sessionData);
        }
    }

    function checkValidUser(api, req, res, callback) {
        // Create session, retrieve the user, set the user prop in the session, returns the session
        sessionManager.createSession(req.bas.logger, req.route.path, function (obj) {
            if (obj.error) {
                returnJSON(obj, res, null);
            } else {
                const sessionData = obj;
                const token = req.query.token;
                callSaneFunctionFromLegacy(sane_getValidUserForRole({
                    token,
                    dbConnector: sessionData.dbConnector,
                    metrics,
                    logger: req.bas.logger,
                    role: roleForAccessTable[api],
                    sessionData,
                }), (obj) => {
                    if (obj.error) {
                        const userAgent = req.headers['user-agent'];
                        const resp = {
                            error: obj.error,
                            busy: obj.busy,
                            ignore: obj.ignore,
                            userAgent: userAgent,
                        };

                        if (req.body.platformArchiveID) {
                            resp.error = resp.error + " [" + req.body.platformArchiveID + "]";
                        }

                        returnJSON(resp, res, sessionData);
                    } else {
                        sessionData.user = obj;
                        callback(sessionData);
                    }
                });
            }
        });
    }
    function checkUserAndOpenBarForWriting(logger, api, req, res, clientArchiveRevision, callback)
    {
        checkValidUser(api, req, res, function(sessionData)
        {
            //checkValidUser only calls back if it worked, no need to check obj.error
            openBarLockedAndSetBar(logger, sessionData, res, clientArchiveRevision, callback);
        });
    }

    function openBarLockedAndSetBar(logger, sessionData, res, clientArchiveRevision, callback) {
        logger = logger.getLogger({action: sessionData.action, sessionToken: sessionData.token});
        sessionManager.openBarLocked(sessionData, sessionData.user.ARCHIVE_ID, "WRITE", function (obj) {
            if (obj.error) {
                returnJSON(obj, res, sessionData);
            }
            else
            {
                // TODO: what we do if clientArchiveRevision overflow?
                if (clientArchiveRevision && clientArchiveRevision > bmprUtilsMod.maxSafeRevision) {
                    logger.error("Current archive revision is reaching the maximum allowed value: " + clientArchiveRevision);
                }

                sessionData.user.bar = obj.bar;
                callback && callback(sessionData, obj);
            }
        });
    }

    function saveUserAndReturnInitialData(sessionData, platformToken, userInfo, archiveID, kind, role, dump, heuristicArchiveSize, res) {
        const req = res.req;
        var internalUserID = getUUID();
        var userToken = getUUID();
        var dbConnector = sessionData.dbConnector;
        let logger = req.bas.logger.getLogger({action: "open", username: userInfo.name, sessionToken: userToken});
        logger.info("saving the user for archive " + archiveID);
        dbConnector.saveUser(userToken, userInfo, internalUserID, archiveID, (new Date()).getTime(), role, platformToken, function (obj) {
            if (obj.error) {
                returnJSON(obj, res, sessionData);
            } else {
                if (!getConnector(kind)) {
                    returnJSON({error: "Not supported platform kind " + kind}, res, sessionData);
                    return;
                }
                // tell the connector someone connected for this archive and let it do whatever
                getConnector(kind).userConnectedToArchive(logger, dbConnector, userToken, function (obj) {
                    if (obj.error) {
                        returnJSON(obj, res, sessionData);
                    } else {
                        var retObj = {
                            token: userToken,
                            userID: internalUserID,
                            rtcChannel: archiveID,
                            timestamp: clock.now(),
                            heuristicArchiveSize: heuristicArchiveSize,
                            dump: dump,
                            basArchiveID: archiveID,
                        };

                        returnJSON(retObj, res, sessionData);
                    }
                });
            }
        });
    }

    return app;
}
