import { KMS, type EncryptCommandOutput } from '@aws-sdk/client-kms';

// TODO: import the following types from saas-utils when AwsKmsAdapter is exported from there
type DictOfStrings = {
    [k: string]: string;
};

type EncryptParams = {
    Plaintext: Buffer;
    KeyId: string;
    EncryptionContext: DictOfStrings;
};

export class AwsKmsAdapter {
    kms: KMS;

    constructor({ region }: { region: string | (() => Promise<string>) }) {
        this.kms = new KMS({ region });
    }

    encrypt(params: EncryptParams): Promise<Buffer> {
        return new Promise((resolve, reject) => {
            this.kms.encrypt(params, (err: unknown, data?: EncryptCommandOutput) => {
                if (err) {
                    reject(err);
                } else {
                    if (!data?.CiphertextBlob) {
                        reject(new Error('No CiphertextBlob returned from KMS'));
                        return;
                    } else {
                        resolve(Buffer.from(data.CiphertextBlob!));
                    }
                }
            });
        });
    }
}
