<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta http-equiv="refresh" content="5">
    <style>
        :root {
            --text-color: black;
            --background-color: white;
        }

        @media (prefers-color-scheme: dark) {
            :root {
                --text-color: white;
                --background-color: black;
            }
        }

        body {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            background-color: var(--background-color);
            color: var(--text-color);
            font-family: Arial, sans-serif;
        }

        .message {
            text-align: center;
            font-size: xx-large;
        }

        .spinner {
            margin-top: 20px;
            width: 50px;
            height: 40px;
            text-align: center;
            font-size: 10px;
        }

        .spinner > div {
            background-color: var(--text-color);
            height: 100%;
            width: 6px;
            display: inline-block;
            animation: sk-stretchdelay 1.2s infinite ease-in-out;
        }

        .spinner .rect2 {
            animation-delay: -1.1s;
        }

        .spinner .rect3 {
            animation-delay: -1.0s;
        }

        .spinner .rect4 {
            animation-delay: -0.9s;
        }

        .spinner .rect5 {
            animation-delay: -0.8s;
        }

        @keyframes sk-stretchdelay {
            0%, 40%, 100% {
                transform: scaleY(0.4)
            }
            20% {
                transform: scaleY(1.0)
            }
        }
    </style>
</head>
<body>
<div class="message">Generating the image, please hold on few seconds...</div>
<div class="spinner">
    <div class="rect1"></div>
    <div class="rect2"></div>
    <div class="rect3"></div>
    <div class="rect4"></div>
    <div class="rect5"></div>
</div>
</body>
</html>