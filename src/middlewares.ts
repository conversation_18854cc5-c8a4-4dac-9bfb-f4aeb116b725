<<<<<<< HEAD
/** @prettier */
// Re-export from modular middleware structure
export * from './middlewares/index.ts';
=======
import { assertBASRequest, isBASRequest, type BASRequest } from './request-context.ts';
import type { NextFunction, Response, Request } from 'express';

/**
 * This function is used to wrap async request handlers.
 */
export function basRequestHandler(fn: (req: BASRequest, res: Response) => Promise<void>) {
    return (req: Request, res: Response, next: NextFunction) => {
        assertBASRequest(req);
        fn(req, res)
            .catch(next)
            .finally(() => {});
    };
}

/**
 * This function is used to wrap async middleware functions.
 */
export function basMiddleware(fn: (req: BASRequest, res: Response, next: NextFunction) => Promise<void>) {
    return (req: Request, res: Response, next: NextFunction) => {
        assertBASRequest(req);
        fn(req, res, next).catch(next);
    };
}

export function globalErrorHandler(err: unknown, req: Request, res: Response, next: NextFunction) {
    const availableLogger = isBASRequest(req) ? req.bas.logger : console;
    const error = err instanceof Error ? err : new Error(`Unexpected error ${err}`);
    availableLogger.error(error.message, error);

    if (res.headersSent) {
        availableLogger.error('Headers already sent, cannot send error response');
        return next(err); // Delegate to the default Express error handler (will close the connection and fail the request)
    }

    res.status(500).json({ error: 'Internal Server Error' });
}
>>>>>>> master
