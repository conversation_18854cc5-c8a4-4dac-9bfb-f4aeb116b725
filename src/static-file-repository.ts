import path from 'path';
import superagent from 'superagent';
import type { HttpResponse } from './utils.ts';

export class StaticFileRepository {
    private proxyConfig: { prefix: string; host: string; path: string }[];

    constructor(
        proxyConfig: {
            prefix: string;
            host: string;
            path: string;
        }[]
    ) {
        this.proxyConfig = proxyConfig;
    }

    // This function replace the old getStaticFileRepository function that always fetched static files from /bw-atlassian/
    legacyGetStaticFileRepository() {
        const repo = this.proxyConfig.find((cfg) => cfg.prefix === '/bw-atlassian/');
        return repo ? repo.host + repo.path : null;
    }

    async fetch(url: string): Promise<HttpResponse> {
        return await superagent.get(url);
    }
}
