import { callWithLegacyCallback } from './calling-style.ts';
import con from './constants.ts';
import { deletePermalinksByMinTimestamp } from './permalinks.js';
import { object2sqliteBuffer, sqliteBuffer2object, parseMultipartFormData, createBA<PERSON><PERSON>romBuffer } from './utils.ts';

async function restore({ req, res, config, metrics, getConnector, sessionManager, broadcastRTCMessage }) {
    // Note: this API is meant to be called server-to-server. No session token is needed, only a platform token, like the OPEN.
    // Because of that, no meaningful user information is available.
    let logger = req.bas.logger.getLogger({action: 'restore'})

    // The request carries the buffer of the BMPR to be restored encoded multipart-form
    let { params, uploadedFileBuffer } = await new Promise((resolve) => parseMultipartFormData(metrics, logger, req, res, (params, uploadedFileBuffer) => resolve({params, uploadedFileBuffer})))

    let platformToken = params.platformToken;
    let platformSiteID = params.platformSiteID;
    let platformArchiveID = params.platformArchiveID;
    let platformArchiveName = params.platformArchiveName;
    let basSessionId = params.basSessionId;
    let bmprTimestamp = params.bmprTimestamp;
    let kind = params.kind;
    let userInfo;
    try {
        userInfo = params.userInfo ? JSON.parse(params.userInfo) : {};
    } catch (e) {
        userInfo = {};
    }
    let username = userInfo.userName || null;
    let platformInfo;
    try {
        platformInfo = params.platformInfo ? JSON.parse(params.platformInfo) : {};
    } catch (e) {
        platformInfo = {};
    }

    logger.updateParams({connectorKind: kind, platformSiteID: platformSiteID, platformArchiveID: platformArchiveID});
    logger.info("restore archive");

    let connector = getConnector(kind);
    if (!connector) {
        return { error: "Unknown connector" };
    }

    await sessionManager.withSession(logger, "restore", async (sessionData) => {
        // Check with the connector to see that the credentials (platform token) are valid
        const { role } = await callWithLegacyCallback(cb => connector.getRole(logger, sessionData.dbConnector, platformToken, platformSiteID, platformArchiveID, userInfo, cb));
        if (role === con.ROLE_NO_ACCESS) {
            logger.error("Project not found, or you don't have permission to view it " + platformArchiveID + " " + platformSiteID);
            return {error: "Project not found, or you don't have permission to view it"};
        }
        // No need to check for the role, we can assume Cloud has already made all the necessary checks

        // Parse the uploaded BMPR (thus also making sure it's not corrupt)
        const { dump: dumpOfUploadedBmpr } = await callWithLegacyCallback(cb => sqliteBuffer2object(config.archivesPath, uploadedFileBuffer, kind, cb));

        let oldArchiveID;

        // Read the PLATFORM_INFO row
        const { BAS_ARCHIVE_ID: existingArchiveID } = await callWithLegacyCallback(cb => sessionData.dbConnector.getBASArchiveID(platformSiteID, platformArchiveID, cb));

        if (existingArchiveID) {
            oldArchiveID = existingArchiveID;
            // It only makes sense to broadcast a warning message for projects present in BAS. If a project is not present in BAS
            // there can't be any editor currently open on it.
            // Broadcast an RTC notification, so that editors will go in read-only mode
            let objToBroadcast = {
                operation: 'aboutToRestoreArchive',
                platformArchiveID: platformArchiveID,
                basSessionId: basSessionId,
                userInfo: userInfo
            };
            broadcastRTCMessage(oldArchiveID, null, null, username, objToBroadcast, function (obj) {
                if (obj.error) {
                    logger.error("flushing, failed to broadcast RTC message for archive " + oldArchiveID + " " + obj.error);
                }
            });
        } else {
            // This may not be an error, but the case in which BAS is not holding the project and must acquire it from S3.
            const { id: loadedArchiveID, buffer, platformInfo: newPlatformInfo } = await callWithLegacyCallback(cb => connector.loadFromPlatform(logger, sessionData.dbConnector, platformToken, platformSiteID, platformArchiveID, null, platformInfo, cb));
            oldArchiveID = loadedArchiveID;
            // update the platformInfo if changed by the loadFromPlatform
            platformInfo = newPlatformInfo ? newPlatformInfo : platformInfo;
            logger.info("loaded the archive from platform " + oldArchiveID);
            await callWithLegacyCallback(cb => createBARFromBuffer(oldArchiveID, buffer, sessionData, platformInfo, kind, platformSiteID, platformArchiveID, platformArchiveName, sessionData.dbConnector, logger, config, sessionManager, cb));
        }

        // Read the current project
        const { bar: barOldProject } = await callWithLegacyCallback(cb => sessionManager.openBarLocked(sessionData, oldArchiveID, "WRITE", cb));
        const { dump } = await callWithLegacyCallback(cb => barOldProject.dump(null, cb));
        // Transfer important metadata from the current BMPR to the one being restored
        dumpOfUploadedBmpr.Info.ArchiveAttributes = dump.Info.ArchiveAttributes; // Retain the project name, along with other metadata
        dumpOfUploadedBmpr.Info.ArchiveRevision = dump.Info.ArchiveRevision + 1; // Increase the archive revision number, like any other edit operations
        // Save the project to the platform
        const { buffer } = await callWithLegacyCallback(cb => object2sqliteBuffer(config, dump, cb));
        await callWithLegacyCallback(cb => connector.save(logger, sessionData.dbConnector, sessionData.user, oldArchiveID, null, dump.Info.ArchiveRevision, buffer, dump, { fromClose: true, fromRestore: true }, cb));

        // Recreate the archive DB from the uploaded BMPR, with a different ID
        const newArchiveID = connector.generateArchiveID();
        const barNewProject = sessionManager.getBar(sessionData);
        await callWithLegacyCallback(cb => barNewProject.createFromDump(newArchiveID, dumpOfUploadedBmpr, cb));

        // Invalidate current sessions by deleting rows in the USERS table.
        // Archive-changing API connections will therefore fail and be forced to do OPEN again.
        // The RTC message that will be sent will induce editors to reload the project, but we still
        // need to invalidate the current sessions for consistency.
        await callWithLegacyCallback(cb => sessionData.dbConnector.deleteSessionsByArchiveID(oldArchiveID, cb));

        // Replace the old archive ID with the new one
        await sessionData.dbConnector.replaceArchiveID(oldArchiveID, newArchiveID);

        // Broadcast an RTC notification, so that editors will reload (doing OPEN first)
        let objToBroadcastArchiveRestored = {
            operation: 'archiveRestored',
            ArchiveRevision: dumpOfUploadedBmpr.Info.ArchiveRevision,
            platformArchiveID: platformArchiveID,
            basSessionId: basSessionId,
            userInfo: userInfo
        };
        // Use the channel of the old archive, because that's where old editors are still listening
        broadcastRTCMessage(oldArchiveID, dumpOfUploadedBmpr.Info.ArchiveRevision, null, null, objToBroadcastArchiveRestored, function (obj) {
            if (obj.error) {
                logger.error("flushing, failed to broadcast RTC message for archive " + oldArchiveID + " " + obj.error);
            }
        });

        await deletePermalinksByMinTimestamp({
            dbConnector: sessionData.dbConnector,
            getConnector,
            platformKind: kind,
            platformSiteID,
            platformArchiveID,
            timestamp: bmprTimestamp,
        });

        // Destroy the old archive DB (need to release the BAR lock before)
        await callWithLegacyCallback(cb => sessionManager.unlockConnection(sessionData, cb));
        await callWithLegacyCallback(cb => barOldProject.destroy(oldArchiveID, cb));
    });
    return { platformArchiveID };
}

export {
    restore,
};
