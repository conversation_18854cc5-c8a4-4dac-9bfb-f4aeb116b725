import jwtModule from 'atlassian-jwt';
import { type SessionData, SessionManager } from './session-manager.ts';
import type { Logger } from '@balsamiq/logging';
import type { Request, Response } from 'express';
import { BASCallbackError, type BASLegacyCallback, isBASLegacyErrorObject } from './calling-style.ts';
import * as uuid from 'uuid';
import xss from 'xss';
import path from 'path';
import type { RedisAdapter } from './redisAdapter.ts';
import { Readable } from 'stream';
import barMod from '@balsamiq/bmpr/lib/BalsamiqArchive.js';
import type { OutgoingHttpHeaders, OutgoingHttpHeader } from 'http';
import superagent from 'superagent';
import assert from 'assert';
import type { Config } from './configLoader.ts';
import { SQLiteAdapter } from './sqlite-adapter.ts';
import Consts from '@balsamiq/bmpr/lib/BalsamiqArchiveConstants.js';
import type { User } from './database.ts';
import type { PermalinkData, PermalinkInfo } from './model.ts';
import type { PlatformData } from './model.ts';
import type { DBConnector } from './database.ts';
import * as jwt from './connectors/lib/jwt.js';
import { callWithLegacyCallback } from './calling-style.ts';
import { assertBASRequest } from './request-context.ts';
import { checkBasicAuthOnRequest } from './basicAuth.ts';
import fs from 'fs';
import type { RtcAdapter } from './rtc-adapter.ts';
import type { Clock } from './clock.ts';
import { acquireApplicationLock, type ApplicationLockName, type MySQLDriverPool } from './mysql-driver.ts';
import busboy from 'busboy';
import type { Metrics } from './metrics.ts';
import type { Connector } from './connectors/connectors-registry.ts';

export function verifyAtlassianJWT(
    logger: Logger,
    req: Request,
    sessionManager: SessionManager,
    callback: BASLegacyCallback<{ success: 'ok' }>
) {
    if (!req.query.jwt) {
        callback({ error: 'missing JWT token' });
        return;
    }

    let jwtToken: string;
    let alg: any; // Unexported type from atlassian-jwt
    let claims: any; // Unexported type from atlassian-jwt
    try {
        jwtToken = `${req.query.jwt}`;
        alg = jwtModule.getAlgorithm(jwtToken);
        claims = jwtModule.decodeSymmetric(jwtToken, '', alg, true);
    } catch (e) {
        callback({ error: 'cannot validate the JWT token, error parsing the token: ' + req.query.jwt });
        return;
    }

    // const req2 = jwtModule.fromExpressRequest(req);
    // console.log(req2.pathname);
    const hash = jwtModule.createQueryStringHash(jwtModule.fromExpressRequest(req));

    if (hash !== claims.qsh) {
        callback({ error: 'invalid JWT token, QSH verification failed' });
        return;
    }

    let connectorKind;
    // TODO: find better way to derive connector type
    if (req.path.includes('/confluence/')) {
        connectorKind = 'confluence';
    } else if (req.path.includes('/jira/')) {
        connectorKind = 'jira';
    } else {
        callback({ error: 'cannot validate the JWT token, unexpected connector type: ' + req.path });
        return;
    }

    sessionManager.createSession(logger, 'validate JWT', function (obj) {
        if (isBASLegacyErrorObject(obj)) {
            callback({ error: 'cannot validate the JWT token, unable to establish a db connection' });
            return;
        }
        const sessionData = obj;
        const dbConnector = sessionData.dbConnector;

        dbConnector.getConnectorData(connectorKind, claims.iss, function (instanceObj) {
            sessionManager.releaseSession(sessionData, function (_) {
                if (isBASLegacyErrorObject(instanceObj) || 'error' in instanceObj) {
                    callback({ error: 'cannot validate the JWT token, unable to retrieve connector data' });
                    return;
                }

                if (!('sharedSecret' in instanceObj) || typeof instanceObj.sharedSecret !== 'string') {
                    callback({ error: 'cannot validate the JWT token, missing shared secret ' + claims.iss });
                    return;
                }

                try {
                    jwtModule.decodeSymmetric(jwtToken, instanceObj.sharedSecret, alg, false);
                    callback({ success: 'ok' });
                } catch (error) {
                    callback({ error: 'invalid JWT signature ' });
                }
            });
        });
    });
}

// TODO: move this function into confluence connector after it has been converted to typescript
export function returnRenderConfluenceImage(
    data: { width: string; alignment: string; src: string },
    res: Response,
    sessionData: SessionData
) {
    const sessionManager = sessionData.sessionManager;
    const maxWidth = 600;

    let width;
    if (data.width) {
        width = Number.parseInt(data.width);
    }

    let alignment = 'center';
    if (data.alignment == 'Left') {
        alignment = 'left';
    } else if (data.alignment == 'Right') {
        alignment = 'right';
    }

    const imageTag = `<img alt="Balsamiq Wireframes" style="max-width: ${maxWidth}px; ${width ? 'width:' + width + 'px' : ''}" src="${data.src}">`;
    const content = `
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <title>Balsamiq Wireframes</title>
            <meta charset="UTF-8">
        </head>
        <body>
            <div style="width: 100%; height: 100%; text-align:${alignment};">
            ${xss(imageTag)}
            </div>
        </body>
        </html>
    `;
    sessionManager.releaseSession(sessionData, function (_) {
        res.setHeader('Content-Type', 'text/html');
        res.send(Buffer.from(content, 'utf8'));
    });
}

export function sanitizeAndNormalizePath(inputPath: string) {
    // Normalize the path to remove any ../ or ./ sequences
    const normalizedPath = path.posix.normalize(inputPath);

    // Prevent path from navigating outside the root by removing leading slashes
    const safePath = normalizedPath.replace(/^(\.\.(\/|\\|$))+/, '');

    return safePath;
}

export function getSessionIdFromBearerToken(req: { headers: { authorization?: string } }): string {
    const authHeader = req.headers.authorization || '';
    if (!authHeader.startsWith('Bearer ')) {
        throw new Error('Missing credentials or wrong auth token');
    }
    return authHeader.split(' ', 2)[1];
}

export function getUUID() {
    return uuid.v1().replace(/-/g, '_');
}

export function parseRedisKey(tag: string, key: string) {
    if (key.startsWith(tag + '::')) {
        const keyPart = key.split('::');
        if (keyPart.length === 3) {
            return {
                type: tag,
                sessionToken: keyPart[1],
                channelId: keyPart[2],
            };
        }
    }
    return null;
}

export async function doAddKeyInRedis(
    keyMakerFn: (sessionToken: string, channelId: string) => string,
    redisAdapter: RedisAdapter,
    logger: Logger,
    sessionToken: string,
    expirationInSecs = 60 * 10
): Promise<{ success: 'ok' } | { missingKey: true }> {
    const channelId = uuid.v1();
    const key = keyMakerFn(sessionToken, channelId);
    await redisAdapter.sadd(key, sessionToken);
    const found = await redisAdapter.expire(key, expirationInSecs);
    logger.info(`Key ${key} added with expiration ${expirationInSecs} secs`);
    return found ? { success: 'ok' } : { missingKey: true };
}

export function base64ToStream(base64: string) {
    const img = Buffer.from(base64, 'base64');
    const readable = new Readable();
    readable.push(img);
    readable.push(null);
    return { stream: readable, length: img.byteLength };
}

export const getThumbnailFromDump = barMod.getThumbnailFromDump;

const CHARACTER_SET = '0123456789abcdefghijklmnopqrstuvwxyz'.split('');

export function encodeBase36(integer: number | string) {
    if (typeof integer === 'string') {
        integer = parseInt(integer, 10);
    }
    if (integer === 0) {
        return '0';
    }
    let s = '';
    while (integer > 0) {
        s = CHARACTER_SET[integer % 36] + s;
        integer = Math.floor(integer / 36);
    }
    return s;
}

export function getShortIDFromResourceOrBranchID(id: string) {
    return id.substring(0, 4);
}

export function pipeStreamToResponse(stream: Readable, res: Response, headers: OutgoingHttpHeaders | OutgoingHttpHeader[]) {
    return new Promise<{}>((resolve, reject) => {
        stream
            .on('readable', () => {
                // Send response headers only when data is ready to be read
                if (!res.headersSent) {
                    res.writeHead(200, headers);
                }
                stream.read();
            })
            .on('error', (err) => {
                // Handles errors on the read stream
                reject(err);
            })
            .pipe(res)
            .on('error', () => {
                // Handles errors on the write stream
                resolve({}); // do not reject on client or network error
            })
            .on('close', () => {
                resolve({});
            });
    });
}

export function pipeToNewRequest(
    incomingReq: Request,
    outgoingRes: Response,
    options: { url: string; headers?: object; timeout?: number; incomingResHandler?: (incomingRes: superagent.Response) => void },
    logger: Logger,
    callback?: BASLegacyCallback<{}>
) {
    logger = logger.getLogger({ module: 'pipeToNewRequest' });

    let callbackCalled = false;
    function invokeCallbackIfNeeded(result: { error: string } | {}) {
        if (callback && !callbackCalled) {
            callbackCalled = true;
            callback(result);
        }
    }

    // The following are headers that should not be forwarded to the target URL
    const excludedHeaders = ['host', 'referer', 'referrer', 'origin', 'connection', 'content-length', 'transfer-encoding', 'upgrade'];
    const incomingReqHeaders = Object.fromEntries(
        Object.entries(incomingReq.headers).filter(([key]) => {
            return (
                !excludedHeaders.includes(key.toLowerCase()) &&
                !key.toLowerCase().startsWith('proxy-') &&
                !key.toLowerCase().startsWith('sec-')
            );
        })
    );

    const outgoingReq = superagent(incomingReq.method, options.url).set({ ...incomingReqHeaders, ...options.headers });

    if (options.timeout !== undefined) {
        outgoingReq.timeout(options.timeout);
    }

    outgoingReq.on('error', (err: Error) => {
        logger.error('outgoing request error: ' + err.message + ' ' + options.url);
        invokeCallbackIfNeeded({ error: err.message });
        outgoingRes.end();
    });

    outgoingReq.on('response', (incomingRes: superagent.Response) => {
        if (options.incomingResHandler) {
            options.incomingResHandler(incomingRes);
        }
        outgoingRes.writeHead(incomingRes.statusCode, incomingRes.headers);
    });

    try {
        outgoingReq.pipe(outgoingRes);
    } catch (err) {
        assert(err instanceof Error, 'Error is not an instance of Error');
        logger.error('Pipe error: ' + err.message + ' ' + options.url);
        invokeCallbackIfNeeded({ error: err.message });
        outgoingRes.end();
        return;
    }

    let outgoingReqFinished = false;
    outgoingRes
        .on('finish', () => {
            logger.info('pipeToNewRequest: client received all data');
            outgoingReqFinished = true;
        })
        .on('close', () => {
            logger.info('pipeToNewRequest: client closed connection');
            if (outgoingReqFinished) {
                invokeCallbackIfNeeded({});
            } else {
                logger.error('pipeToNewRequest: unexpected error piping the request');
                invokeCallbackIfNeeded({ error: 'aborted' });
            }
        })
        .on('error', () => {
            logger.error('pipeToNewRequest: client closed connection');
            outgoingRes.end();
        });

    return outgoingReq;
}

const validWebUriRegexp = new RegExp(
    '^' +
        // protocol identifier
        '(?:(?:https?|ftp)://)' +
        // user:pass authentication
        '(?:\\S+(?::\\S*)?@)?' +
        '(?:' +
        // IP address exclusion
        // private & local networks
        '(?!(?:10|127)(?:\\.\\d{1,3}){3})' +
        '(?!(?:169\\.254|192\\.168)(?:\\.\\d{1,3}){2})' +
        '(?!172\\.(?:1[6-9]|2\\d|3[0-1])(?:\\.\\d{1,3}){2})' +
        // IP address dotted notation octets
        // excludes loopback network 0.0.0.0
        // excludes reserved space >= *********
        // excludes network & broacast addresses
        // (first & last IP address of each class)
        '(?:[1-9]\\d?|1\\d\\d|2[01]\\d|22[0-3])' +
        '(?:\\.(?:1?\\d{1,2}|2[0-4]\\d|25[0-5])){2}' +
        '(?:\\.(?:[1-9]\\d?|1\\d\\d|2[0-4]\\d|25[0-4]))' +
        '|' +
        // host name
        '(?:(?:[a-z\\u00a1-\\uffff0-9]-*)*[a-z\\u00a1-\\uffff0-9]+)' +
        // domain name
        '(?:\\.(?:[a-z\\u00a1-\\uffff0-9]-*)*[a-z\\u00a1-\\uffff0-9]+)*' +
        // TLD identifier
        '(?:\\.(?:[a-z\\u00a1-\\uffff]{2,}))' +
        // TLD may end with dot
        '\\.?' +
        ')' +
        // port number
        '(?::\\d{2,5})?' +
        // resource path
        '(?:[/?#]\\S*)?' +
        '$',
    'i'
);
export function isWebUri(url: string) {
    return validWebUriRegexp.test(url);
}

export function queryStringToJSON(queryString: string) {
    const pairs = queryString.slice(1).split('&');

    let result: Record<string, string> = {};
    pairs.forEach(function (pair: string) {
        const pairTuple = pair.split('=');
        result[pairTuple[0]] = decodeURIComponent(pair[1] || '');
    });

    return JSON.parse(JSON.stringify(result));
}

export function removePrefix(str: string, prefix: string) {
    let i = str.indexOf(prefix);
    if (i == 0) {
        return str.substring(prefix.length);
    } else {
        return str;
    }
}

export function isImage(mimeType: string) {
    return mimeType == 'image/jpeg' || mimeType == 'image/png' || mimeType == 'image/gif';
}

// returns an sqlite buffer
// TODO: remove the any type once types are available in BalsamiqArchive
export function object2sqliteBuffer(config: Config, obj: unknown, callback: BASLegacyCallback<{ buffer: Buffer }>) {
    let sqlDriverInstance = new SQLiteAdapter({
        archivesPath: config.archivesPath,
    });
    let bar = new barMod.BalsamiqArchive(sqlDriverInstance);
    let tempID = uuid.v1();
    bar.createFromDump(tempID, obj, function (obj: any) {
        if (obj.error) {
            callback(obj);
        } else {
            bar.getBuffer(tempID, function (obj: any) {
                if (obj.error) {
                    callback(obj);
                } else {
                    const buffer = obj.buffer;
                    bar.destroy(tempID, function (obj: any) {
                        if (obj.error) {
                            callback(obj);
                        } else {
                            callback({ buffer: buffer });
                        }
                    });
                }
            });
        }
    });
}

// TODO: remove the any type once types are available in BalsamiqArchive
export function sqliteBuffer2object(archivesPath: string, buffer: Buffer, kind: string, callback: BASLegacyCallback<{ dump: BmprDump }>) {
    var tempID = uuid.v1();
    var sqlDriverInstance = new SQLiteAdapter({
        archivesPath,
    });
    var bar = new barMod.BalsamiqArchive(sqlDriverInstance);
    bar.createFromBuffer(tempID, buffer, function (obj: any) {
        let migrated: any;
        let ret: any;
        if (obj.error) {
            callback(obj);
        } else {
            migrated = obj.migrated;
            bar.dump(Consts.Branch.AllBranches, function (obj: any) {
                if (obj.error) {
                    callback(obj);
                } else {
                    var dump = obj.dump;
                    var resourceIDs = [];
                    var i;

                    dump.migrated = migrated;

                    if (kind === 'cloud' || kind === 'wd') {
                        // strip the thumbnail image for Cloud and Webdemo
                        stripThumbnailImageFromDump(dump);
                    }

                    for (i = 0; i < dump.Resources.length; i++) {
                        resourceIDs.push(dump.Resources[i].ID);
                    }
                    bar.getResourcesData(resourceIDs, Consts.Branch.AllBranches, function (obj: any) {
                        if (obj.error) {
                            // try to delete the temp file in any case
                            ret = obj;
                            bar.destroy(tempID, function (obj: any) {
                                if (obj.error) {
                                    ret.error += ' additional error on destroying ' + obj.error;
                                }
                                callback(ret);
                            });
                        } else {
                            for (i = 0; i < dump.Resources.length; i++) {
                                dump.Resources[i].DATA = obj.data[dump.Resources[i].ID + '|' + dump.Resources[i].BRANCHID];
                            }

                            bar.destroy(tempID, function (obj: any) {
                                if (obj.error) {
                                    callback(obj);
                                } else {
                                    callback({ dump: dump });
                                }
                            });
                        }
                    });
                }
            });
        }
    });
}

// TODO: remove the any type once types are available in BalsamiqArchive
export function stripThumbnailImageFromDump(dump: any) {
    const thumbnails = dump.Thumbnails || [];
    for (let thumbnail of thumbnails) {
        let attributes = (thumbnail && thumbnail.ATTRIBUTES) || {};
        delete attributes.image;
    }
}

export async function decodeJWT({
    connectorId,
    dbConnector,
    platformToken,
    noVerify,
    ignoreExpiredToken,
    secretsCache,
}: {
    connectorId: string;
    dbConnector: DBConnector;
    platformToken: string;
    noVerify?: boolean;
    ignoreExpiredToken?: boolean;
    secretsCache?: Record<string, string>;
}) {
    let claims: { exp?: number; iss?: string } = {};
    claims = jwt.decode(platformToken, '', true);

    if (noVerify) {
        return claims;
    }

    if (!claims.exp) {
        throw new Error('Missing exp claim in JWT token');
    }

    if (!claims.iss) {
        throw new Error('Missing iss claim in JWT token');
    }

    let secondsValidityLeft = claims.exp - new Date().getTime() / 1000;
    if (secondsValidityLeft < 0) {
        if (!ignoreExpiredToken) {
            throw new Error('PlatformToken expired (' + -Math.round(secondsValidityLeft) + ' seconds ago)');
        }
    }

    const secretKey = `${connectorId}::${claims.iss}`;
    let sharedSecret;
    if (secretsCache && secretsCache[secretKey]) {
        sharedSecret = secretsCache[secretKey];
    } else {
        const connectorData = await callWithLegacyCallback<Object>((cb) => dbConnector.getConnectorData(connectorId, `${claims.iss}`, cb));
        if (!('sharedSecret' in connectorData) || !connectorData.sharedSecret) {
            throw new Error(`No "sharedSecret" key found in the ${connectorId} connectorData for ISS ${claims.iss}`);
        }
        if (typeof connectorData.sharedSecret !== 'string') {
            throw new Error(`"sharedSecret" key in the ${connectorId} connectorData for ISS ${claims.iss} is not a string`);
        }
        sharedSecret = connectorData.sharedSecret;
        if (secretsCache) {
            secretsCache[secretKey] = sharedSecret;
        }
    }

    return { claims: jwt.decode(platformToken, sharedSecret, false) };
}

export function checkServerAPICredentialsFactory(config: Config): (req: Request) => Promise<boolean> {
    const serverApiSecrets = config.getServerApiSecrets();
    return async (req) => {
        assertBASRequest(req);
        const secrets = await serverApiSecrets.getSecret(req.bas.logger);
        assert(
            secrets && typeof secrets === 'object' && 'getAllSecrets' in secrets && typeof secrets.getAllSecrets === 'function',
            'Invalid serverApiSecrets'
        );
        for (let { domain, secret } of secrets.getAllSecrets()) {
            if (checkBasicAuthOnRequest(req, domain, secret)) {
                return true;
            }
        }
        return false;
    };
}

export function verifyCallFromAtlassian(
    jwtToken: string,
    expectedAudience: string,
    expectedIssuer: string,
    callback: BASLegacyCallback<{}>
) {
    try {
        let kid = jwtModule.getKeyId(jwtToken);
        let alg = jwtModule.getAlgorithm(jwtToken);

        if (kid) {
            superagent.get(`https://connect-install-keys.atlassian.com/${kid}`, function (err, res) {
                if (err) {
                    callback({ error: 'https://connect-install-keys.atlassian.com unreachable: ' + err });
                } else {
                    if (res.statusCode === 200) {
                        const rsaPublicKey = res.text;
                        let jwtBody;
                        try {
                            jwtBody = jwtModule.decodeAsymmetric(jwtToken, rsaPublicKey, alg, false);
                        } catch (e) {
                            const err = e as Error;
                            callback({ error: 'unexpected exception decoding jwtToken: ' + err.message });
                            return;
                        }
                        if (jwtBody.aud.includes(expectedAudience) && jwtBody.iss === expectedIssuer) {
                            callback({});
                        } else {
                            callback({ error: `unexpected aud or exp ${jwtBody.aud} or aud value ${jwtBody.exp}` });
                        }
                    } else {
                        callback({ error: 'unexpected status code ' + res.statusCode });
                    }
                }
            });
        } else {
            callback({ error: 'malformed jwt: kid is not present' });
        }
    } catch (e) {
        callback({ error: 'unexpected exception: ' + e });
    }
}

export function startCheckingLockedQueries(sessionManager: SessionManager, logger: Logger) {
    logger = logger.getLogger({ subsystem: 'lockedQueries' });
    const intervalHandler = async function () {
        try {
            await sessionManager.withSession(logger, 'check lockedQueries', async ({ dbConnector }) => {
                const rows = await dbConnector.getLockedQueries();
                if (rows.length > 0) {
                    logger.info('Locked queries detected', { lockedQueries: rows });
                }
            });
        } catch (err) {
            logger.error('Unexpected error', err as Error);
        }
    };
    setInterval(intervalHandler, 60 * 1000);
}

export function isAtlassianNewLifeCycleActive(jwtToken: string) {
    try {
        let kid = jwtModule.getKeyId(jwtToken);
        return !!kid;
    } catch (e) {
        return false;
    }
}

export function isAnonymousUser(user: User) {
    try {
        // TODO: set userInfo type to unknown and perform the necessary checks
        const userInfo = JSON.parse(user.USERINFO ?? '{}');
        return userInfo && (userInfo.isAnonymous || userInfo.anonymous);
    } catch (e) {
        return false;
    }
}

export function getBASAuthorizationHeaderStringFactory(config: Config): (logger: Logger) => Promise<string> {
    const serverApiSecrets = config.getServerApiSecrets();
    return async (logger) => {
        const secrets = await serverApiSecrets.getSecret(logger);
        assert(
            secrets && typeof secrets === 'object' && 'getFirstSecret' in secrets && typeof secrets.getFirstSecret === 'function',
            'Invalid serverApiSecrets'
        );
        let secret;
        try {
            secret = secrets.getFirstSecret('bas');
        } catch (e) {
            logger.error('Error getting BAS secret', e as Error);
        }
        return secret ? 'Basic ' + Buffer.from('bas:' + secret).toString('base64') : '';
    };
}

// Inspiration from: https://stackoverflow.com/a/5827895
export function walk_directory(dir: string, done: (err: Error | null, results?: { filePath: string; size: number }[]) => void) {
    let results: { filePath: string; size: number }[] = [];
    fs.readdir(dir, function (err, list) {
        if (err) return done(err);
        let i = 0;
        (function next() {
            let file = list[i++];
            if (!file) return done(null, results);
            file = path.resolve(dir, file);
            fs.stat(file, function (err, stat) {
                if (err) {
                    done(err);
                } else {
                    if (stat && stat.isDirectory()) {
                        walk_directory(file, function (err, res) {
                            if (err) {
                                done(err);
                            } else {
                                if (res) {
                                    results = results.concat(res);
                                }
                                next();
                            }
                        });
                    } else {
                        results.push({
                            filePath: path.resolve(dir, file),
                            size: stat.size,
                        });
                        next();
                    }
                }
            });
        })();
    });
}

export function broadcastRTCMessage({
    logger,
    rtcAdapter,
    clock,
    channelId,
    message,
}: {
    logger: Logger;
    rtcAdapter: RtcAdapter;
    clock: Clock;
    channelId: string;
    message: Record<string, unknown>;
}) {
    logger.info('Broadcast message', { channelId, message });

    // TODO: Uncomment after the migration to typescript is complete
    // this is to keep track of the old broadcastRTCMessage function's behavior
    //
    // objToBroadcast.archiveRevision = archiveRevision;
    // objToBroadcast.author = internalUserID;
    // objToBroadcast.username = username;

    message.timestamp = clock.now();

    rtcAdapter.sendMessage(channelId, message);
}

export type WatchdogContentData = Record<
    string,
    {
        status: 'onprogress' | 'error' | 'success';
        error?: string | Error;
    }
>;

export function watchdog(contentData: WatchdogContentData, callback: BASLegacyCallback<{ status: 'completed' }>) {
    let i,
        key,
        keys = Object.keys(contentData),
        completed = 0,
        error = null;
    for (i = 0; i < keys.length; i++) {
        key = keys[i];
        if (contentData[key].error) {
            error = contentData[key].error;
            break;
        } else if (contentData[key].status === 'onprogress') {
            break;
        } else {
            // success
            completed++;
        }
    }

    if (completed === keys.length) {
        callback({ status: 'completed' });
    } else if (error) {
        callback({ error: error });
    } else {
        // retry later
        setTimeout(function () {
            watchdog(contentData, callback);
        }, 200);
    }
}

export function makePermalinkObject(permalinkData: PermalinkData, connector: Connector) {
    const { permalinkID, resourceID, branchID, dirty, permalinkKind, platformKind, timestamp, permalinkInfo } = permalinkData;
    const { image, edit, comment, view } = connector.getPermalinkExtraFromPermalinkData(permalinkData);
    return {
        resourceID,
        branchID,
        permalinkID,
        permalinkKind,
        platformKind,
        permalinkInfo,
        dirty,
        timestamp,
        image,
        comment,
        edit,
        view,
    };
}

export async function getPermalinksDataHelper({
    siteId,
    projectId,
    kind,
    dbConnector,
    connector,
}: {
    siteId: string;
    projectId: string;
    kind: string;
    dbConnector: DBConnector;
    connector: Connector;
}) {
    const platformSiteID = siteId;
    const platformArchiveID = projectId;
    const platformKind = kind;

    const { permalinksData } = await callWithLegacyCallback<{ permalinksData: PermalinkData[] }>((cb) =>
        dbConnector.getPermalinksDataFromArchiveID(platformKind, platformSiteID, platformArchiveID, cb)
    );

    return {
        permalinksData: permalinksData.map((permalinkData) => {
            return makePermalinkObject(permalinkData, connector);
        }),
    };
}

export function getKindFromArchiveID(archiveID: string, archiveIDPrefix: string) {
    let tmp = archiveID.substring(archiveIDPrefix.length).split('_');
    return tmp[0];
}

export async function createOrUpdateImageUnfurling({
    resourceInfo,
    platformKind,
    platformSiteID,
    platformArchiveID,
    resourceID,
    branchID,
    dbConnector,
    permalinkInfo,
    platformInfo,
    connector,
    logger,
    clock,
}: {
    resourceInfo: { resourceID: string; branchID: string; name: string | null; projectName: string; thumbnail?: string };
    platformKind: string;
    platformSiteID: string;
    platformArchiveID: string;
    resourceID: string;
    branchID: string;
    dbConnector: DBConnector;
    permalinkInfo: PermalinkInfo;
    platformInfo: Record<string, unknown>;
    connector: Connector;
    logger: Logger;
    clock: Clock;
}) {
    const permalinkID = connector.makePermalinkID();
    const [permalinkData, created] = await dbConnector.insertOrUpdatePermalink({
        permalinkID,
        platformKind,
        platformSiteID,
        platformArchiveID,
        resourceID,
        branchID,
        dirty: false,
        permalinkKind: Consts.PermalinkKind.image_unfurling,
        permalinkInfo,
        platformInfo,
        timestamp: clock.now(),
    });

    //lazy image generation
    connector.generatePermalinkImage({ logger, permalinkData }).catch((err) => {
        logger.error(`Unexpected error in connector.generatePermalinkImage: ${err.message}`, err);
    });

    const permalinkObject = makePermalinkObject(permalinkData, connector);

    // in case of first creation, forcing the client to generate the image through the query parameter
    if (permalinkObject.image) {
        if (created === 1) {
            permalinkObject.image += '?q=thumbnail-only';
        } else {
            const extensionMatch = permalinkObject.image.match(/(\.\w+)$/);
            if (extensionMatch) {
                permalinkObject.image = permalinkObject.image.replace(/(\.\w+)$/, '_thumbnail$1');
            }
        }
    }

    return {
        ...permalinkObject,
        projectName: resourceInfo.projectName,
        name: resourceInfo.name,
    };
}

// Explanation and use case
// ------------------------
// Gardening jobs run at fixed intervals and are supposed to be singletons.
// If a specific execution is longer than the said interval, the following
// run will overlap the old one, thus violating the singleton property.
// In order to avoid this scenario, a simple detection mechanism is used,
// based on MySQL global named-locks. A new connection is created and held
// for the whole duration of the job execution. A new starting job will
// try to acquire the lock in a non-blocking way, and will fail if another
// task (holding the same named-lock) is still running. Therefore will
// voluntarily quit in order not to overlap executions.
// A new connection is needed because global locks are automatically
// released upon every successfully committed transaction. Therefore
// regular connections cannot be used, because they initiate and commit
// several transactions.
// Explicitly releasing the lock is not necessary, and indeed explicitly
// avoided, because simply quitting the process will terminate the DB
// connection, thus implicitly releasing the lock, according to the
// semantics of GET_LOCK (see MySQL's documentation). In fact, this
// semantics plays in our favour because even if the process terminates
// abnormally, the lock is still implicitly released, thus allowing the
// following scheduled task to correctly execute.
export function acquireApplicationLockOnNewConnection(
    mySqlDriverInstance: MySQLDriverPool,
    lockName: ApplicationLockName,
    cb: BASLegacyCallback<{ success: true }>
) {
    mySqlDriverInstance.getConnection(function (obj) {
        if (isBASLegacyErrorObject(obj)) {
            cb({ error: 'Cannot acquire DB connection' });
        } else {
            acquireApplicationLock(obj, lockName, false, function (obj) {
                if (isBASLegacyErrorObject(obj)) {
                    cb({ error: `Cannot acquire application lock ${lockName}` });
                } else {
                    if (!obj.lockAcquired) {
                        cb({ error: `Another process is holding the ${lockName} application lock` });
                    } else {
                        cb({ success: true });
                    }
                }
            });
        }
    });
}

export function parseMultipartFormData(
    metrics: Metrics,
    logger: Logger,
    req: Request,
    res: Response,
    callback: (params: Record<string, string>, uploadedFileBuffer: Buffer | null) => void
) {
    let bb = busboy({ headers: req.headers });
    let errorOccurred = false;
    let params: Record<string, string> = {};
    let fileDataChunks: Buffer[] = [];

    let startParsing = Date.now();
    bb.on('file', function (fieldname, file, info) {
        const { encoding } = info;
        file.on('data', function (data) {
            fileDataChunks.push(Buffer.from(data, encoding as BufferEncoding));
        });
        file.on('error', function (err) {
            if (!errorOccurred) {
                // Do not terminate the request more than once
                logger.error('Error reading file from request body: ' + err.message, err, { action: 'create upload' });
                errorOccurred = true;
                res.status(400).send('Error processing the request');
            }
        });
    });
    bb.on('field', function (fieldname, val) {
        params[fieldname] = val;
    });
    bb.on('close', function () {
        if (!errorOccurred) {
            metrics.addValue('upload-parseTime', Date.now() - startParsing, 'Milliseconds');
            (req as any).startProcessing = Date.now(); // TODO: avoid injecting into the request object
            callback(params, fileDataChunks.length > 0 ? Buffer.concat(fileDataChunks) : null);
        }
    });
    bb.on('error', function (err) {
        if (!errorOccurred) {
            // Do not terminate the request more than once
            const error = err as Error;
            logger.error('Error parsing the request body: ' + error.message, error, { action: 'create upload' });
            errorOccurred = true;
            res.status(400).send('Error processing the request');
        }
    });
    req.pipe(bb).on('error', function (err) {
        if (!errorOccurred) {
            // Do not terminate the request more than once
            const error = err as Error;
            logger.error('Error on piping data: ' + error.message, error, { action: 'create upload' });
            errorOccurred = true;
            res.status(400).send('Error processing the request');
        }
    });
}

export async function getResourceNameAndIDs({
    config,
    sessionManager,
    dbConnector,
    connector,
    sessionData,
    platformKind,
    platformSiteID,
    platformArchiveID,
    platformInfo,
    resourceID,
    branchID,
    logger,
}: {
    config: Config;
    sessionManager: SessionManager;
    dbConnector: DBConnector;
    connector: Connector;
    sessionData: SessionData;
    platformKind: string;
    platformSiteID: string;
    platformArchiveID: string;
    platformInfo: Record<string, unknown>;
    resourceID: string;
    branchID: string;
    logger: Logger;
}) {
    async function _getResourceInfoFromConnector({
        platformKind,
        platformSiteID,
        platformArchiveID,
        platformInfo,
        resourceID,
        branchID,
    }: {
        platformKind: string;
        platformSiteID: string;
        platformArchiveID: string;
        platformInfo: Record<string, unknown>;
        resourceID: string;
        branchID: string;
    }) {
        const authInfo = await callWithLegacyCallback<{ platformToken: string | null }>((cb) =>
            connector.getAuthTokenFromPlatform(logger, platformInfo, cb)
        );
        const platformToken = authInfo.platformToken ?? '';

        const archive = await callWithLegacyCallback<{ id: string; buffer: Buffer; platformInfo?: Record<string, unknown> }>((cb) =>
            connector.loadFromPlatform(logger, dbConnector, platformToken, platformSiteID, platformArchiveID, null, platformInfo, cb)
        );
        const archiveID = archive.id;
        const buffer = archive.buffer;

        platformInfo = archive.platformInfo || platformInfo; // update the platformInfo if changed by the loadFromPlatform
        let bar: BalsamiqArchive;
        try {
            bar = await callWithLegacyCallback<BalsamiqArchive>((cb) =>
                createBARFromBuffer(
                    archiveID,
                    buffer,
                    sessionData,
                    platformInfo,
                    platformKind,
                    platformSiteID,
                    platformArchiveID,
                    null,
                    dbConnector,
                    logger,
                    config,
                    sessionManager,
                    cb
                )
            );
        } catch (error) {
            if (error instanceof BASCallbackError && error?.resultObj?.busy) {
                // The archive has just been loaded by another (likely concurrent) request.
                return await _getResourceInfoFromBAS({ archiveID, platformSiteID, platformArchiveID, resourceID, branchID });
            }
            throw error;
        }
        return _getResourceInfo(bar.dump, resourceID, branchID);
    }

    async function _getResourceInfoFromBAS({
        archiveID,
        platformSiteID,
        platformArchiveID,
        resourceID,
        branchID,
    }: {
        archiveID: string;
        platformSiteID: string;
        platformArchiveID: string;
        resourceID: string;
        branchID: string;
    }) {
        logger.info(
            `permalink accessing an already opened basArchiveID:${archiveID} platformArchiveID:${platformArchiveID} platformSiteID: ${platformSiteID}`
        );

        try {
            const archive = await callWithLegacyCallback<{ bar: BalsamiqArchive }>((cb) =>
                sessionManager.openBarLocked(sessionData, archiveID, 'READ', cb)
            );
            const bar = archive.bar;
            const { dump } = await callWithLegacyCallback<BmprDump>((cb) => bar.getToc(Consts.Branch.AllBranches, cb));
            return _getResourceInfo(dump, resourceID, branchID);
        } finally {
            await callWithLegacyCallback((cb) => sessionManager.unlockConnection(sessionData, cb));
        }
    }

    function _getResourceInfo(dump: BmprDump, resourceID: string, branchID: string) {
        let ret: {
            resourceInfo?: {
                resourceID: string;
                branchID: string;
                name: string | null;
                projectName: string;
                thumbnail?: string;
            };
        } = {};
        if (dump && dump.Resources && Array.isArray(dump.Resources) && dump.Resources.length > 0) {
            const projectName = dump.Info.ArchiveAttributes.name;

            if (!resourceID) {
                const resources = dump.Resources.slice()
                    .filter((element: any) => {
                        return !element.ATTRIBUTES.trashed && element.ATTRIBUTES.kind === 'mockup';
                    })
                    .sort(function (a: any, b: any) {
                        return a.ATTRIBUTES.order - b.ATTRIBUTES.order;
                    });

                ret =
                    resources.length > 0
                        ? {
                              resourceInfo: {
                                  resourceID: resources[0].ID,
                                  branchID: resources[0].BRANCHID,
                                  name: resources[0].ATTRIBUTES.name,
                                  projectName,
                              },
                          }
                        : {};
            } else {
                if (!branchID) {
                    branchID = Consts.Branch.MasterBranchID;
                }
                const resource = dump.Resources.find((resource: any) => {
                    if (branchID !== Consts.Branch.MasterBranchID) {
                        return (
                            !resource.ATTRIBUTES.trashed &&
                            resource.ID.indexOf(resourceID) === 0 &&
                            resource.BRANCHID.indexOf(branchID) === 0
                        );
                    } else {
                        return (
                            !resource.ATTRIBUTES.trashed &&
                            resource.ATTRIBUTES.kind === 'mockup' &&
                            resource.ID.indexOf(resourceID) === 0 &&
                            resource.BRANCHID.indexOf(branchID) === 0
                        );
                    }
                });

                let name = resource ? resource.ATTRIBUTES.name : null;
                if (resource && !name) {
                    const resourceMaster = dump.Resources.find((resource: any) => {
                        return resource.ID.indexOf(resourceID) === 0 && resource.BRANCHID.indexOf(Consts.Branch.MasterBranchID) === 0;
                    });
                    if (resourceMaster && resourceMaster.ATTRIBUTES.name) {
                        name = resourceMaster.ATTRIBUTES.name;
                        if (branchID !== Consts.Branch.MasterBranchID) {
                            const branch = dump.Branch.find((branch: any) => {
                                return branch.branchID.indexOf(branchID) === 0;
                            });

                            if (branch && branch.ATTRIBUTES.branchName) {
                                name += ' (' + branch.ATTRIBUTES.branchName + ')';
                            }
                        }
                    }
                }
                ret = resource
                    ? {
                          resourceInfo: {
                              resourceID: resource.ID,
                              branchID: resource.BRANCHID,
                              name: name,
                              projectName,
                          },
                      }
                    : {};
            }
        }

        if (ret && ret.resourceInfo && ret.resourceInfo.resourceID && ret.resourceInfo.branchID && dump.Thumbnails) {
            const thumbnail = dump.Thumbnails.find((thumbnail: any) => {
                return (
                    thumbnail.ATTRIBUTES.resourceID.indexOf(ret?.resourceInfo?.resourceID) === 0 &&
                    thumbnail.ATTRIBUTES.branchID.indexOf(ret?.resourceInfo?.branchID) === 0
                );
            });

            if (thumbnail && thumbnail.ATTRIBUTES && thumbnail.ATTRIBUTES.image) {
                ret.resourceInfo['thumbnail'] = thumbnail.ATTRIBUTES.image;
            }
        }

        return ret;
    }

    const archiveInfo = await callWithLegacyCallback<PlatformData | {}>((cb) =>
        dbConnector.getBASArchiveIDWithExclusiveRowLock(platformSiteID, platformArchiveID, true, cb)
    );

    if ('BAS_ARCHIVE_ID' in archiveInfo) {
        const archiveID = archiveInfo.BAS_ARCHIVE_ID;
        return await _getResourceInfoFromBAS({ archiveID, platformSiteID, platformArchiveID, resourceID, branchID });
    } else {
        // Retrieve the archive id from connector
        return await _getResourceInfoFromConnector({ platformKind, platformSiteID, platformArchiveID, platformInfo, resourceID, branchID });
    }
}

export function createBARFromBuffer(
    archiveID: string,
    buffer: Buffer,
    sessionData: SessionData,
    platformInfo: Record<string, unknown> | null | undefined,
    kind: string,
    platformSiteID: string,
    platformArchiveID: string,
    platformArchiveName: string | null,
    dbConnector: DBConnector,
    logger: Logger,
    config: Config,
    sessionManager: SessionManager,
    callback: BASLegacyCallback<{ dump: BmprDump; heuristicArchiveSize: number }>
) {
    sqliteBuffer2object(config.archivesPath, buffer, kind, function (obj) {
        if (isBASLegacyErrorObject(obj)) {
            callback(obj);
            return;
        }
        const dump: BmprDump = obj.dump;
        const bar = sessionManager.getBar(sessionData);
        logger.info('creating the archive ' + archiveID);
        // we do not need LOCK, archiveID is unique and PLATFORM_DATA is not yet saved
        bar.createFromDump(archiveID, dump, function (obj: unknown) {
            if (isBASLegacyErrorObject(obj)) {
                callback(obj);
                return;
            }

            bar.getHeuristicArchiveSize(function (objSize: any) {
                let heuristicArchiveSize: number;
                if (isBASLegacyErrorObject(objSize)) {
                    logger.error('error calculating archive heuristic size: ' + objSize.error);
                } else {
                    heuristicArchiveSize = objSize.heuristicArchiveSize;
                    logger.info('archive heuristic size: ' + objSize.heuristicArchiveSize, { heuristicArchiveSize });
                }

                // we save the current revision on load, it will be useful on saving
                if (!platformInfo) {
                    platformInfo = {};
                }
                if (dump.migrated) {
                    // force the archive to be flushed
                    logger.info('archive migrated to 2.0, forcing the flush');
                    platformInfo = {
                        ...platformInfo,
                        archiveRevisionOnPlatform: dump.Info.ArchiveRevision - 1,
                    };
                } else {
                    platformInfo = {
                        ...platformInfo,
                        archiveRevisionOnPlatform: dump.Info.ArchiveRevision,
                    };
                }
                logger.info('saving platform info for archive ' + archiveID, { platformInfo });
                dbConnector.saveArchivePlatformData(
                    archiveID,
                    kind,
                    platformSiteID,
                    platformArchiveID,
                    platformArchiveName,
                    platformInfo,
                    function (obj) {
                        if (isBASLegacyErrorObject(obj)) {
                            callback(obj);
                            return;
                        }
                        callback({ dump: dump, heuristicArchiveSize: heuristicArchiveSize });
                    }
                );
            });
        });
    });
}
export type HttpResponse<T = unknown> = {
    status: number;
    body?: T;
    headers?: Record<string, string>;
    text?: string;
};
