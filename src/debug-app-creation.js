// Quick debug script to test app creation
import { createApp } from './app.js';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function testAppCreation() {
    console.log('Starting app creation test...');

    try {
        // Create a minimal mock context
        const mockContext = {
            config: { port: 3000, proxyConfig: [] },
            metrics: { addValue: () => {} },
            sessionManager: {},
            connectors: { jira: {}, confluence: {}, webDemo: {}, cloud: {} },
            getConnector: () => null,
            clock: {},
            roleForAccessTable: {},
            bmprUtilsMod: {},
            serverUtils: {},
            rateLimiterConfiguration: {},
            blockList: {},
            redisAdapter: {},
            buildNumberTracker: { asyncMiddleware: async () => {} },
            srcDirName: __dirname,
        };

        console.log('Creating app...');
        const app = createApp(mockContext);
        console.log('App created successfully!');

    } catch (error) {
        console.error('Error during app creation:', error);
        console.error('Stack trace:', error.stack);
    }
}

testAppCreation();
